try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="785674ca-db22-4737-a010-5e208e082ce4",e._sentryDebugIdIdentifier="sentry-dbid-785674ca-db22-4737-a010-5e208e082ce4")}catch(e){}(()=>{var e={};e.id=1087,e.ids=[1087],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3781:(e,t,r)=>{"use strict";r.d(t,{TreatmentsList:()=>s});let s=(0,r(91611).registerClientReference)(function(){throw Error("Attempted to call TreatmentsList() from the server but TreatmentsList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\treatments\\treatments-list.tsx","TreatmentsList")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},26882:(e,t,r)=>{"use strict";r.d(t,{C5:()=>g,MJ:()=>h,Rr:()=>y,eI:()=>p,lR:()=>f,lV:()=>l,zB:()=>m});var s=r(24443),n=r(60222),a=r(16586),i=r(95550),o=r(72595),d=r(18984);let l=i.Op,c=n.createContext({}),m=({...e})=>(0,s.jsx)(c.Provider,{value:{name:e.name},"data-sentry-element":"FormFieldContext.Provider","data-sentry-component":"FormField","data-sentry-source-file":"form.tsx",children:(0,s.jsx)(i.xI,{...e,"data-sentry-element":"Controller","data-sentry-source-file":"form.tsx"})}),u=()=>{let e=n.useContext(c),t=n.useContext(x),{getFieldState:r}=(0,i.xW)(),s=(0,i.lN)({name:e.name}),a=r(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:`${o}-form-item`,formDescriptionId:`${o}-form-item-description`,formMessageId:`${o}-form-item-message`,...a}},x=n.createContext({});function p({className:e,...t}){let r=n.useId();return(0,s.jsx)(x.Provider,{value:{id:r},"data-sentry-element":"FormItemContext.Provider","data-sentry-component":"FormItem","data-sentry-source-file":"form.tsx",children:(0,s.jsx)("div",{"data-slot":"form-item",className:(0,o.cn)("grid gap-2",e),...t})})}function f({className:e,...t}){let{error:r,formItemId:n}=u();return(0,s.jsx)(d.J,{"data-slot":"form-label","data-error":!!r,className:(0,o.cn)("data-[error=true]:text-destructive",e),htmlFor:n,...t,"data-sentry-element":"Label","data-sentry-component":"FormLabel","data-sentry-source-file":"form.tsx"})}function h({...e}){let{error:t,formItemId:r,formDescriptionId:n,formMessageId:i}=u();return(0,s.jsx)(a.DX,{"data-slot":"form-control",id:r,"aria-describedby":t?`${n} ${i}`:`${n}`,"aria-invalid":!!t,...e,"data-sentry-element":"Slot","data-sentry-component":"FormControl","data-sentry-source-file":"form.tsx"})}function y({className:e,...t}){let{formDescriptionId:r}=u();return(0,s.jsx)("p",{"data-slot":"form-description",id:r,className:(0,o.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-component":"FormDescription","data-sentry-source-file":"form.tsx"})}function g({className:e,...t}){let{error:r,formMessageId:n}=u(),a=r?String(r?.message??""):t.children;return a?(0,s.jsx)("p",{"data-slot":"form-message",id:n,className:(0,o.cn)("text-destructive text-sm",e),...t,"data-sentry-component":"FormMessage","data-sentry-source-file":"form.tsx",children:a}):null}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},32690:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.default,__next_app__:()=>c,pages:()=>l,routeModule:()=>m,tree:()=>d});var s=r(29703),n=r(85544),a=r(62458),i=r(77821),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let d={children:["",{children:["dashboard",{children:["treatments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,73509)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\treatments\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,56164)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3259))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,69549)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,62458)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,8036)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,11103,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,13780,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3259))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\treatments\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/dashboard/treatments/page",pathname:"/dashboard/treatments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41634:(e,t,r)=>{"use strict";r.d(t,{K:()=>a});var s=r(24443),n=r(75922);function a({open:e,onOpenChange:t,title:r,description:a,confirmText:i="确认",cancelText:o="取消",variant:d="default",onConfirm:l,loading:c=!1}){return(0,s.jsx)(n.Lt,{open:e,onOpenChange:t,"data-sentry-element":"AlertDialog","data-sentry-component":"ConfirmationDialog","data-sentry-source-file":"confirmation-dialog.tsx",children:(0,s.jsxs)(n.EO,{"data-sentry-element":"AlertDialogContent","data-sentry-source-file":"confirmation-dialog.tsx",children:[(0,s.jsxs)(n.wd,{"data-sentry-element":"AlertDialogHeader","data-sentry-source-file":"confirmation-dialog.tsx",children:[(0,s.jsx)(n.r7,{"data-sentry-element":"AlertDialogTitle","data-sentry-source-file":"confirmation-dialog.tsx",children:r}),(0,s.jsx)(n.$v,{"data-sentry-element":"AlertDialogDescription","data-sentry-source-file":"confirmation-dialog.tsx",children:a})]}),(0,s.jsxs)(n.ck,{"data-sentry-element":"AlertDialogFooter","data-sentry-source-file":"confirmation-dialog.tsx",children:[(0,s.jsx)(n.Zr,{disabled:c,"data-sentry-element":"AlertDialogCancel","data-sentry-source-file":"confirmation-dialog.tsx",children:o}),(0,s.jsx)(n.Rx,{onClick:l,disabled:c,className:"destructive"===d?"bg-destructive text-destructive-foreground hover:bg-destructive/90":"","data-sentry-element":"AlertDialogAction","data-sentry-source-file":"confirmation-dialog.tsx",children:c?"加载中...":i})]})]})})}},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54607:(e,t,r)=>{Promise.resolve().then(r.bind(r,3781)),Promise.resolve().then(r.bind(r,89371))},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},62855:(e,t,r)=>{Promise.resolve().then(r.bind(r,86389)),Promise.resolve().then(r.bind(r,67529))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73509:(e,t,r)=>{"use strict";let s;r.r(t),r.d(t,{default:()=>j,generateImageMetadata:()=>y,generateMetadata:()=>h,generateViewport:()=>g});var n=r(63033),a=r(78869),i=r(79615),o=r(44508),d=r(83829),l=(0,r(19e3).A)("outline","stethoscope","IconStethoscope",[["path",{d:"M6 4h-1a2 2 0 0 0 -2 2v3.5h0a5.5 5.5 0 0 0 11 0v-3.5a2 2 0 0 0 -2 -2h-1",key:"svg-0"}],["path",{d:"M8 15a6 6 0 1 0 12 0v-3",key:"svg-1"}],["path",{d:"M11 3v2",key:"svg-2"}],["path",{d:"M6 3v2",key:"svg-3"}],["path",{d:"M20 10m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0",key:"svg-4"}]]),c=r(3781),m=r(84758),u=r(19761);async function x(){let{userId:e}=await (0,i.j)();return e?(0,a.jsx)(d.A,{"data-sentry-element":"PageContainer","data-sentry-component":"TreatmentsPage","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)("div",{className:"flex flex-1 flex-col space-y-4",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold tracking-tight flex items-center gap-2",children:[(0,a.jsx)(l,{className:"size-6","data-sentry-element":"IconStethoscope","data-sentry-source-file":"page.tsx"}),(0,m.t)("treatments.title")]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:(0,m.t)("treatments.subtitle")})]})}),(0,a.jsx)(c.TreatmentsList,{"data-sentry-element":"TreatmentsList","data-sentry-source-file":"page.tsx"})]})}):(0,o.redirect)("/auth/sign-in")}let p={...n},f="workUnitAsyncStorage"in p?p.workUnitAsyncStorage:"requestAsyncStorage"in p?p.requestAsyncStorage:void 0;s=new Proxy(x,{apply:(e,t,r)=>{let s,n,a;try{let e=f?.getStore();s=e?.headers.get("sentry-trace")??void 0,n=e?.headers.get("baggage")??void 0,a=e?.headers}catch(e){}return u.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/treatments",componentType:"Page",sentryTraceHeader:s,baggageHeader:n,headers:a}).apply(t,r)}});let h=void 0,y=void 0,g=void 0,j=s},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},81646:(e,t,r)=>{"use strict";r.d(t,{T:()=>a});var s=r(24443);r(60222);var n=r(72595);function a({className:e,...t}){return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,n.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t,"data-sentry-component":"Textarea","data-sentry-source-file":"textarea.tsx"})}},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86389:(e,t,r)=>{"use strict";r.d(t,{TreatmentsList:()=>q});var s=r(24443),n=r(60222),a=r(33284),i=r(74482),o=r(92113),d=r(20866),l=r(49958),c=r(9752),m=r(57602),u=r(95550),x=r(80395),p=r(13875),f=r(61780),h=r(26882),y=r(19342),g=r(81646),j=r(85001),v=r(32300);let b=p.Ik({name:p.Yj().min(2,"治疗名称至少需要2个字符").max(100,"治疗名称不能超过100个字符").regex(/^[a-zA-Z0-9\s\-&()]+$/,"治疗名称只能包含字母、数字、空格、连字符和括号"),description:p.Yj().max(1e3,"描述不能超过1000个字符").optional(),defaultPrice:p.ai().min(0,"价格必须为0元或更高").max(1e4,"价格不能超过10,000元").multipleOf(.01,"价格必须是有效的货币金额（例如：99.99）"),defaultDurationInMinutes:p.ai().min(5,"时长至少需要5分钟").max(480,"时长不能超过8小时（480分钟）").int("时长必须是整数分钟")});function C({open:e,onOpenChange:t,treatment:r,onSuccess:i}){let[o,d]=(0,n.useState)(!1),l=!!r,c=(0,u.mN)({resolver:(0,x.u)(b),defaultValues:{name:r?.name||"",description:r?.description||"",defaultPrice:r?.defaultPrice||0,defaultDurationInMinutes:r?.defaultDurationInMinutes||30}}),p=async e=>{d(!0);try{let s={name:e.name,description:e.description||void 0,defaultPrice:e.defaultPrice,defaultDurationInMinutes:e.defaultDurationInMinutes};l?(await m._M.update(r.id,s),j.toast.success("Treatment updated successfully")):(await m._M.create(s),j.toast.success("Treatment created successfully")),i?.(),t(!1),c.reset()}catch(e){console.error("Failed to save treatment:",e),j.toast.error(`Failed to ${l?"update":"create"} treatment`)}finally{d(!1)}};return(0,s.jsx)(f.lG,{open:e,onOpenChange:t,"data-sentry-element":"Dialog","data-sentry-component":"TreatmentFormDialog","data-sentry-source-file":"treatment-form-dialog.tsx",children:(0,s.jsxs)(f.Cf,{className:"sm:max-w-[500px]","data-sentry-element":"DialogContent","data-sentry-source-file":"treatment-form-dialog.tsx",children:[(0,s.jsxs)(f.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"treatment-form-dialog.tsx",children:[(0,s.jsx)(f.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"treatment-form-dialog.tsx",children:l?(0,v.t)("treatments.editTreatment"):(0,v.t)("treatments.newTreatment")}),(0,s.jsx)(f.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"treatment-form-dialog.tsx",children:l?"更新下方的治疗信息。":"填写治疗详细信息以创建新服务。"})]}),(0,s.jsx)(h.lV,{...c,"data-sentry-element":"Form","data-sentry-source-file":"treatment-form-dialog.tsx",children:(0,s.jsxs)("form",{onSubmit:c.handleSubmit(p),className:"space-y-4",children:[(0,s.jsx)(h.zB,{control:c.control,name:"name",render:({field:e})=>(0,s.jsxs)(h.eI,{children:[(0,s.jsxs)(h.lR,{children:[(0,v.t)("treatments.form.name")," *"]}),(0,s.jsx)(h.MJ,{children:(0,s.jsx)(y.p,{placeholder:(0,v.t)("treatments.form.namePlaceholder"),...e})}),(0,s.jsx)(h.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"treatment-form-dialog.tsx"}),(0,s.jsx)(h.zB,{control:c.control,name:"description",render:({field:e})=>(0,s.jsxs)(h.eI,{children:[(0,s.jsx)(h.lR,{children:(0,v.t)("treatments.form.description")}),(0,s.jsx)(h.MJ,{children:(0,s.jsx)(g.T,{placeholder:(0,v.t)("treatments.form.descriptionPlaceholder"),className:"min-h-[80px]",...e})}),(0,s.jsx)(h.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"treatment-form-dialog.tsx"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsx)(h.zB,{control:c.control,name:"defaultPrice",render:({field:e})=>(0,s.jsxs)(h.eI,{children:[(0,s.jsxs)(h.lR,{children:[(0,v.t)("treatments.form.price")," (\xa5) *"]}),(0,s.jsx)(h.MJ,{children:(0,s.jsx)(y.p,{type:"number",step:"0.01",placeholder:(0,v.t)("treatments.form.pricePlaceholder"),...e,onChange:t=>e.onChange(parseFloat(t.target.value)||0)})}),(0,s.jsx)(h.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"treatment-form-dialog.tsx"}),(0,s.jsx)(h.zB,{control:c.control,name:"defaultDurationInMinutes",render:({field:e})=>(0,s.jsxs)(h.eI,{children:[(0,s.jsxs)(h.lR,{children:[(0,v.t)("treatments.form.duration")," (分钟) *"]}),(0,s.jsx)(h.MJ,{children:(0,s.jsx)(y.p,{type:"number",placeholder:(0,v.t)("treatments.form.durationPlaceholder"),...e,onChange:t=>e.onChange(parseInt(t.target.value)||0)})}),(0,s.jsx)(h.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"treatment-form-dialog.tsx"})]}),(0,s.jsxs)(f.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"treatment-form-dialog.tsx",children:[(0,s.jsx)(a.$,{type:"button",variant:"outline",onClick:()=>t(!1),disabled:o,"data-sentry-element":"Button","data-sentry-source-file":"treatment-form-dialog.tsx",children:(0,v.t)("common.actions.cancel")}),(0,s.jsx)(a.$,{type:"submit",disabled:o,"data-sentry-element":"Button","data-sentry-source-file":"treatment-form-dialog.tsx",children:o?"保存中...":l?"更新治疗":"创建治疗"})]})]})})]})})}var N=r(41634),w=r(11035);function q(){let{hasPermission:e}=(0,i.It)(),[t,r]=(0,n.useState)([]),[u,x]=(0,n.useState)(!0),[p,f]=(0,n.useState)(null),[h,y]=(0,n.useState)(!1),[g,b]=(0,n.useState)(),[q,P]=(0,n.useState)(!1),[D,k]=(0,n.useState)(),[F,I]=(0,n.useState)(!1),_=async()=>{try{x(!0),f(null);let e=await m._M.getAll({limit:100});r(e.docs)}catch(e){console.error("Failed to fetch treatments:",e),f("Failed to load treatments. Please try again later.")}finally{x(!1)}},T=()=>{b(void 0),y(!0)},M=e=>{b(e),y(!0)},A=async()=>{if(D){I(!0);try{await m._M.delete(D.id),j.toast.success("Treatment deleted successfully"),P(!1),k(void 0),_()}catch(e){console.error("Failed to delete treatment:",e),j.toast.error("Failed to delete treatment")}finally{I(!1)}}},S=()=>{y(!1),b(void 0),_()};return u?(0,s.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:(0,v.t)("treatments.loadingTreatments")})]})}):p?(0,s.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-red-600 mb-4",children:p}),(0,s.jsx)(a.$,{onClick:()=>window.location.reload(),variant:"outline",children:"重试"})]})}):0===t.length?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(o.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium mb-2",children:(0,v.t)("treatments.noTreatments")}),(0,s.jsx)("p",{className:"text-muted-foreground mb-4",children:"开始添加您的第一个治疗项目。"}),(0,s.jsx)(i.Bk,{permission:"canCreateTreatments",children:(0,s.jsxs)(a.$,{onClick:T,children:[(0,s.jsx)(d.A,{className:"h-4 w-4 mr-2"}),(0,v.t)("treatments.newTreatment")]})})]})}),(0,s.jsx)(C,{open:h,onOpenChange:y,treatment:g,onSuccess:S})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("div",{children:(0,s.jsxs)("h3",{className:"text-lg font-medium",children:[t.length," ",(0,v.t)("treatments.treatmentsCount")]})}),(0,s.jsx)(i.Bk,{permission:"canCreateTreatments","data-sentry-element":"PermissionGate","data-sentry-source-file":"treatments-list.tsx",children:(0,s.jsxs)(a.$,{onClick:T,"data-sentry-element":"Button","data-sentry-source-file":"treatments-list.tsx",children:[(0,s.jsx)(d.A,{className:"h-4 w-4 mr-2","data-sentry-element":"IconPlus","data-sentry-source-file":"treatments-list.tsx"}),(0,v.t)("treatments.newTreatment")]})})]}),(0,s.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:t.map(e=>(0,s.jsxs)("div",{className:"border rounded-lg p-4 space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("h4",{className:"font-medium text-lg",children:e.name}),e.description&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-2",children:e.description})]}),(0,s.jsx)(i.Bk,{permission:"canEditTreatments",children:(0,s.jsxs)(w.rI,{children:[(0,s.jsx)(w.ty,{asChild:!0,children:(0,s.jsx)(a.$,{variant:"ghost",size:"sm",children:(0,s.jsx)(l.A,{className:"h-4 w-4"})})}),(0,s.jsxs)(w.SQ,{children:[(0,s.jsxs)(w._2,{onClick:()=>M(e),children:[(0,s.jsx)(l.A,{className:"h-4 w-4 mr-2"}),(0,v.t)("common.actions.edit")]}),(0,s.jsxs)(w._2,{onClick:()=>{k(e),P(!0)},className:"text-red-600",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 mr-2"}),(0,v.t)("common.actions.delete")]})]})]})})]}),(0,s.jsx)("div",{className:"flex items-center justify-between text-sm",children:(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"价格:"}),(0,s.jsxs)("span",{className:"font-medium",children:["\xa5",e.defaultPrice?.toFixed(2)||"未设置"]})]}),e.defaultDurationInMinutes&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"时长:"}),(0,s.jsxs)("span",{children:[e.defaultDurationInMinutes," 分钟"]})]})]})})]},e.id))})]}),(0,s.jsx)(C,{open:h,onOpenChange:y,treatment:g,onSuccess:S,"data-sentry-element":"TreatmentFormDialog","data-sentry-source-file":"treatments-list.tsx"}),(0,s.jsx)(N.K,{open:q,onOpenChange:P,title:"删除治疗项目",description:`您确定要删除"${D?.name}"吗？此操作无法撤销。`,confirmText:"删除",variant:"destructive",onConfirm:A,loading:F,"data-sentry-element":"ConfirmationDialog","data-sentry-source-file":"treatments-list.tsx"})]})}},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[55,3738,1950,5886,9615,7927,6451,5618,2584,9616,4144,4889,3875,395,8774,7494,6273,490],()=>r(32690));module.exports=s})();
//# sourceMappingURL=page.js.map