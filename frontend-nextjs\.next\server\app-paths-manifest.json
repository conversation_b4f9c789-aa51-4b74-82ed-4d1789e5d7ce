{"/api/bill-items/route": "app/api/bill-items/route.js", "/api/bill-items/[id]/route": "app/api/bill-items/[id]/route.js", "/api/bills/generate-from-appointment/route": "app/api/bills/generate-from-appointment/route.js", "/api/bills/[id]/route": "app/api/bills/[id]/route.js", "/api/bills/route": "app/api/bills/route.js", "/api/payments/[id]/route": "app/api/payments/[id]/route.js", "/api/payments/route": "app/api/payments/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/api/appointments/[id]/route": "app/api/appointments/[id]/route.js", "/api/appointments/route": "app/api/appointments/route.js", "/api/auth/sync/route": "app/api/auth/sync/route.js", "/api/patient-interactions/route": "app/api/patient-interactions/route.js", "/api/patient-interactions/[id]/route": "app/api/patient-interactions/[id]/route.js", "/api/patient-tasks/[id]/route": "app/api/patient-tasks/[id]/route.js", "/api/patient-tasks/route": "app/api/patient-tasks/route.js", "/api/patients/[id]/interactions/route": "app/api/patients/[id]/interactions/route.js", "/api/patients/[id]/route": "app/api/patients/[id]/route.js", "/api/patients/[id]/tasks/route": "app/api/patients/[id]/tasks/route.js", "/api/treatments/[id]/route": "app/api/treatments/[id]/route.js", "/api/patients/route": "app/api/patients/route.js", "/api/patients/[id]/timeline/route": "app/api/patients/[id]/timeline/route.js", "/api/treatments/route": "app/api/treatments/route.js", "/_not-found/page": "app/_not-found/page.js", "/page": "app/page.js", "/auth/sign-up/[[...sign-up]]/page": "app/auth/sign-up/[[...sign-up]]/page.js", "/auth/sign-in/[[...sign-in]]/page": "app/auth/sign-in/[[...sign-in]]/page.js", "/dashboard/appointments/page": "app/dashboard/appointments/page.js", "/dashboard/billing/page": "app/dashboard/billing/page.js", "/dashboard/admin/page": "app/dashboard/admin/page.js", "/dashboard/page": "app/dashboard/page.js", "/dashboard/patients/page": "app/dashboard/patients/page.js", "/dashboard/kanban/page": "app/dashboard/kanban/page.js", "/dashboard/treatments/page": "app/dashboard/treatments/page.js", "/dashboard/product/[productId]/page": "app/dashboard/product/[productId]/page.js", "/dashboard/product/page": "app/dashboard/product/page.js", "/dashboard/profile/[[...profile]]/page": "app/dashboard/profile/[[...profile]]/page.js", "/dashboard/patients/[id]/page": "app/dashboard/patients/[id]/page.js", "/dashboard/overview/@area_stats/page": "app/dashboard/overview/@area_stats/page.js", "/dashboard/overview/@pie_stats/page": "app/dashboard/overview/@pie_stats/page.js", "/dashboard/overview/@bar_stats/page": "app/dashboard/overview/@bar_stats/page.js", "/dashboard/overview/@sales/page": "app/dashboard/overview/@sales/page.js"}