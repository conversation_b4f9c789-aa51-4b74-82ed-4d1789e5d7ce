{"version": 3, "file": "../app/dashboard/treatments/page.js", "mappings": "sbAAA,olBCAA,oDCAA,oGCAA,oECAA,0GCAA,qDCAA,oMCQA,IAAMA,EAAOC,EAAAA,EAAYA,CAInBC,EAAmBC,EAAAA,aAAmB,CAAwB,CAAC,GAC/DC,EAAY,CAAkH,CAClI,GAAGC,EACkC,GAC9B,UAACH,EAAiBI,QAAQ,EAACC,MAAO,CACvCC,KAAMH,EAAMG,IACd,EAAGC,sBAAoB,4BAA4BC,wBAAsB,YAAYC,0BAAwB,oBACzG,UAACC,EAAAA,EAAUA,CAAAA,CAAE,GAAGP,CAAK,CAAEI,sBAAoB,aAAaE,0BAAwB,eAGhFE,EAAe,KACnB,IAAMC,EAAeX,EAAAA,UAAgB,CAACD,GAChCa,EAAcZ,EAAAA,UAAgB,CAACa,GAC/B,eACJC,CAAa,CACd,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,GACZC,EAAYC,CAAAA,EAAAA,EAAAA,EAAAA,CAAYA,CAAC,CAC7BZ,KAAMM,EAAaN,IAAI,GAEnBa,EAAaJ,EAAcH,EAAaN,IAAI,CAAEW,GACpD,GAAI,CAACL,EACH,MAAM,MADW,kDAGnB,GAAM,IACJQ,CAAE,CACH,CAAGP,EACJ,MAAO,IACLO,EACAd,KAAMM,EAAaN,IAAI,CACvBe,WAAY,GAAGD,EAAG,UAAU,CAAC,CAC7BE,kBAAmB,GAAGF,EAAG,sBAAsB,CAAC,CAChDG,cAAe,GAAGH,EAAG,kBAAkB,CAAC,CACxC,GAAGD,CAAU,CAEjB,EAIML,EAAkBb,EAAAA,aAAmB,CAAuB,CAAC,GACnE,SAASuB,EAAS,WAChBC,CAAS,CACT,GAAGtB,EACyB,EAC5B,IAAMiB,EAAKnB,EAAAA,KAAW,GACtB,MAAO,UAACa,EAAgBV,QAAQ,EAACC,MAAO,IACtCe,CACF,EAAGb,sBAAoB,2BAA2BC,wBAAsB,WAAWC,0BAAwB,oBACvG,UAACiB,MAAAA,CAAIC,YAAU,YAAYF,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,aAAcH,GAAa,GAAGtB,CAAK,IAElF,CACA,SAAS0B,EAAU,WACjBJ,CAAS,CACT,GAAGtB,EAC8C,EACjD,GAAM,OACJ2B,CAAK,YACLT,CAAU,CACX,CAAGV,IACJ,MAAO,UAACoB,EAAAA,CAAKA,CAAAA,CAACJ,YAAU,aAAaK,aAAY,CAAC,CAACF,EAAOL,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qCAAsCH,GAAYQ,QAASZ,EAAa,GAAGlB,CAAK,CAAEI,sBAAoB,QAAQC,wBAAsB,YAAYC,0BAAwB,YAClP,CACA,SAASyB,EAAY,CACnB,GAAG/B,EAC+B,EAClC,GAAM,OACJ2B,CAAK,YACLT,CAAU,mBACVC,CAAiB,eACjBC,CAAa,CACd,CAAGZ,IACJ,MAAO,UAACwB,EAAAA,EAAIA,CAAAA,CAACR,YAAU,eAAeP,GAAIC,EAAYe,mBAAkB,EAAkC,GAAGd,EAAkB,CAAC,EAAEC,EAAAA,CAAe,CAAhE,GAAGD,EAAAA,CAAmB,CAA4Ce,eAAc,CAAC,CAACP,EAAQ,GAAG3B,CAAK,CAAEI,sBAAoB,OAAOC,wBAAsB,cAAcC,0BAAwB,YAC9Q,CACA,SAAS6B,EAAgB,WACvBb,CAAS,CACT,GAAGtB,EACuB,EAC1B,GAAM,mBACJmB,CAAiB,CAClB,CAAGX,IACJ,MAAO,UAAC4B,IAAAA,CAAEZ,YAAU,mBAAmBP,GAAIE,EAAmBG,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCH,GAAa,GAAGtB,CAAK,CAAEK,wBAAsB,kBAAkBC,0BAAwB,YACtM,CACA,SAAS+B,EAAY,WACnBf,CAAS,CACT,GAAGtB,EACuB,EAC1B,GAAM,OACJ2B,CAAK,CACLP,eAAa,CACd,CAAGZ,IACE8B,EAAOX,EAAQY,OAAOZ,GAAOa,SAAW,IAAMxC,EAAMyC,QAAQ,QAClE,EAGO,EAHH,CAGG,CAHI,CAGJ,KAACL,IAAAA,CAAEZ,YAAU,eAAeP,GAAIG,EAAeE,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,2BAA4BH,GAAa,GAAGtB,CAAK,CAAEK,wBAAsB,cAAcC,0BAAwB,oBAC9KgC,IAHI,IAKX,0BC3GA,oDCAA,kDCAA,+CCAA,yGCAA,6VCeA,OACA,UACA,GACA,CACA,UACA,YACA,CACA,UACA,aACA,CACA,uBAAiC,EACjC,MApBA,IAAoB,uCAAsI,CAoB1J,qGAES,EACF,CACP,CAGA,EACA,CACO,CACP,CACA,QAhCA,IAAsB,uCAA4H,CAgClJ,2FACA,UACA,sBAAoC,uCAAyQ,aAC7S,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QAjDA,IAAsB,uCAAiH,CAiDvI,gFACA,gBAjDA,IAAsB,uCAAuH,CAiD7I,sFACA,aAjDA,IAAsB,sCAAoH,CAiD1I,mFACA,WAjDA,IAAsB,4CAAgF,CAiDtG,+CACA,cAjDA,IAAsB,4CAAmF,CAiDzG,kDACA,UACA,sBAAoC,uCAAyQ,aAC7S,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,wGAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,kCACA,iCAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,0BC5FD,kDCAA,iECAA,uDCAA,wGCcO,SAASI,EAAmB,MACjCC,CAAI,CACJC,cAAY,OACZC,CAAK,aACLC,CAAW,aACXC,EAAc,IAAI,YAClBC,EAAa,IAAI,SACjBC,EAAU,SAAS,WACnBC,CAAS,CACTC,WAAU,CAAK,CACS,EACxB,MAAO,UAACC,EAAAA,EAAWA,CAAAA,CAACT,KAAMA,EAAMC,aAAcA,EAAcxC,sBAAoB,cAAcC,wBAAsB,qBAAqBC,0BAAwB,mCAC7J,WAAC+C,EAAAA,EAAkBA,CAAAA,CAACjD,sBAAoB,qBAAqBE,0BAAwB,oCACnF,WAACgD,EAAAA,EAAiBA,CAAAA,CAAClD,sBAAoB,oBAAoBE,0BAAwB,oCACjF,UAACiD,EAAAA,EAAgBA,CAAAA,CAACnD,sBAAoB,mBAAmBE,0BAAwB,mCAA2BuC,IAC5G,UAACW,EAAAA,EAAsBA,CAAAA,CAACpD,sBAAoB,yBAAyBE,0BAAwB,mCAA2BwC,OAE1H,WAACW,EAAAA,EAAiBA,CAAAA,CAACrD,sBAAoB,oBAAoBE,0BAAwB,oCACjF,UAACoD,EAAAA,EAAiBA,CAAAA,CAACC,SAAUR,EAAS/C,sBAAoB,oBAAoBE,0BAAwB,mCAA2B0C,IACjI,UAACY,EAAAA,EAAiBA,CAAAA,CAACC,QAASX,EAAWS,SAAUR,EAAS7B,UAAuB,gBAAZ2B,EAA4B,qEAAuE,GAAI7C,sBAAoB,oBAAoBE,0BAAwB,mCACzO6C,EAAU,SAAWJ,WAKlC,0BCvCA,qDCAA,yDCAA,qDCAA,+DCAA,sCAAiL,CAEjL,uCAAiK,yBCFjK,oDCAA,kECAA,yDCAA,gDCAA,uCAAiL,CAEjL,uCAAiK,yBCFjK,6GCAA,+DGmBI,sBAAsB,+IFlB1B,EAAe,cAAqB,UAAW,aAAe,mBAAmB,CAAC,CAAC,OAAO,CAAC,EAAI,yEAA0E,KAAM,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAI,0BAA0B,CAAM,UAAQ,GAAE,CAAC,OAAO,CAAC,EAAI,UAAU,IAAM,CAAO,OAAC,EAAE,CAAC,OAAO,CAAC,EAAI,QAAS,KAAM,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAI,2CAA2C,CAAM,UAAQ,EAAC,CAAC,kCCMtX,eAAee,IAC5B,GAAM,OADsBA,CAE1BC,CAAM,CAFoBD,CAGxB,MAAME,CAAAA,EAAAA,EAAAA,CAAAA,CAAAA,EAAAA,QACV,EAGOC,CAAAA,CAHH,CAGGA,CAHM,CAGNA,GAAAA,CAACC,CAAAA,EAAAA,CAAAA,CAAAA,CAAc9D,qBAAoB,iBAAgBC,uBAAsB,kBAAiBC,yBAAwB,YACrH,SAAA6D,CAAAA,EAAAA,EAAAA,IAAAA,CAAC5C,CAAAA,KAAAA,CAAAA,CAAID,SAAU,4CAEb2C,CAAAA,EAAAA,EAAAA,GAAAA,CAAC1C,CAAAA,KAAAA,CAAAA,CAAID,SAAU,qCACb,SAAA6C,CAAAA,EAAAA,EAAAA,IAAAA,CAAC5C,CAAAA,KAAAA,CAAAA,WACC4C,CAAAA,EAAAA,EAAAA,IAAAA,CAACC,CAAAA,IAAAA,CAAAA,CAAG9C,SAAU,uEACZ2C,CAAAA,EAAAA,EAAAA,GAAAA,CAACI,CAAAA,EAAAA,CAAgB/C,SAAU,GAA1B+C,OAAmCjE,qBAAoB,mBAAkBE,yBAAwB,cACjGgE,CAAAA,EAAAA,EAAAA,CAAAA,CAAE,wBAELL,CAAAA,EAAAA,EAAAA,GAAAA,CAAC7B,CAAAA,GAAAA,CAAAA,CAAEd,SAAU,kCACVgD,CAAAA,EAAAA,EAAAA,CAAAA,CAAE,+BAMTL,CAAAA,EAAAA,EAAAA,GAAAA,CAACM,CAAAA,EAAAA,cAAAA,CAAAA,CAAenE,qBAAoB,kBAAiBE,yBAAwB,mBAlB1EkE,CAAAA,EAAAA,EAAAA,QAAAA,CAAS,iBAqBpB,CCzBA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAAyB,IAAI,CATd,IASmB,CAAC,EAAiB,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IAIkC,EAAE,CACzD,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAAC,GAAG,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,MAD4B,CACnB,CAAE,CAElB,CAEM,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,uBAAuB,CACvC,aAAa,CAAE,MAAM,mBACrB,EACA,aAAa,EADI,SAEjB,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CADuB,CAAN,CAOjB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,IChF9B,4DCAA,kDCAA,wDCAA,iECAA,uDCAA,qDCAA,0DCAA,iDCAA,2DCAA,yHCEA,SAASC,EAAS,WAChBnD,CAAS,CACT,GAAGtB,EAC8B,EACjC,MAAO,UAAC0E,WAAAA,CAASlD,YAAU,WAAWF,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,scAAucH,GAAa,GAAGtB,CAAK,CAAEK,wBAAsB,WAAWC,0BAAwB,gBAC7kB,0BCPA,iDCAA,sSCeA,IAAMqE,EAAkBC,EAAAA,EAAQ,CAAC,CAC/BzE,KAAMyE,EAAAA,EAAQ,GAAGC,GAAG,CAAC,EAAG,gBAAgBC,GAAG,CAAC,IAAK,kBAAkBC,KAAK,CAAC,wBAAyB,2BAClGjC,YAAa8B,EAAAA,EAAQ,GAAGE,GAAG,CAAC,IAAM,iBAAiBE,QAAQ,GAC3DC,aAAcL,EAAAA,EAAQ,GAAGC,GAAG,CAAC,EAAG,cAAcC,GAAG,CAAC,IAAO,iBAAiBI,UAAU,CAAC,IAAM,0BAC3FC,yBAA0BP,EAAAA,EAAQ,GAAGC,GAAG,CAAC,EAAG,aAAaC,GAAG,CAAC,IAAK,oBAAoBM,GAAG,CAAC,YAC5F,GAQO,SAASC,EAAoB,MAClC1C,CAAI,CACJC,cAAY,WACZ0C,CAAS,WACTC,CAAS,CACgB,EACzB,GAAM,CAACpC,EAASqC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjCC,EAAY,CAAC,CAACJ,EACdK,EAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAoB,CACtCC,SAAUC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAACnB,GACtBoB,cAAe,CACb5F,KAAMmF,GAAWnF,MAAQ,GACzB2C,YAAawC,GAAWxC,aAAe,GACvCmC,aAAcK,GAAWL,cAAgB,EACzCE,yBAA0BG,GAAWH,0BAA4B,EACnE,CACF,GAaMa,EAAW,MAAOC,IACtBT,GAAW,GACX,GAAI,CACF,IAAMU,EAAgB,CACpB/F,KAAM8F,EAAK9F,IAAI,CACf2C,YAAamD,EAAKnD,WAAW,OAAIqD,EACjClB,aAAcgB,EAAKhB,YAAY,CAC/BE,yBAA0Bc,EAAKd,wBAAwB,EAErDO,GACF,MAAMU,EADO,EACMA,CAACC,MAAM,CAACf,EAAUrE,EAAE,CAAEiF,GACzCI,EAAAA,KAAKA,CAACC,OAAO,CAAC,oCAEd,MAAMH,EAAAA,EAAaA,CAACI,MAAM,CAACN,GAC3BI,EAAAA,KAAKA,CAACC,OAAO,CAAC,mCAEhBhB,MACA3C,GAAa,GACb+C,EAAKc,KAAK,EACZ,CAAE,MAAO9E,EAAO,CACd+E,QAAQ/E,KAAK,CAAC,4BAA6BA,GAC3C2E,EAAAA,KAAKA,CAAC3E,KAAK,CAAC,CAAC,UAAU,EAAE+D,EAAY,SAAW,SAAS,UAAU,CAAC,CACtE,QAAU,CACRF,GAAW,EACb,CACF,EACA,MAAO,UAACmB,EAAAA,EAAMA,CAAAA,CAAChE,KAAMA,EAAMC,aAAcA,EAAcxC,sBAAoB,SAASC,wBAAsB,sBAAsBC,0BAAwB,qCACpJ,WAACsG,EAAAA,EAAaA,CAAAA,CAACtF,UAAU,mBAAmBlB,sBAAoB,gBAAgBE,0BAAwB,sCACtG,WAACuG,EAAAA,EAAYA,CAAAA,CAACzG,sBAAoB,eAAeE,0BAAwB,sCACvE,UAACwG,EAAAA,EAAWA,CAAAA,CAAC1G,sBAAoB,cAAcE,0BAAwB,qCACpEoF,EAAYpB,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,4BAA8BA,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,6BAEjD,UAACyC,EAAAA,EAAiBA,CAAAA,CAAC3G,sBAAoB,oBAAoBE,0BAAwB,qCAChFoF,EAAY,aAAe,uBAIhC,UAAC/F,EAAAA,EAAIA,CAAAA,CAAE,GAAGgG,CAAI,CAAEvF,sBAAoB,OAAOE,0BAAwB,qCACjE,WAACqF,OAAAA,CAAKK,SAAUL,EAAKqB,YAAY,CAAChB,GAAW1E,UAAU,sBAErD,UAACvB,EAAAA,EAASA,CAAAA,CAACkH,QAAStB,EAAKsB,OAAO,CAAE9G,KAAK,OAAO+G,OAAQ,CAAC,OACvDC,CAAK,CACN,GAAK,WAAC9F,EAAAA,EAAQA,CAAAA,WACP,WAACK,EAAAA,EAASA,CAAAA,WAAE4C,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,wBAAwB,QACtC,UAACvC,EAAAA,EAAWA,CAAAA,UACV,UAACqF,EAAAA,CAAKA,CAAAA,CAACC,YAAa/C,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,mCAAqC,GAAG6C,CAAK,KAErE,UAAC9E,EAAAA,EAAWA,CAAAA,CAAAA,MACDjC,sBAAoB,YAAYE,0BAAwB,8BAGzE,UAACP,EAAAA,EAASA,CAAAA,CAACkH,QAAStB,EAAKsB,OAAO,CAAE9G,KAAK,cAAc+G,OAAQ,CAAC,OAC9DC,CAAK,CACN,GAAK,WAAC9F,EAAAA,EAAQA,CAAAA,WACP,UAACK,EAAAA,EAASA,CAAAA,UAAE4C,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,iCACd,UAACvC,EAAAA,EAAWA,CAAAA,UACV,UAAC0C,EAAAA,CAAQA,CAAAA,CAAC4C,YAAa/C,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,0CAA2ChD,UAAU,eAAgB,GAAG6F,CAAK,KAExG,UAAC9E,EAAAA,EAAWA,CAAAA,CAAAA,MACDjC,sBAAoB,YAAYE,0BAAwB,8BAEzE,WAACiB,MAAAA,CAAID,UAAU,mCAEb,UAACvB,EAAAA,EAASA,CAAAA,CAACkH,QAAStB,EAAKsB,OAAO,CAAE9G,KAAK,eAAe+G,OAAQ,CAAC,OAC/DC,CAAK,CACN,GAAK,WAAC9F,EAAAA,EAAQA,CAAAA,WACP,WAACK,EAAAA,EAASA,CAAAA,WAAE4C,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,yBAAyB,eACvC,UAACvC,EAAAA,EAAWA,CAAAA,UACV,UAACqF,EAAAA,CAAKA,CAAAA,CAACE,KAAK,SAASC,KAAK,OAAOF,YAAa/C,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,oCAAsC,GAAG6C,CAAK,CAAEK,SAAUC,GAAKN,EAAMK,QAAQ,CAACE,WAAWD,EAAEE,MAAM,CAACzH,KAAK,GAAK,OAE9J,UAACmC,EAAAA,EAAWA,CAAAA,CAAAA,MACDjC,sBAAoB,YAAYE,0BAAwB,8BAGzE,UAACP,EAAAA,EAASA,CAAAA,CAACkH,QAAStB,EAAKsB,OAAO,CAAE9G,KAAK,2BAA2B+G,OAAQ,CAAC,OAC3EC,CAAK,CACN,GAAK,WAAC9F,EAAAA,EAAQA,CAAAA,WACP,WAACK,EAAAA,EAASA,CAAAA,WAAE4C,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,4BAA4B,aAC1C,UAACvC,EAAAA,EAAWA,CAAAA,UACV,UAACqF,EAAAA,CAAKA,CAAAA,CAACE,KAAK,SAASD,YAAa/C,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,uCAAyC,GAAG6C,CAAK,CAAEK,SAAUC,GAAKN,EAAMK,QAAQ,CAACI,SAASH,EAAEE,MAAM,CAACzH,KAAK,GAAK,OAEnJ,UAACmC,EAAAA,EAAWA,CAAAA,CAAAA,MACDjC,sBAAoB,YAAYE,0BAAwB,iCAG3E,WAACuH,EAAAA,EAAYA,CAAAA,CAACzH,sBAAoB,eAAeE,0BAAwB,sCACvE,UAACwH,EAAAA,CAAMA,CAAAA,CAACR,KAAK,SAASrE,QAAQ,UAAUY,QAAS,IAAMjB,GAAa,GAAQe,SAAUR,EAAS/C,sBAAoB,SAASE,0BAAwB,qCACjJgE,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,2BAEL,UAACwD,EAAAA,CAAMA,CAAAA,CAACR,KAAK,SAAS3D,SAAUR,EAAS/C,sBAAoB,SAASE,0BAAwB,qCAC3F6C,EAAU,SAAWuC,EAAY,OAAS,qBAO3D,2BC5IO,SAASnB,IACd,GAAM,eACJwD,CAAa,CACd,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,GACL,CAACC,EAAYC,EAAc,CAAGzC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAc,EAAE,EACtD,CAACtC,EAASqC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAAC9D,EAAOwG,EAAS,CAAG1C,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,MAC5C,CAAC2C,EAAgBC,EAAkB,CAAG5C,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC/C,CAAC6C,EAAkBC,EAAoB,CAAG9C,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,GAClD,CAAC+C,EAAkBC,EAAoB,CAAGhD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACnD,CAACiD,EAAmBC,EAAqB,CAAGlD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,GACpD,CAACmD,EAAeC,EAAiB,CAAGpD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC7CqD,EAAkB,UACtB,GAAI,CACFtD,EAAW,IACX2C,EAAS,MACT,IAAMY,EAAW,MAAM3C,EAAAA,EAAaA,CAAC4C,MAAM,CAAC,CAC1CC,MAAO,GACT,GACAf,EAAca,EAASG,IAAI,CAC7B,CAAE,MAAOC,EAAK,CACZzC,QAAQ/E,KAAK,CAAC,8BAA+BwH,GAC7ChB,EAAS,qDACX,QAAU,CACR3C,EAAW,GACb,CACF,EAMM4D,EAAqB,KACzBb,OAAoBpC,GACpBkC,GAAkB,EACpB,EACMgB,EAAsB,IAC1Bd,EAAoBjD,GACpB+C,GAAkB,EACpB,EA0BMiB,EAAsB,UAC1B,GAAKZ,CAAD,EACJG,EAAiB,IACjB,GAAI,CACF,MAHsB,EAGhBzC,EAAaA,CAACmD,MAAM,CAACb,EAAkBzH,EAAE,EAC/CqF,EAAAA,KAAKA,CAACC,OAAO,CAAC,kCACdkC,GAAoB,GACpBE,EAAqBxC,QACrB2C,GACF,CAAE,MAAOnH,EAAO,CACd+E,MAFmB,EAEX/E,KAAK,CAAC,WAFwB,mBAEOA,GAC7C2E,EAAAA,KAAKA,CAAC3E,KAAK,CAAC,6BACd,QAAU,CACRkH,GAAiB,EACnB,EACF,EACMW,EAAoB,KACxBnB,GAAkB,GAClBE,EAAoBpC,QACpB2C,GACF,SACI3F,EACK,OADI,CACJ,EAAC5B,MAAAA,CAAID,OAH0B,GAGhB,iDAClB,WAACC,MAAAA,CAAID,UAAU,wBACb,UAACC,MAAAA,CAAID,UAAU,6EACf,UAACc,IAAAA,CAAEd,UAAU,iCAAyBgD,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,uCAI5C3C,EACK,KADE,GACF,EAACJ,MAAAA,CAAID,UAAU,iDAClB,WAACC,MAAAA,CAAID,UAAU,wBACb,UAACc,IAAAA,CAAEd,UAAU,6BAAqBK,IAClC,UAACmG,EAAAA,CAAMA,CAAAA,CAACjE,QAAS,IAAM4F,OAAOC,QAAQ,CAACC,MAAM,GAAI1G,QAAQ,mBAAU,YAMjD,GAAG,CAAzBgF,EAAW2B,MAAM,CACZ,iCACH,UAACrI,MAAAA,CAAID,UAAU,iDACb,WAACC,MAAAA,CAAID,UAAU,wBACb,UAAC+C,EAAAA,CAAeA,CAAAA,CAAC/C,UAAU,iDAC3B,UAACuI,KAAAA,CAAGvI,UAAU,oCAA4BgD,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,6BAC5C,UAAClC,IAAAA,CAAEd,UAAU,sCAA6B,mBAG1C,UAACwI,EAAAA,EAAcA,CAAAA,CAACC,WAAW,+BACzB,WAACjC,EAAAA,CAAMA,CAAAA,CAACjE,QAASuF,YACf,UAACY,EAAAA,CAAQA,CAAAA,CAAC1I,UAAU,iBACnBgD,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,qCAMX,UAACe,EAAmBA,CAAC1C,KAAMyF,EAAgBxF,SAAvByC,IAAqCgD,EAAmB/C,UAAWgD,EAAkB/C,UAAWiE,OAGnH,iCACH,WAACjI,MAAAA,CAAID,UAAU,sBAEb,WAACC,MAAAA,CAAID,UAAU,8CACb,UAACC,MAAAA,UACC,WAACsI,KAAAA,CAAGvI,UAAU,gCACX2G,EAAW2B,MAAM,CAAC,IAAEtF,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,mCAG3B,UAACwF,EAAAA,EAAcA,CAAAA,CAACC,WAAW,sBAAsB3J,sBAAoB,iBAAiBE,0BAAwB,+BAC5G,WAACwH,EAAAA,CAAMA,CAAAA,CAACjE,QAASuF,EAAoBhJ,sBAAoB,SAASE,0BAAwB,gCACxF,UAAC0J,EAAAA,CAAQA,CAAAA,CAAC1I,UAAU,eAAelB,sBAAoB,WAAWE,0BAAwB,wBACzFgE,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,mCAMT,UAAC/C,MAAAA,CAAID,UAAU,oDACZ2G,EAAWgC,GAAG,CAAC3E,GAAa,WAAC/D,MAAAA,CAAuBD,UAAU,4CAC3D,WAACC,MAAAA,CAAID,UAAU,6CACb,WAACC,MAAAA,CAAID,UAAU,sBACb,UAAC4I,KAAAA,CAAG5I,UAAU,+BAAuBgE,EAAUnF,IAAI,GAClDmF,EAAUxC,WAAW,EAAI,UAACV,IAAAA,CAAEd,UAAU,sDAClCgE,EAAUxC,WAAW,MAG5B,UAACgH,EAAAA,EAAcA,CAAAA,CAACC,WAAW,6BACzB,WAACI,EAAAA,EAAYA,CAAAA,WACX,UAACC,EAAAA,EAAmBA,CAAAA,CAACC,OAAO,aAC1B,UAACvC,EAAAA,CAAMA,CAAAA,CAAC7E,QAAQ,QAAQqH,KAAK,cAC3B,UAACC,EAAAA,CAAQA,CAAAA,CAACjJ,UAAU,gBAGxB,WAACkJ,EAAAA,EAAmBA,CAAAA,WAClB,WAACC,EAAAA,EAAgBA,CAAAA,CAAC5G,QAAS,IAAMwF,EAAoB/D,aACnD,UAACiF,EAAAA,CAAQA,CAAAA,CAACjJ,UAAU,iBACnBgD,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,0BAEL,WAACmG,EAAAA,EAAgBA,CAAAA,CAAC5G,QAAS,KAC7B8E,EAAqBrD,GACrBmD,GAAoB,EACtB,EAAGnH,UAAU,yBACP,UAACoJ,EAAAA,CAASA,CAAAA,CAACpJ,UAAU,iBACpBgD,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,uCAOb,UAAC/C,MAAAA,CAAID,UAAU,qDACb,WAACC,MAAAA,CAAID,UAAU,sBACb,WAACC,MAAAA,CAAID,UAAU,oCACb,UAACqJ,OAAAA,CAAKrJ,UAAU,iCAAwB,QACxC,WAACqJ,OAAAA,CAAKrJ,UAAU,wBAAc,OAAEgE,EAAUL,YAAY,EAAE2F,QAAQ,IAAM,YAEvEtF,EAAUH,wBAAwB,EAAI,WAAC5D,MAAAA,CAAID,UAAU,oCAClD,UAACqJ,OAAAA,CAAKrJ,UAAU,iCAAwB,QACxC,WAACqJ,OAAAA,WAAMrF,EAAUH,wBAAwB,CAAC,mBAxCfG,EAAUrE,EAAE,QAgDvD,UAACoE,EAAmBA,CAAC1C,KAAMyF,EAAgBxF,SAAvByC,IAAqCgD,EAAmB/C,UAAWgD,EAAkB/C,UAAWiE,EAAmBpJ,sBAAoB,sBAAsBE,0BAAwB,wBAEzM,UAACoC,EAAAA,CAAkBA,CAAAA,CAACC,KAAM6F,EAAkB5F,aAAc6F,EAAqB5F,MAAM,SAASC,YAAa,CAAC,OAAO,EAAE4F,GAAmBvI,KAAK,WAAW,CAAC,CAAE4C,YAAY,KAAKE,QAAQ,cAAcC,UAAWoG,EAAqBnG,QAASyF,EAAexI,sBAAoB,qBAAqBE,0BAAwB,0BAEjU,yBClNA,6DCAA", "sources": ["webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"module\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"require-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"os\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/form.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"stream\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"util\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:child_process\"", "webpack://next-shadcn-dashboard-starter/?dbc0", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"path\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:http\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:zlib\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/confirmation-dialog.tsx", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:tls\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:https\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:os\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"import-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:stream\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:util\"", "webpack://next-shadcn-dashboard-starter/?af1a", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:fs\"", "webpack://next-shadcn-dashboard-starter/../../../src/icons/IconStethoscope.ts", "webpack://next-shadcn-dashboard-starter/src/app/dashboard/treatments/page.tsx", "webpack://next-shadcn-dashboard-starter/sentry-wrapper-module", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"worker_threads\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"zlib\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"perf_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:worker_threads\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:path\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:net\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"url\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"child_process\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:readline\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/textarea.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"tty\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"async_hooks\"", "webpack://next-shadcn-dashboard-starter/./src/components/treatments/treatment-form-dialog.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/treatments/treatments-list.tsx", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:inspector\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\nimport { Slot } from '@radix-ui/react-slot';\nimport { Controller, FormProvider, useFormContext, useFormState, type ControllerProps, type FieldPath, type FieldValues } from 'react-hook-form';\nimport { cn } from '@/lib/utils';\nimport { Label } from '@/components/ui/label';\nconst Form = FormProvider;\ntype FormFieldContextValue<TFieldValues extends FieldValues = FieldValues, TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>> = {\n  name: TName;\n};\nconst FormFieldContext = React.createContext<FormFieldContextValue>({} as FormFieldContextValue);\nconst FormField = <TFieldValues extends FieldValues = FieldValues, TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return <FormFieldContext.Provider value={{\n    name: props.name\n  }} data-sentry-element=\"FormFieldContext.Provider\" data-sentry-component=\"FormField\" data-sentry-source-file=\"form.tsx\">\r\n      <Controller {...props} data-sentry-element=\"Controller\" data-sentry-source-file=\"form.tsx\" />\r\n    </FormFieldContext.Provider>;\n};\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext);\n  const itemContext = React.useContext(FormItemContext);\n  const {\n    getFieldState\n  } = useFormContext();\n  const formState = useFormState({\n    name: fieldContext.name\n  });\n  const fieldState = getFieldState(fieldContext.name, formState);\n  if (!fieldContext) {\n    throw new Error('useFormField should be used within <FormField>');\n  }\n  const {\n    id\n  } = itemContext;\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState\n  };\n};\ntype FormItemContextValue = {\n  id: string;\n};\nconst FormItemContext = React.createContext<FormItemContextValue>({} as FormItemContextValue);\nfunction FormItem({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  const id = React.useId();\n  return <FormItemContext.Provider value={{\n    id\n  }} data-sentry-element=\"FormItemContext.Provider\" data-sentry-component=\"FormItem\" data-sentry-source-file=\"form.tsx\">\r\n      <div data-slot='form-item' className={cn('grid gap-2', className)} {...props} />\r\n    </FormItemContext.Provider>;\n}\nfunction FormLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  const {\n    error,\n    formItemId\n  } = useFormField();\n  return <Label data-slot='form-label' data-error={!!error} className={cn('data-[error=true]:text-destructive', className)} htmlFor={formItemId} {...props} data-sentry-element=\"Label\" data-sentry-component=\"FormLabel\" data-sentry-source-file=\"form.tsx\" />;\n}\nfunction FormControl({\n  ...props\n}: React.ComponentProps<typeof Slot>) {\n  const {\n    error,\n    formItemId,\n    formDescriptionId,\n    formMessageId\n  } = useFormField();\n  return <Slot data-slot='form-control' id={formItemId} aria-describedby={!error ? `${formDescriptionId}` : `${formDescriptionId} ${formMessageId}`} aria-invalid={!!error} {...props} data-sentry-element=\"Slot\" data-sentry-component=\"FormControl\" data-sentry-source-file=\"form.tsx\" />;\n}\nfunction FormDescription({\n  className,\n  ...props\n}: React.ComponentProps<'p'>) {\n  const {\n    formDescriptionId\n  } = useFormField();\n  return <p data-slot='form-description' id={formDescriptionId} className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-component=\"FormDescription\" data-sentry-source-file=\"form.tsx\" />;\n}\nfunction FormMessage({\n  className,\n  ...props\n}: React.ComponentProps<'p'>) {\n  const {\n    error,\n    formMessageId\n  } = useFormField();\n  const body = error ? String(error?.message ?? '') : props.children;\n  if (!body) {\n    return null;\n  }\n  return <p data-slot='form-message' id={formMessageId} className={cn('text-destructive text-sm', className)} {...props} data-sentry-component=\"FormMessage\" data-sentry-source-file=\"form.tsx\">\r\n      {body}\r\n    </p>;\n}\nexport { useFormField, Form, FormItem, FormLabel, FormControl, FormDescription, FormMessage, FormField };", "module.exports = require(\"stream\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\");\nconst page6 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\treatments\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'treatments',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\treatments\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\treatments\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/dashboard/treatments/page\",\n        pathname: \"/dashboard/treatments\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "'use client';\n\nimport { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';\ninterface ConfirmationDialogProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  title: string;\n  description: string;\n  confirmText?: string;\n  cancelText?: string;\n  variant?: 'default' | 'destructive';\n  onConfirm: () => void;\n  loading?: boolean;\n}\nexport function ConfirmationDialog({\n  open,\n  onOpenChange,\n  title,\n  description,\n  confirmText = '确认',\n  cancelText = '取消',\n  variant = 'default',\n  onConfirm,\n  loading = false\n}: ConfirmationDialogProps) {\n  return <AlertDialog open={open} onOpenChange={onOpenChange} data-sentry-element=\"AlertDialog\" data-sentry-component=\"ConfirmationDialog\" data-sentry-source-file=\"confirmation-dialog.tsx\">\n      <AlertDialogContent data-sentry-element=\"AlertDialogContent\" data-sentry-source-file=\"confirmation-dialog.tsx\">\n        <AlertDialogHeader data-sentry-element=\"AlertDialogHeader\" data-sentry-source-file=\"confirmation-dialog.tsx\">\n          <AlertDialogTitle data-sentry-element=\"AlertDialogTitle\" data-sentry-source-file=\"confirmation-dialog.tsx\">{title}</AlertDialogTitle>\n          <AlertDialogDescription data-sentry-element=\"AlertDialogDescription\" data-sentry-source-file=\"confirmation-dialog.tsx\">{description}</AlertDialogDescription>\n        </AlertDialogHeader>\n        <AlertDialogFooter data-sentry-element=\"AlertDialogFooter\" data-sentry-source-file=\"confirmation-dialog.tsx\">\n          <AlertDialogCancel disabled={loading} data-sentry-element=\"AlertDialogCancel\" data-sentry-source-file=\"confirmation-dialog.tsx\">{cancelText}</AlertDialogCancel>\n          <AlertDialogAction onClick={onConfirm} disabled={loading} className={variant === 'destructive' ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90' : ''} data-sentry-element=\"AlertDialogAction\" data-sentry-source-file=\"confirmation-dialog.tsx\">\n            {loading ? '加载中...' : confirmText}\n          </AlertDialogAction>\n        </AlertDialogFooter>\n      </AlertDialogContent>\n    </AlertDialog>;\n}", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "import(/* webpackMode: \"eager\", webpackExports: [\"TreatmentsList\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\treatments\\\\treatments-list.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ScrollArea\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\");\n", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "import(/* webpackMode: \"eager\", webpackExports: [\"TreatmentsList\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\treatments\\\\treatments-list.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ScrollArea\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\");\n", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'stethoscope', 'IconStethoscope', [[\"path\",{\"d\":\"M6 4h-1a2 2 0 0 0 -2 2v3.5h0a5.5 5.5 0 0 0 11 0v-3.5a2 2 0 0 0 -2 -2h-1\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M8 15a6 6 0 1 0 12 0v-3\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M11 3v2\",\"key\":\"svg-2\"}],[\"path\",{\"d\":\"M6 3v2\",\"key\":\"svg-3\"}],[\"path\",{\"d\":\"M20 10m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0\",\"key\":\"svg-4\"}]]);", "import { auth } from '@clerk/nextjs/server';\nimport { redirect } from 'next/navigation';\nimport PageContainer from '@/components/layout/page-container';\nimport { Button } from '@/components/ui/button';\nimport { IconPlus, IconStethoscope } from '@tabler/icons-react';\nimport { TreatmentsList } from '@/components/treatments/treatments-list';\nimport { t } from '@/lib/translations';\nexport default async function TreatmentsPage() {\n  const {\n    userId\n  } = await auth();\n  if (!userId) {\n    return redirect('/auth/sign-in');\n  }\n  return <PageContainer data-sentry-element=\"PageContainer\" data-sentry-component=\"TreatmentsPage\" data-sentry-source-file=\"page.tsx\">\n      <div className='flex flex-1 flex-col space-y-4'>\n        {/* Header */}\n        <div className='flex items-center justify-between'>\n          <div>\n            <h2 className='text-2xl font-bold tracking-tight flex items-center gap-2'>\n              <IconStethoscope className='size-6' data-sentry-element=\"IconStethoscope\" data-sentry-source-file=\"page.tsx\" />\n              {t('treatments.title')}\n            </h2>\n            <p className='text-muted-foreground'>\n              {t('treatments.subtitle')}\n            </p>\n          </div>\n        </div>\n\n        {/* Treatments List - Client Component */}\n        <TreatmentsList data-sentry-element=\"TreatmentsList\" data-sentry-source-file=\"page.tsx\" />\n      </div>\n    </PageContainer>;\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard/treatments',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard/treatments',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard/treatments',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard/treatments',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"worker_threads\");", "module.exports = require(\"zlib\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"node:crypto\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Textarea({\n  className,\n  ...props\n}: React.ComponentProps<'textarea'>) {\n  return <textarea data-slot='textarea' className={cn('border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm', className)} {...props} data-sentry-component=\"Textarea\" data-sentry-source-file=\"textarea.tsx\" />;\n}\nexport { Textarea };", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "'use client';\n\nimport React, { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport * as z from 'zod';\nimport { Button } from '@/components/ui/button';\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';\nimport { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { treatmentsApi } from '@/lib/api';\nimport { Treatment } from '@/types/clinic';\nimport { toast } from 'sonner';\nimport { t } from '@/lib/translations';\nconst treatmentSchema = z.object({\n  name: z.string().min(2, '治疗名称至少需要2个字符').max(100, '治疗名称不能超过100个字符').regex(/^[a-zA-Z0-9\\s\\-&()]+$/, '治疗名称只能包含字母、数字、空格、连字符和括号'),\n  description: z.string().max(1000, '描述不能超过1000个字符').optional(),\n  defaultPrice: z.number().min(0, '价格必须为0元或更高').max(10000, '价格不能超过10,000元').multipleOf(0.01, '价格必须是有效的货币金额（例如：99.99）'),\n  defaultDurationInMinutes: z.number().min(5, '时长至少需要5分钟').max(480, '时长不能超过8小时（480分钟）').int('时长必须是整数分钟')\n});\ntype TreatmentFormData = z.infer<typeof treatmentSchema>;\ninterface TreatmentFormDialogProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  treatment?: Treatment;\n  onSuccess?: () => void;\n}\nexport function TreatmentFormDialog({\n  open,\n  onOpenChange,\n  treatment,\n  onSuccess\n}: TreatmentFormDialogProps) {\n  const [loading, setLoading] = useState(false);\n  const isEditing = !!treatment;\n  const form = useForm<TreatmentFormData>({\n    resolver: zodResolver(treatmentSchema),\n    defaultValues: {\n      name: treatment?.name || '',\n      description: treatment?.description || '',\n      defaultPrice: treatment?.defaultPrice || 0,\n      defaultDurationInMinutes: treatment?.defaultDurationInMinutes || 30\n    }\n  });\n\n  // Reset form when treatment changes or dialog opens\n  React.useEffect(() => {\n    if (open) {\n      form.reset({\n        name: treatment?.name || '',\n        description: treatment?.description || '',\n        defaultPrice: treatment?.defaultPrice || 0,\n        defaultDurationInMinutes: treatment?.defaultDurationInMinutes || 30\n      });\n    }\n  }, [open, treatment, form]);\n  const onSubmit = async (data: TreatmentFormData) => {\n    setLoading(true);\n    try {\n      const treatmentData = {\n        name: data.name,\n        description: data.description || undefined,\n        defaultPrice: data.defaultPrice,\n        defaultDurationInMinutes: data.defaultDurationInMinutes\n      };\n      if (isEditing) {\n        await treatmentsApi.update(treatment.id, treatmentData);\n        toast.success('Treatment updated successfully');\n      } else {\n        await treatmentsApi.create(treatmentData);\n        toast.success('Treatment created successfully');\n      }\n      onSuccess?.();\n      onOpenChange(false);\n      form.reset();\n    } catch (error) {\n      console.error('Failed to save treatment:', error);\n      toast.error(`Failed to ${isEditing ? 'update' : 'create'} treatment`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  return <Dialog open={open} onOpenChange={onOpenChange} data-sentry-element=\"Dialog\" data-sentry-component=\"TreatmentFormDialog\" data-sentry-source-file=\"treatment-form-dialog.tsx\">\n      <DialogContent className=\"sm:max-w-[500px]\" data-sentry-element=\"DialogContent\" data-sentry-source-file=\"treatment-form-dialog.tsx\">\n        <DialogHeader data-sentry-element=\"DialogHeader\" data-sentry-source-file=\"treatment-form-dialog.tsx\">\n          <DialogTitle data-sentry-element=\"DialogTitle\" data-sentry-source-file=\"treatment-form-dialog.tsx\">\n            {isEditing ? t('treatments.editTreatment') : t('treatments.newTreatment')}\n          </DialogTitle>\n          <DialogDescription data-sentry-element=\"DialogDescription\" data-sentry-source-file=\"treatment-form-dialog.tsx\">\n            {isEditing ? '更新下方的治疗信息。' : '填写治疗详细信息以创建新服务。'}\n          </DialogDescription>\n        </DialogHeader>\n\n        <Form {...form} data-sentry-element=\"Form\" data-sentry-source-file=\"treatment-form-dialog.tsx\">\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-4\">\n            {/* Treatment Name */}\n            <FormField control={form.control} name=\"name\" render={({\n            field\n          }) => <FormItem>\n                  <FormLabel>{t('treatments.form.name')} *</FormLabel>\n                  <FormControl>\n                    <Input placeholder={t('treatments.form.namePlaceholder')} {...field} />\n                  </FormControl>\n                  <FormMessage />\n                </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"treatment-form-dialog.tsx\" />\n\n            {/* Description */}\n            <FormField control={form.control} name=\"description\" render={({\n            field\n          }) => <FormItem>\n                  <FormLabel>{t('treatments.form.description')}</FormLabel>\n                  <FormControl>\n                    <Textarea placeholder={t('treatments.form.descriptionPlaceholder')} className=\"min-h-[80px]\" {...field} />\n                  </FormControl>\n                  <FormMessage />\n                </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"treatment-form-dialog.tsx\" />\n\n            <div className=\"grid grid-cols-2 gap-4\">\n              {/* Default Price */}\n              <FormField control={form.control} name=\"defaultPrice\" render={({\n              field\n            }) => <FormItem>\n                    <FormLabel>{t('treatments.form.price')} (¥) *</FormLabel>\n                    <FormControl>\n                      <Input type=\"number\" step=\"0.01\" placeholder={t('treatments.form.pricePlaceholder')} {...field} onChange={e => field.onChange(parseFloat(e.target.value) || 0)} />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"treatment-form-dialog.tsx\" />\n\n              {/* Default Duration */}\n              <FormField control={form.control} name=\"defaultDurationInMinutes\" render={({\n              field\n            }) => <FormItem>\n                    <FormLabel>{t('treatments.form.duration')} (分钟) *</FormLabel>\n                    <FormControl>\n                      <Input type=\"number\" placeholder={t('treatments.form.durationPlaceholder')} {...field} onChange={e => field.onChange(parseInt(e.target.value) || 0)} />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"treatment-form-dialog.tsx\" />\n            </div>\n\n            <DialogFooter data-sentry-element=\"DialogFooter\" data-sentry-source-file=\"treatment-form-dialog.tsx\">\n              <Button type=\"button\" variant=\"outline\" onClick={() => onOpenChange(false)} disabled={loading} data-sentry-element=\"Button\" data-sentry-source-file=\"treatment-form-dialog.tsx\">\n                {t('common.actions.cancel')}\n              </Button>\n              <Button type=\"submit\" disabled={loading} data-sentry-element=\"Button\" data-sentry-source-file=\"treatment-form-dialog.tsx\">\n                {loading ? '保存中...' : isEditing ? '更新治疗' : '创建治疗'}\n              </Button>\n            </DialogFooter>\n          </form>\n        </Form>\n      </DialogContent>\n    </Dialog>;\n}", "'use client';\n\nimport { useEffect, useState } from 'react';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { useRole, PermissionGate } from '@/lib/role-context';\nimport { IconPlus, IconStethoscope, IconEdit, IconTrash } from '@tabler/icons-react';\nimport { treatmentsApi, appointmentsApi } from '@/lib/api';\nimport { Treatment } from '@/types/clinic';\nimport { TreatmentFormDialog } from './treatment-form-dialog';\nimport { ConfirmationDialog } from '@/components/ui/confirmation-dialog';\nimport { toast } from 'sonner';\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';\nimport { t } from '@/lib/translations';\nexport function TreatmentsList() {\n  const {\n    hasPermission\n  } = useRole();\n  const [treatments, setTreatments] = useState<Treatment[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [formDialogOpen, setFormDialogOpen] = useState(false);\n  const [editingTreatment, setEditingTreatment] = useState<Treatment | undefined>();\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [treatmentToDelete, setTreatmentToDelete] = useState<Treatment | undefined>();\n  const [actionLoading, setActionLoading] = useState(false);\n  const fetchTreatments = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await treatmentsApi.getAll({\n        limit: 100\n      });\n      setTreatments(response.docs);\n    } catch (err) {\n      console.error('Failed to fetch treatments:', err);\n      setError('Failed to load treatments. Please try again later.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchTreatments();\n  }, []);\n\n  // Handler functions\n  const handleNewTreatment = () => {\n    setEditingTreatment(undefined);\n    setFormDialogOpen(true);\n  };\n  const handleEditTreatment = (treatment: Treatment) => {\n    setEditingTreatment(treatment);\n    setFormDialogOpen(true);\n  };\n  const handleDeleteTreatment = async (treatment: Treatment) => {\n    // Check if treatment has any appointments\n    try {\n      setActionLoading(true);\n      const appointmentsResponse = await appointmentsApi.getAll({\n        limit: 1,\n        where: {\n          treatment: {\n            equals: treatment.id\n          }\n        }\n      });\n      if (appointmentsResponse.docs.length > 0) {\n        toast.error('Cannot delete treatment with existing appointments. Please cancel or complete all appointments first.');\n        return;\n      }\n      setTreatmentToDelete(treatment);\n      setDeleteDialogOpen(true);\n    } catch (error) {\n      console.error('Failed to check treatment appointments:', error);\n      toast.error('Failed to verify treatment appointments');\n    } finally {\n      setActionLoading(false);\n    }\n  };\n  const handleDeleteConfirm = async () => {\n    if (!treatmentToDelete) return;\n    setActionLoading(true);\n    try {\n      await treatmentsApi.delete(treatmentToDelete.id);\n      toast.success('Treatment deleted successfully');\n      setDeleteDialogOpen(false);\n      setTreatmentToDelete(undefined);\n      fetchTreatments(); // Refresh the list\n    } catch (error) {\n      console.error('Failed to delete treatment:', error);\n      toast.error('Failed to delete treatment');\n    } finally {\n      setActionLoading(false);\n    }\n  };\n  const handleFormSuccess = () => {\n    setFormDialogOpen(false);\n    setEditingTreatment(undefined);\n    fetchTreatments(); // Refresh the list\n  };\n  if (loading) {\n    return <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\n          <p className=\"text-muted-foreground\">{t('treatments.loadingTreatments')}</p>\n        </div>\n      </div>;\n  }\n  if (error) {\n    return <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <p className=\"text-red-600 mb-4\">{error}</p>\n          <Button onClick={() => window.location.reload()} variant=\"outline\">\n            重试\n          </Button>\n        </div>\n      </div>;\n  }\n  if (treatments.length === 0) {\n    return <>\n        <div className=\"flex items-center justify-center py-8\">\n          <div className=\"text-center\">\n            <IconStethoscope className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium mb-2\">{t('treatments.noTreatments')}</h3>\n            <p className=\"text-muted-foreground mb-4\">\n              开始添加您的第一个治疗项目。\n            </p>\n            <PermissionGate permission=\"canCreateTreatments\">\n              <Button onClick={handleNewTreatment}>\n                <IconPlus className=\"h-4 w-4 mr-2\" />\n                {t('treatments.newTreatment')}\n              </Button>\n            </PermissionGate>\n          </div>\n        </div>\n\n        <TreatmentFormDialog open={formDialogOpen} onOpenChange={setFormDialogOpen} treatment={editingTreatment} onSuccess={handleFormSuccess} />\n      </>;\n  }\n  return <>\n      <div className=\"space-y-4\">\n        {/* Header with Add Treatment button */}\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h3 className=\"text-lg font-medium\">\n              {treatments.length} {t('treatments.treatmentsCount')}\n            </h3>\n          </div>\n          <PermissionGate permission=\"canCreateTreatments\" data-sentry-element=\"PermissionGate\" data-sentry-source-file=\"treatments-list.tsx\">\n            <Button onClick={handleNewTreatment} data-sentry-element=\"Button\" data-sentry-source-file=\"treatments-list.tsx\">\n              <IconPlus className=\"h-4 w-4 mr-2\" data-sentry-element=\"IconPlus\" data-sentry-source-file=\"treatments-list.tsx\" />\n              {t('treatments.newTreatment')}\n            </Button>\n          </PermissionGate>\n        </div>\n\n        {/* Treatments Grid */}\n        <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n          {treatments.map(treatment => <div key={treatment.id} className=\"border rounded-lg p-4 space-y-3\">\n              <div className=\"flex items-start justify-between\">\n                <div className=\"space-y-1\">\n                  <h4 className=\"font-medium text-lg\">{treatment.name}</h4>\n                  {treatment.description && <p className=\"text-sm text-muted-foreground line-clamp-2\">\n                      {treatment.description}\n                    </p>}\n                </div>\n                <PermissionGate permission=\"canEditTreatments\">\n                  <DropdownMenu>\n                    <DropdownMenuTrigger asChild>\n                      <Button variant=\"ghost\" size=\"sm\">\n                        <IconEdit className=\"h-4 w-4\" />\n                      </Button>\n                    </DropdownMenuTrigger>\n                    <DropdownMenuContent>\n                      <DropdownMenuItem onClick={() => handleEditTreatment(treatment)}>\n                        <IconEdit className=\"h-4 w-4 mr-2\" />\n                        {t('common.actions.edit')}\n                      </DropdownMenuItem>\n                      <DropdownMenuItem onClick={() => {\n                    setTreatmentToDelete(treatment);\n                    setDeleteDialogOpen(true);\n                  }} className=\"text-red-600\">\n                        <IconTrash className=\"h-4 w-4 mr-2\" />\n                        {t('common.actions.delete')}\n                      </DropdownMenuItem>\n                    </DropdownMenuContent>\n                  </DropdownMenu>\n                </PermissionGate>\n              </div>\n\n              <div className=\"flex items-center justify-between text-sm\">\n                <div className=\"space-y-1\">\n                  <div className=\"flex items-center gap-2\">\n                    <span className=\"text-muted-foreground\">价格:</span>\n                    <span className=\"font-medium\">¥{treatment.defaultPrice?.toFixed(2) || '未设置'}</span>\n                  </div>\n                  {treatment.defaultDurationInMinutes && <div className=\"flex items-center gap-2\">\n                      <span className=\"text-muted-foreground\">时长:</span>\n                      <span>{treatment.defaultDurationInMinutes} 分钟</span>\n                    </div>}\n                </div>\n              </div>\n            </div>)}\n        </div>\n      </div>\n\n      <TreatmentFormDialog open={formDialogOpen} onOpenChange={setFormDialogOpen} treatment={editingTreatment} onSuccess={handleFormSuccess} data-sentry-element=\"TreatmentFormDialog\" data-sentry-source-file=\"treatments-list.tsx\" />\n\n      <ConfirmationDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen} title=\"删除治疗项目\" description={`您确定要删除\"${treatmentToDelete?.name}\"吗？此操作无法撤销。`} confirmText=\"删除\" variant=\"destructive\" onConfirm={handleDeleteConfirm} loading={actionLoading} data-sentry-element=\"ConfirmationDialog\" data-sentry-source-file=\"treatments-list.tsx\" />\n    </>;\n}", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["Form", "FormProvider", "FormFieldContext", "React", "FormField", "props", "Provider", "value", "name", "data-sentry-element", "data-sentry-component", "data-sentry-source-file", "Controller", "useFormField", "fieldContext", "itemContext", "FormItemContext", "getFieldState", "useFormContext", "formState", "useFormState", "fieldState", "id", "formItemId", "formDescriptionId", "formMessageId", "FormItem", "className", "div", "data-slot", "cn", "FormLabel", "error", "Label", "data-error", "htmlFor", "FormControl", "Slot", "aria-<PERSON><PERSON>", "aria-invalid", "FormDescription", "p", "FormMessage", "body", "String", "message", "children", "ConfirmationDialog", "open", "onOpenChange", "title", "description", "confirmText", "cancelText", "variant", "onConfirm", "loading", "AlertDialog", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Alert<PERSON><PERSON><PERSON>H<PERSON>er", "AlertDialogTitle", "AlertDialogDescription", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AlertDialogCancel", "disabled", "AlertDialogAction", "onClick", "TreatmentsPage", "userId", "auth", "_jsx", "<PERSON><PERSON><PERSON><PERSON>", "_jsxs", "h2", "IconStethoscope", "t", "TreatmentsList", "redirect", "Textarea", "textarea", "treatmentSchema", "z", "min", "max", "regex", "optional", "defaultPrice", "multipleOf", "defaultDurationInMinutes", "int", "TreatmentFormDialog", "treatment", "onSuccess", "setLoading", "useState", "isEditing", "form", "useForm", "resolver", "zodResolver", "defaultValues", "onSubmit", "data", "treatmentData", "undefined", "treatmentsApi", "update", "toast", "success", "create", "reset", "console", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "DialogDescription", "handleSubmit", "control", "render", "field", "Input", "placeholder", "type", "step", "onChange", "e", "parseFloat", "target", "parseInt", "<PERSON><PERSON><PERSON><PERSON>er", "<PERSON><PERSON>", "hasPermission", "useRole", "treatments", "setTreatments", "setError", "formDialogOpen", "setFormDialogOpen", "editingTreatment", "setEditingTreatment", "deleteDialogOpen", "setDeleteDialogOpen", "treatmentToDelete", "setTreatmentToDelete", "actionLoading", "setActionLoading", "fetchTreatments", "response", "getAll", "limit", "docs", "err", "handleNewTreatment", "handleEditTreatment", "handleDeleteConfirm", "delete", "handleFormSuccess", "window", "location", "reload", "length", "h3", "PermissionGate", "permission", "IconPlus", "map", "h4", "DropdownMenu", "DropdownMenuTrigger", "<PERSON><PERSON><PERSON><PERSON>", "size", "IconEdit", "DropdownMenuContent", "DropdownMenuItem", "IconTrash", "span", "toFixed"], "sourceRoot": ""}