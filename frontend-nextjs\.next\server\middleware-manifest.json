{"version": 3, "middleware": {"/": {"files": ["server/edge-instrumentation.js", "server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "eU_12uEuXAJZF9zaAMMTr", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "6iMNFxJQRw5kwyobQ2E/Q8u2QleJW6yuaZTizsNE3Gw=", "__NEXT_PREVIEW_MODE_ID": "25b575a03628525bafe1eaaa4616f23a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5850a5a3bd80ef67e5a96a9d090f2fba7a52dcadbce07480b5613187731af4c6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4ef3c7713c6f02a0b66df049d0b6c761bcdf4c234eed818594c769806e300cac"}}}, "functions": {}, "sortedMiddleware": ["/"]}