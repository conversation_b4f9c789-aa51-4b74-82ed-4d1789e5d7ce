{"version": 3, "middleware": {"/": {"files": ["server/edge-instrumentation.js", "server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "C4EHqlHQJa-V1240Z7COt", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "6iMNFxJQRw5kwyobQ2E/Q8u2QleJW6yuaZTizsNE3Gw=", "__NEXT_PREVIEW_MODE_ID": "169f4582ec6c239c0ddd7d6ab39c3a9e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "450ca7f95cbb12cf79019c89a4531e1b2b33a6a3fd042492e02d10abb68d8546", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "97b5966ee144572da806d7218c6cf8d4cc58197626a694ac81eb84fb90fe0f0f"}}}, "functions": {}, "sortedMiddleware": ["/"]}