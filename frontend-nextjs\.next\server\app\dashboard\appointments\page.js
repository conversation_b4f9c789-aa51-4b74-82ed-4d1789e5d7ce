try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="c82c2045-2efb-438f-a9e7-ccf51f7980b4",e._sentryDebugIdIdentifier="sentry-dbid-c82c2045-2efb-438f-a9e7-ccf51f7980b4")}catch(e){}(()=>{var e={};e.id=8876,e.ids=[8876],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4077:(e,t,n)=>{var r=n(4945);e.exports=function(e,t){var n=t?r(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)}},4849:(e,t,n)=>{var r=n(71879),a=n(96598);e.exports=function(e,t){return e&&r(t,a(t),e)}},4945:(e,t,n)=>{var r=n(25904);e.exports=function(e){var t=new e.constructor(e.byteLength);return new r(t).set(new r(e)),t}},6138:(e,t,n)=>{var r=n(13747);e.exports=function(e){return r(e)?void 0:e}},6526:(e,t,n)=>{Promise.resolve().then(n.bind(n,59484)),Promise.resolve().then(n.bind(n,67529))},8086:e=>{"use strict";e.exports=require("module")},8140:(e,t,n)=>{var r=n(56097),a=Object.create;e.exports=function(){function e(){}return function(t){if(!r(t))return{};if(a)return a(t);e.prototype=t;var n=new e;return e.prototype=void 0,n}}()},8432:function(e){e.exports=function(e,t){t.prototype.isSameOrBefore=function(e,t){return this.isSame(e,t)||this.isBefore(e,t)}}},8576:(e,t,n)=>{var r=n(20521),a=n(14634);e.exports=function(e){return a(e)&&"[object Map]"==r(e)}},8941:(e,t,n)=>{var r=n(4945);e.exports=function(e,t){var n=t?r(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11855:function(e){e.exports=function(e,t){t.prototype.isLeapYear=function(){return this.$y%4==0&&this.$y%100!=0||this.$y%400==0}}},16912:(e,t,n)=>{var r=n(11630),a=n(13228),o=n(7348),i=n(70717),s=Object.prototype,l=s.hasOwnProperty;e.exports=r(function(e,t){e=Object(e);var n=-1,r=t.length,c=r>2?t[2]:void 0;for(c&&o(t[0],t[1],c)&&(r=1);++n<r;)for(var u=t[n],d=i(u),f=-1,p=d.length;++f<p;){var m=d[f],h=e[m];(void 0===h||a(h,s[m])&&!l.call(e,m))&&(e[m]=u[m])}return e})},17403:(e,t,n)=>{"use strict";n.d(t,{AppointmentsList:()=>r});let r=(0,n(91611).registerClientReference)(function(){throw Error("Attempted to call AppointmentsList() from the server but AppointmentsList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\appointments\\appointments-list.tsx","AppointmentsList")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},24157:(e,t,n)=>{var r=n(8140),a=n(47559),o=n(9247);e.exports=function(e){return"function"!=typeof e.constructor||o(e)?{}:r(a(e))}},25239:(e,t,n)=>{var r=n(4945),a=n(4077),o=n(89733),i=n(63636),s=n(8941);e.exports=function(e,t,n){var l=e.constructor;switch(t){case"[object ArrayBuffer]":return r(e);case"[object Boolean]":case"[object Date]":return new l(+e);case"[object DataView]":return a(e,n);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return s(e,n);case"[object Map]":case"[object Set]":return new l;case"[object Number]":case"[object String]":return new l(e);case"[object RegExp]":return o(e);case"[object Symbol]":return i(e)}}},27279:(e,t,n)=>{"use strict";let r;n.r(t),n.d(t,{default:()=>b,generateImageMetadata:()=>g,generateMetadata:()=>v,generateViewport:()=>y});var a=n(63033),o=n(78869),i=n(79615),s=n(44508),l=n(83829),c=n(17403),u=(0,n(19e3).A)("outline","calendar","IconCalendar",[["path",{d:"M4 7a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12z",key:"svg-0"}],["path",{d:"M16 3v4",key:"svg-1"}],["path",{d:"M8 3v4",key:"svg-2"}],["path",{d:"M4 11h16",key:"svg-3"}],["path",{d:"M11 15h1",key:"svg-4"}],["path",{d:"M12 15v3",key:"svg-5"}]]),d=n(84758),f=n(19761);async function p(){let{userId:e}=await (0,i.j)();return e?(0,o.jsx)(l.A,{"data-sentry-element":"PageContainer","data-sentry-component":"AppointmentsPage","data-sentry-source-file":"page.tsx",children:(0,o.jsxs)("div",{className:"flex flex-1 flex-col space-y-4",children:[(0,o.jsx)("div",{className:"flex items-center justify-between",children:(0,o.jsxs)("div",{children:[(0,o.jsxs)("h2",{className:"text-2xl font-bold tracking-tight flex items-center gap-2",children:[(0,o.jsx)(u,{className:"size-6","data-sentry-element":"IconCalendar","data-sentry-source-file":"page.tsx"}),(0,d.t)("appointments.title")]}),(0,o.jsx)("p",{className:"text-muted-foreground",children:(0,d.t)("appointments.subtitle")})]})}),(0,o.jsx)(c.AppointmentsList,{"data-sentry-element":"AppointmentsList","data-sentry-source-file":"page.tsx"})]})}):(0,s.redirect)("/auth/sign-in")}let m={...a},h="workUnitAsyncStorage"in m?m.workUnitAsyncStorage:"requestAsyncStorage"in m?m.requestAsyncStorage:void 0;r=new Proxy(p,{apply:(e,t,n)=>{let r,a,o;try{let e=h?.getStore();r=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,o=e?.headers}catch(e){}return f.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/appointments",componentType:"Page",sentryTraceHeader:r,baggageHeader:a,headers:o}).apply(t,n)}});let v=void 0,g=void 0,y=void 0,b=r},27910:e=>{"use strict";e.exports=require("stream")},28096:function(e){e.exports=function(){"use strict";var e="minute",t=/[+-]\d\d(?::?\d\d)?/g,n=/([+-]|\d\d)/g;return function(r,a,o){var i=a.prototype;o.utc=function(e){var t={date:e,utc:!0,args:arguments};return new a(t)},i.utc=function(t){var n=o(this.toDate(),{locale:this.$L,utc:!0});return t?n.add(this.utcOffset(),e):n},i.local=function(){return o(this.toDate(),{locale:this.$L,utc:!1})};var s=i.parse;i.parse=function(e){e.utc&&(this.$u=!0),this.$utils().u(e.$offset)||(this.$offset=e.$offset),s.call(this,e)};var l=i.init;i.init=function(){if(this.$u){var e=this.$d;this.$y=e.getUTCFullYear(),this.$M=e.getUTCMonth(),this.$D=e.getUTCDate(),this.$W=e.getUTCDay(),this.$H=e.getUTCHours(),this.$m=e.getUTCMinutes(),this.$s=e.getUTCSeconds(),this.$ms=e.getUTCMilliseconds()}else l.call(this)};var c=i.utcOffset;i.utcOffset=function(r,a){var o=this.$utils().u;if(o(r))return this.$u?0:o(this.$offset)?c.call(this):this.$offset;if("string"==typeof r&&null===(r=function(e){void 0===e&&(e="");var r=e.match(t);if(!r)return null;var a=(""+r[0]).match(n)||["-",0,0],o=a[0],i=60*a[1]+ +a[2];return 0===i?0:"+"===o?i:-i}(r)))return this;var i=16>=Math.abs(r)?60*r:r,s=this;if(a)return s.$offset=i,s.$u=0===r,s;if(0!==r){var l=this.$u?this.toDate().getTimezoneOffset():-1*this.utcOffset();(s=this.local().add(i+l,e)).$offset=i,s.$x.$localOffset=l}else s=this.utc();return s};var u=i.format;i.format=function(e){var t=e||(this.$u?"YYYY-MM-DDTHH:mm:ss[Z]":"");return u.call(this,t)},i.valueOf=function(){var e=this.$utils().u(this.$offset)?0:this.$offset+(this.$x.$localOffset||this.$d.getTimezoneOffset());return this.$d.valueOf()-6e4*e},i.isUTC=function(){return!!this.$u},i.toISOString=function(){return this.toDate().toISOString()},i.toString=function(){return this.toDate().toUTCString()};var d=i.toDate;i.toDate=function(e){return"s"===e&&this.$offset?o(this.format("YYYY-MM-DD HH:mm:ss:SSS")).toDate():d.call(this)};var f=i.diff;i.diff=function(e,t,n){if(e&&this.$u===e.$u)return f.call(this,e,t,n);var r=this.local(),a=o(e).local();return f.call(r,a,t,n)}}}()},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31415:(e,t,n)=>{var r=n(56097),a=n(9247),o=n(52225),i=Object.prototype.hasOwnProperty;e.exports=function(e){if(!r(e))return o(e);var t=a(e),n=[];for(var s in e)"constructor"==s&&(t||!i.call(e,s))||n.push(s);return n}},31421:e=>{"use strict";e.exports=require("node:child_process")},32247:(e,t,n)=>{var r=n(10245),a=n(57413),o=n(78171),i=n(4849),s=n(54686),l=n(59338),c=n(34919),u=n(36543),d=n(33096),f=n(95242),p=n(72617),m=n(20521),h=n(76070),v=n(25239),g=n(24157),y=n(5517),b=n(58148),w=n(49546),x=n(56097),D=n(78588),S=n(96598),k=n(70717),E="[object Arguments]",j="[object Function]",N="[object Object]",M={};M[E]=M["[object Array]"]=M["[object ArrayBuffer]"]=M["[object DataView]"]=M["[object Boolean]"]=M["[object Date]"]=M["[object Float32Array]"]=M["[object Float64Array]"]=M["[object Int8Array]"]=M["[object Int16Array]"]=M["[object Int32Array]"]=M["[object Map]"]=M["[object Number]"]=M[N]=M["[object RegExp]"]=M["[object Set]"]=M["[object String]"]=M["[object Symbol]"]=M["[object Uint8Array]"]=M["[object Uint8ClampedArray]"]=M["[object Uint16Array]"]=M["[object Uint32Array]"]=!0,M["[object Error]"]=M[j]=M["[object WeakMap]"]=!1,e.exports=function e(t,n,T,O,C,A){var P,R=1&n,_=2&n,I=4&n;if(T&&(P=C?T(t,O,C,A):T(t)),void 0!==P)return P;if(!x(t))return t;var L=y(t);if(L){if(P=h(t),!R)return c(t,P)}else{var F=m(t),z=F==j||"[object GeneratorFunction]"==F;if(b(t))return l(t,R);if(F==N||F==E||z&&!C){if(P=_||z?{}:g(t),!R)return _?d(t,s(P,t)):u(t,i(P,t))}else{if(!M[F])return C?t:{};P=v(t,F,R)}}A||(A=new r);var q=A.get(t);if(q)return q;A.set(t,P),D(t)?t.forEach(function(r){P.add(e(r,n,T,r,t,A))}):w(t)&&t.forEach(function(r,a){P.set(a,e(r,n,T,a,t,A))});var H=I?_?p:f:_?k:S,$=L?void 0:H(t);return a($||t,function(r,a){$&&(r=t[a=r]),o(P,a,e(r,n,T,a,t,A))}),P}},32774:function(e){e.exports=function(e,t,n){t.prototype.isBetween=function(e,t,r,a){var o=n(e),i=n(t),s="("===(a=a||"()")[0],l=")"===a[1];return(s?this.isAfter(o,r):!this.isBefore(o,r))&&(l?this.isBefore(i,r):!this.isAfter(i,r))||(s?this.isBefore(o,r):!this.isAfter(o,r))&&(l?this.isAfter(i,r):!this.isBefore(i,r))}}},33096:(e,t,n)=>{var r=n(71879),a=n(36887);e.exports=function(e,t){return r(e,a(e),t)}},33873:e=>{"use strict";e.exports=require("path")},34919:e=>{e.exports=function(e,t){var n=-1,r=e.length;for(t||(t=Array(r));++n<r;)t[n]=e[n];return t}},36543:(e,t,n)=>{var r=n(71879),a=n(32980);e.exports=function(e,t){return r(e,a(e),t)}},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},36887:(e,t,n)=>{var r=n(73516),a=n(47559),o=n(32980),i=n(32941);e.exports=Object.getOwnPropertySymbols?function(e){for(var t=[];e;)r(t,o(e)),e=a(e);return t}:i},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},39979:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","refresh","IconRefresh",[["path",{d:"M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4",key:"svg-0"}],["path",{d:"M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4",key:"svg-1"}]])},41634:(e,t,n)=>{"use strict";n.d(t,{K:()=>o});var r=n(24443),a=n(75922);function o({open:e,onOpenChange:t,title:n,description:o,confirmText:i="确认",cancelText:s="取消",variant:l="default",onConfirm:c,loading:u=!1}){return(0,r.jsx)(a.Lt,{open:e,onOpenChange:t,"data-sentry-element":"AlertDialog","data-sentry-component":"ConfirmationDialog","data-sentry-source-file":"confirmation-dialog.tsx",children:(0,r.jsxs)(a.EO,{"data-sentry-element":"AlertDialogContent","data-sentry-source-file":"confirmation-dialog.tsx",children:[(0,r.jsxs)(a.wd,{"data-sentry-element":"AlertDialogHeader","data-sentry-source-file":"confirmation-dialog.tsx",children:[(0,r.jsx)(a.r7,{"data-sentry-element":"AlertDialogTitle","data-sentry-source-file":"confirmation-dialog.tsx",children:n}),(0,r.jsx)(a.$v,{"data-sentry-element":"AlertDialogDescription","data-sentry-source-file":"confirmation-dialog.tsx",children:o})]}),(0,r.jsxs)(a.ck,{"data-sentry-element":"AlertDialogFooter","data-sentry-source-file":"confirmation-dialog.tsx",children:[(0,r.jsx)(a.Zr,{disabled:u,"data-sentry-element":"AlertDialogCancel","data-sentry-source-file":"confirmation-dialog.tsx",children:s}),(0,r.jsx)(a.Rx,{onClick:c,disabled:u,className:"destructive"===l?"bg-destructive text-destructive-foreground hover:bg-destructive/90":"","data-sentry-element":"AlertDialogAction","data-sentry-source-file":"confirmation-dialog.tsx",children:u?"加载中...":i})]})]})})}},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},46932:(e,t,n)=>{var r=n(46946),a=n(26617),o=n(13653);e.exports=function(e){return o(a(e,void 0,r),e+"")}},46946:(e,t,n)=>{var r=n(89676);e.exports=function(e){return(null==e?0:e.length)?r(e,1):[]}},48161:e=>{"use strict";e.exports=require("node:os")},49546:(e,t,n)=>{var r=n(8576),a=n(14001),o=n(52645),i=o&&o.isMap;e.exports=i?a(i):r},52154:function(e){e.exports=function(e,t,n){var r=t.prototype,a=function(e){return e&&(e.indexOf?e:e.s)},o=function(e,t,n,r,o){var i=e.name?e:e.$locale(),s=a(i[t]),l=a(i[n]),c=s||l.map(function(e){return e.slice(0,r)});if(!o)return c;var u=i.weekStart;return c.map(function(e,t){return c[(t+(u||0))%7]})},i=function(){return n.Ls[n.locale()]},s=function(e,t){return e.formats[t]||e.formats[t.toUpperCase()].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(e,t,n){return t||n.slice(1)})},l=function(){var e=this;return{months:function(t){return t?t.format("MMMM"):o(e,"months")},monthsShort:function(t){return t?t.format("MMM"):o(e,"monthsShort","months",3)},firstDayOfWeek:function(){return e.$locale().weekStart||0},weekdays:function(t){return t?t.format("dddd"):o(e,"weekdays")},weekdaysMin:function(t){return t?t.format("dd"):o(e,"weekdaysMin","weekdays",2)},weekdaysShort:function(t){return t?t.format("ddd"):o(e,"weekdaysShort","weekdays",3)},longDateFormat:function(t){return s(e.$locale(),t)},meridiem:this.$locale().meridiem,ordinal:this.$locale().ordinal}};r.localeData=function(){return l.bind(this)()},n.localeData=function(){var e=i();return{firstDayOfWeek:function(){return e.weekStart||0},weekdays:function(){return n.weekdays()},weekdaysShort:function(){return n.weekdaysShort()},weekdaysMin:function(){return n.weekdaysMin()},months:function(){return n.months()},monthsShort:function(){return n.monthsShort()},longDateFormat:function(t){return s(e,t)},meridiem:e.meridiem,ordinal:e.ordinal}},n.months=function(){return o(i(),"months")},n.monthsShort=function(){return o(i(),"monthsShort","months",3)},n.weekdays=function(e){return o(i(),"weekdays",null,null,e)},n.weekdaysShort=function(e){return o(i(),"weekdaysShort","weekdays",3,e)},n.weekdaysMin=function(e){return o(i(),"weekdaysMin","weekdays",2,e)}}},52225:e=>{e.exports=function(e){var t=[];if(null!=e)for(var n in Object(e))t.push(n);return t}},52326:(e,t,n)=>{var r=n(20521),a=n(14634);e.exports=function(e){return a(e)&&"[object Set]"==r(e)}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},53360:function(e){e.exports=function(){"use strict";var e={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"};return function(t,n,r){var a=n.prototype,o=a.format;r.en.formats=e,a.format=function(t){void 0===t&&(t="YYYY-MM-DDTHH:mm:ssZ");var n,r,a=this.$locale().formats,i=(n=t,r=void 0===a?{}:a,n.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,function(t,n,a){var o=a&&a.toUpperCase();return n||r[a]||e[a]||r[o].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(e,t,n){return t||n.slice(1)})}));return o.call(this,i)}}}()},54686:(e,t,n)=>{var r=n(71879),a=n(70717);e.exports=function(e,t){return e&&r(t,a(t),e)}},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57413:e=>{e.exports=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r&&!1!==t(e[n],n,e););return e}},57975:e=>{"use strict";e.exports=require("node:util")},59338:(e,t,n)=>{e=n.nmd(e);var r=n(60753),a=t&&!t.nodeType&&t,o=a&&e&&!e.nodeType&&e,i=o&&o.exports===a?r.Buffer:void 0,s=i?i.allocUnsafe:void 0;e.exports=function(e,t){if(t)return e.slice();var n=e.length,r=s?s(n):new e.constructor(n);return e.copy(r),r}},59484:(e,t,n)=>{"use strict";n.d(t,{AppointmentsList:()=>iB});var r,a,o={};n.r(o),n.d(o,{add:()=>eZ,century:()=>tu,date:()=>ti,day:()=>to,decade:()=>tc,diff:()=>tf,endOf:()=>e1,eq:()=>e2,gt:()=>e3,gte:()=>e5,hours:()=>ta,inRange:()=>te,lt:()=>e8,lte:()=>e7,max:()=>e9,milliseconds:()=>tt,min:()=>e6,minutes:()=>tr,month:()=>ts,neq:()=>e4,seconds:()=>tn,startOf:()=>e0,subtract:()=>eJ,weekday:()=>td,year:()=>tl});var i=n(24443),s=n(60222),l=n.n(s),c=n(10531),u=n(33284),d=n(74482),f=n(69322),p=n(4826),m=n(49958),h=n(9752),v=n(95748),g=n(20866),y=n(46244),b=(0,y.A)("outline","table","IconTable",[["path",{d:"M3 5a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-14z",key:"svg-0"}],["path",{d:"M3 10h18",key:"svg-1"}],["path",{d:"M10 3v18",key:"svg-2"}]]),w=n(57602),x=n(74916),D=n(68882),S=n(92585),k=n(95550),E=n(80395),j=n(13875),N=n(4224),M=n(40574),T=n(61780),O=n(26882),C=n(19342),A=n(23032),P=n(5149),R=n(1378),_=n(72595),I=n(85001),L=n(71057),F=n(59077),z=n(49111);let q={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},H=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,$=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,W=/^([+-])(\d{2})(?::?(\d{2}))?$/;function Y(e){return e?parseInt(e):1}function U(e){return e&&parseFloat(e.replace(",","."))||0}let V=[31,null,31,30,31,30,31,31,30,31,30,31];function B(e){return e%400==0||e%4==0&&e%100!=0}let G={lessThanXSeconds:{one:"不到 1 秒",other:"不到 {{count}} 秒"},xSeconds:{one:"1 秒",other:"{{count}} 秒"},halfAMinute:"半分钟",lessThanXMinutes:{one:"不到 1 分钟",other:"不到 {{count}} 分钟"},xMinutes:{one:"1 分钟",other:"{{count}} 分钟"},xHours:{one:"1 小时",other:"{{count}} 小时"},aboutXHours:{one:"大约 1 小时",other:"大约 {{count}} 小时"},xDays:{one:"1 天",other:"{{count}} 天"},aboutXWeeks:{one:"大约 1 个星期",other:"大约 {{count}} 个星期"},xWeeks:{one:"1 个星期",other:"{{count}} 个星期"},aboutXMonths:{one:"大约 1 个月",other:"大约 {{count}} 个月"},xMonths:{one:"1 个月",other:"{{count}} 个月"},aboutXYears:{one:"大约 1 年",other:"大约 {{count}} 年"},xYears:{one:"1 年",other:"{{count}} 年"},overXYears:{one:"超过 1 年",other:"超过 {{count}} 年"},almostXYears:{one:"将近 1 年",other:"将近 {{count}} 年"}};var K=n(54897);let X={date:(0,K.k)({formats:{full:"y'年'M'月'd'日' EEEE",long:"y'年'M'月'd'日'",medium:"yyyy-MM-dd",short:"yy-MM-dd"},defaultWidth:"full"}),time:(0,K.k)({formats:{full:"zzzz a h:mm:ss",long:"z a h:mm:ss",medium:"a h:mm:ss",short:"a h:mm"},defaultWidth:"full"}),dateTime:(0,K.k)({formats:{full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},defaultWidth:"full"})};var Q=n(48757),Z=n(88957);function J(e,t,n){let r="eeee p";return!function(e,t,n){let[r,a]=(0,Q.x)(n?.in,e,t);return+(0,Z.k)(r,n)==+(0,Z.k)(a,n)}(e,t,n)?e.getTime()>t.getTime()?"'下个'"+r:"'上个'"+r:r}let ee={lastWeek:J,yesterday:"'昨天' p",today:"'今天' p",tomorrow:"'明天' p",nextWeek:J,other:"PP p"};var et=n(49531);let en={ordinalNumber:(e,t)=>{let n=Number(e);switch(t?.unit){case"date":return n.toString()+"日";case"hour":return n.toString()+"时";case"minute":return n.toString()+"分";case"second":return n.toString()+"秒";default:return"第 "+n.toString()}},era:(0,et.o)({values:{narrow:["前","公元"],abbreviated:["前","公元"],wide:["公元前","公元"]},defaultWidth:"wide"}),quarter:(0,et.o)({values:{narrow:["1","2","3","4"],abbreviated:["第一季","第二季","第三季","第四季"],wide:["第一季度","第二季度","第三季度","第四季度"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:(0,et.o)({values:{narrow:["一","二","三","四","五","六","七","八","九","十","十一","十二"],abbreviated:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],wide:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]},defaultWidth:"wide"}),day:(0,et.o)({values:{narrow:["日","一","二","三","四","五","六"],short:["日","一","二","三","四","五","六"],abbreviated:["周日","周一","周二","周三","周四","周五","周六"],wide:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"]},defaultWidth:"wide"}),dayPeriod:(0,et.o)({values:{narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},defaultWidth:"wide",formattingValues:{narrow:{am:"上",pm:"下",midnight:"凌晨",noon:"午",morning:"早",afternoon:"下午",evening:"晚",night:"夜"},abbreviated:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"},wide:{am:"上午",pm:"下午",midnight:"凌晨",noon:"中午",morning:"早晨",afternoon:"中午",evening:"晚上",night:"夜间"}},defaultFormattingWidth:"wide"})};var er=n(95355);let ea={code:"zh-CN",formatDistance:(e,t,n)=>{let r,a=G[e];if(r="string"==typeof a?a:1===t?a.one:a.other.replace("{{count}}",String(t)),n?.addSuffix)if(n.comparison&&n.comparison>0)return r+"内";else return r+"前";return r},formatLong:X,formatRelative:(e,t,n,r)=>{let a=ee[e];return"function"==typeof a?a(t,n,r):a},localize:en,match:{ordinalNumber:(0,n(6653).K)({matchPattern:/^(第\s*)?\d+(日|时|分|秒)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:(0,er.A)({matchPatterns:{narrow:/^(前)/i,abbreviated:/^(前)/i,wide:/^(公元前|公元)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^(前)/i,/^(公元)/i]},defaultParseWidth:"any"}),quarter:(0,er.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^第[一二三四]刻/i,wide:/^第[一二三四]刻钟/i},defaultMatchWidth:"wide",parsePatterns:{any:[/(1|一)/i,/(2|二)/i,/(3|三)/i,/(4|四)/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:(0,er.A)({matchPatterns:{narrow:/^(一|二|三|四|五|六|七|八|九|十[二一])/i,abbreviated:/^(一|二|三|四|五|六|七|八|九|十[二一]|\d|1[12])月/i,wide:/^(一|二|三|四|五|六|七|八|九|十[二一])月/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^一/i,/^二/i,/^三/i,/^四/i,/^五/i,/^六/i,/^七/i,/^八/i,/^九/i,/^十(?!(一|二))/i,/^十一/i,/^十二/i],any:[/^一|1/i,/^二|2/i,/^三|3/i,/^四|4/i,/^五|5/i,/^六|6/i,/^七|7/i,/^八|8/i,/^九|9/i,/^十(?!(一|二))|10/i,/^十一|11/i,/^十二|12/i]},defaultParseWidth:"any"}),day:(0,er.A)({matchPatterns:{narrow:/^[一二三四五六日]/i,short:/^[一二三四五六日]/i,abbreviated:/^周[一二三四五六日]/i,wide:/^星期[一二三四五六日]/i},defaultMatchWidth:"wide",parsePatterns:{any:[/日/i,/一/i,/二/i,/三/i,/四/i,/五/i,/六/i]},defaultParseWidth:"any"}),dayPeriod:(0,er.A)({matchPatterns:{any:/^(上午?|下午?|午夜|[中正]午|早上?|下午|晚上?|凌晨|)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^上午?/i,pm:/^下午?/i,midnight:/^午夜/i,noon:/^[中正]午/i,morning:/^早上/i,afternoon:/^下午/i,evening:/^晚上?/i,night:/^凌晨/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:4}};function eo(e){return(0,N.GP)(e,"yyyy年MM月dd日 HH:mm",{locale:ea})}let ei={month:"月视图",week:"周视图",work_week:"工作周",day:"日视图",agenda:"议程",today:"今天",previous:"上一个",next:"下一个",showMore:e=>`+${e} 更多`},es={appointment:{created:e=>{let t="consultation"===e.appointmentType?"咨询预约":"治疗预约";I.toast.success(`${t}创建成功！`,{description:`患者: ${e.patient.fullName} | 时间: ${eo(new Date(e.appointmentDate))}`,duration:4e3})},updated:e=>{let t="consultation"===e.appointmentType?"咨询预约":"治疗预约";I.toast.success(`${t}更新成功！`,{description:`患者: ${e.patient.fullName} | 时间: ${eo(new Date(e.appointmentDate))}`,duration:4e3})},deleted:e=>{I.toast.success(`预约删除成功！`,{description:`患者: ${e}`,duration:4e3})},statusChanged:(e,t,n)=>{let r={scheduled:"已预约",confirmed:"已确认",completed:"已完成",cancelled:"已取消","no-show":"未到诊"},a=r[t]||t,o=r[n]||n;I.toast.success(`预约状态更新成功！`,{description:`${e.patient.fullName}: ${a} → ${o}`,duration:4e3})},batchStatusChanged:(e,t)=>{I.toast.success(`批量状态更新成功！`,{description:`${e} 个预约已更新为 ${{confirmed:"已确认",completed:"已完成",cancelled:"已取消"}[t]||t}`,duration:4e3})}},reminder:{upcoming:(e,t)=>{let n=t<60?`${t} 分钟后`:`${Math.floor(t/60)} 小时后`;I.toast.info(`预约提醒`,{description:`${e.patient.fullName} 的预约将在 ${n} 开始`,duration:8e3})},overdue:e=>{I.toast.error(`预约超时`,{description:`${e.patient.fullName} 的预约时间已过，状态为: ${e.status}`,duration:1e4})},reminderSet:(e,t)=>{I.toast.success(`预约提醒已设置`,{description:`将在 ${t} 提醒 ${e.patient.fullName} 的预约`,duration:4e3})}},error:{fetchFailed:()=>{I.toast.error(`获取预约数据失败`,{description:"请检查网络连接后重试",duration:5e3})},saveFailed:e=>{I.toast.error(`${e?"更新":"创建"}预约失败`,{description:"请稍后重试或联系管理员",duration:5e3})},deleteFailed:()=>{I.toast.error(`删除预约失败`,{description:"请稍后重试或联系管理员",duration:5e3})},permissionDenied:e=>{I.toast.error(`权限不足`,{description:`您没有权限执行: ${e}`,duration:5e3})},networkError:()=>{I.toast.error(`网络连接失败`,{description:"请检查网络连接后重试",duration:5e3})}}},el=j.Ik({appointmentType:j.k5(["consultation","treatment"],{required_error:"Please select appointment type"}),appointmentDate:j.p6({required_error:"Please select an appointment date and time"}).refine(e=>e>=new Date(new Date().getTime()-864e5),{message:"Appointment date cannot be more than 24 hours in the past"}),patientId:j.Yj().min(1,"Please select a patient from the dropdown"),treatmentId:j.Yj().optional(),practitionerId:j.Yj().min(1,"Please select a practitioner from the dropdown"),price:j.ai().min(0,"Price must be $0 or greater").max(1e4,"Price cannot exceed $10,000").multipleOf(.01,"Price must be a valid currency amount").optional(),durationInMinutes:j.ai().min(5,"Duration must be at least 5 minutes").max(480,"Duration cannot exceed 8 hours (480 minutes)").int("Duration must be a whole number of minutes"),status:j.k5(["scheduled","confirmed","completed","cancelled","no-show"],{required_error:"Please select an appointment status"}),consultationType:j.k5(["initial","follow-up","price-inquiry"]).optional(),interestedTreatments:j.YO(j.Yj()).optional()}).refine(e=>"treatment"!==e.appointmentType||!!e.treatmentId,{message:"Treatment appointments must have a treatment selected",path:["treatmentId"]});function ec({open:e,onOpenChange:t,appointment:n,onSuccess:r}){let[a,o]=(0,s.useState)(!1),[l,c]=(0,s.useState)([]),[d,f]=(0,s.useState)([]),[p,m]=(0,s.useState)([]),[h,v]=(0,s.useState)([]),[g,y]=(0,s.useState)(null),[b,x]=(0,s.useState)([]),D=!!n,S=(0,k.mN)({resolver:(0,E.u)(el),defaultValues:{appointmentType:n?.appointmentType||"consultation",appointmentDate:n?new Date(n.appointmentDate):new Date,patientId:n?.patient.id||"",treatmentId:n?.treatment?.id||"",practitionerId:n?.practitioner.id||"",price:n?.price||0,durationInMinutes:n?.durationInMinutes||30,status:n?.status||"scheduled",consultationType:n?.consultationType||"initial",interestedTreatments:n?.interestedTreatments?.map(e=>e.id)||[]}});S.watch("treatmentId"),S.watch("appointmentDate"),S.watch("durationInMinutes"),S.watch("practitionerId");let j=async e=>{o(!0);try{let a,o={appointmentType:e.appointmentType,appointmentDate:e.appointmentDate.toISOString(),patient:e.patientId,treatment:e.treatmentId||void 0,practitioner:e.practitionerId,price:e.price||0,durationInMinutes:e.durationInMinutes,status:e.status,consultationType:"consultation"===e.appointmentType?e.consultationType:void 0,interestedTreatments:"consultation"===e.appointmentType?e.interestedTreatments:void 0};D?(a=await w.RG.update(n.id,o),es.appointment.updated(a)):(a=await w.RG.create(o),es.appointment.created(a)),r?.(),t(!1),S.reset()}catch(e){console.error("Failed to save appointment:",e),es.error.saveFailed(D)}finally{o(!1)}};return(0,i.jsx)(T.lG,{open:e,onOpenChange:t,"data-sentry-element":"Dialog","data-sentry-component":"AppointmentFormDialog","data-sentry-source-file":"appointment-form-dialog.tsx",children:(0,i.jsxs)(T.Cf,{className:"sm:max-w-[600px]","data-sentry-element":"DialogContent","data-sentry-source-file":"appointment-form-dialog.tsx",children:[(0,i.jsxs)(T.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"appointment-form-dialog.tsx",children:[(0,i.jsx)(T.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"appointment-form-dialog.tsx",children:D?"Edit Appointment":"New Appointment"}),(0,i.jsx)(T.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"appointment-form-dialog.tsx",children:D?"Update the appointment details below.":"Fill in the details to schedule a new appointment."})]}),(0,i.jsx)(O.lV,{...S,"data-sentry-element":"Form","data-sentry-source-file":"appointment-form-dialog.tsx",children:(0,i.jsxs)("form",{onSubmit:S.handleSubmit(j),className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,i.jsx)(O.zB,{control:S.control,name:"patientId",render:({field:e})=>(0,i.jsxs)(O.eI,{children:[(0,i.jsx)(O.lR,{children:"Patient"}),(0,i.jsxs)(A.l6,{onValueChange:e.onChange,defaultValue:e.value,children:[(0,i.jsx)(O.MJ,{children:(0,i.jsx)(A.bq,{children:(0,i.jsx)(A.yv,{placeholder:"Select patient"})})}),(0,i.jsx)(A.gC,{children:l.map(e=>(0,i.jsx)(A.eb,{value:e.id,children:e.fullName},e.id))})]}),(0,i.jsx)(O.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"appointment-form-dialog.tsx"}),(0,i.jsx)(O.zB,{control:S.control,name:"treatmentId",render:({field:e})=>(0,i.jsxs)(O.eI,{children:[(0,i.jsx)(O.lR,{children:"Treatment"}),(0,i.jsxs)(A.l6,{onValueChange:e.onChange,defaultValue:e.value,children:[(0,i.jsx)(O.MJ,{children:(0,i.jsx)(A.bq,{children:(0,i.jsx)(A.yv,{placeholder:"Select treatment"})})}),(0,i.jsx)(A.gC,{children:d.map(e=>(0,i.jsx)(A.eb,{value:e.id,children:e.name},e.id))})]}),(0,i.jsx)(O.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"appointment-form-dialog.tsx"})]}),(0,i.jsx)(O.zB,{control:S.control,name:"appointmentDate",render:({field:e})=>(0,i.jsxs)(O.eI,{className:"flex flex-col",children:[(0,i.jsx)(O.lR,{children:"Appointment Date & Time"}),(0,i.jsxs)(P.AM,{children:[(0,i.jsx)(P.Wv,{asChild:!0,children:(0,i.jsx)(O.MJ,{children:(0,i.jsxs)(u.$,{variant:"outline",className:(0,_.cn)("w-full pl-3 text-left font-normal",!e.value&&"text-muted-foreground"),children:[e.value?(0,N.GP)(e.value,"PPP p"):(0,i.jsx)("span",{children:"Pick a date and time"}),(0,i.jsx)(M.A,{className:"ml-auto h-4 w-4 opacity-50"})]})})}),(0,i.jsx)(P.hl,{className:"w-auto p-0",align:"start",children:(0,i.jsx)(R.V,{mode:"single",selected:e.value,onSelect:e.onChange,disabled:e=>e<new Date||e<new Date("1900-01-01"),initialFocus:!0})})]}),(0,i.jsx)(O.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"appointment-form-dialog.tsx"}),(0,i.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,i.jsx)(O.zB,{control:S.control,name:"price",render:({field:e})=>(0,i.jsxs)(O.eI,{children:[(0,i.jsx)(O.lR,{children:"Price ($)"}),(0,i.jsx)(O.MJ,{children:(0,i.jsx)(C.p,{type:"number",step:"0.01",...e,onChange:t=>e.onChange(parseFloat(t.target.value)||0)})}),(0,i.jsx)(O.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"appointment-form-dialog.tsx"}),(0,i.jsx)(O.zB,{control:S.control,name:"durationInMinutes",render:({field:e})=>(0,i.jsxs)(O.eI,{children:[(0,i.jsx)(O.lR,{children:"Duration (min)"}),(0,i.jsx)(O.MJ,{children:(0,i.jsx)(C.p,{type:"number",...e,onChange:t=>e.onChange(parseInt(t.target.value)||0)})}),(0,i.jsx)(O.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"appointment-form-dialog.tsx"}),(0,i.jsx)(O.zB,{control:S.control,name:"status",render:({field:e})=>(0,i.jsxs)(O.eI,{children:[(0,i.jsx)(O.lR,{children:"Status"}),(0,i.jsxs)(A.l6,{onValueChange:e.onChange,defaultValue:e.value,children:[(0,i.jsx)(O.MJ,{children:(0,i.jsx)(A.bq,{children:(0,i.jsx)(A.yv,{})})}),(0,i.jsxs)(A.gC,{children:[(0,i.jsx)(A.eb,{value:"scheduled",children:"Scheduled"}),(0,i.jsx)(A.eb,{value:"completed",children:"Completed"}),(0,i.jsx)(A.eb,{value:"cancelled",children:"Cancelled"})]})]}),(0,i.jsx)(O.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"appointment-form-dialog.tsx"})]}),(0,i.jsx)(O.zB,{control:S.control,name:"practitionerId",render:({field:e})=>(0,i.jsxs)(O.eI,{children:[(0,i.jsx)(O.lR,{children:"Practitioner"}),(0,i.jsxs)(A.l6,{onValueChange:e.onChange,defaultValue:e.value,children:[(0,i.jsx)(O.MJ,{children:(0,i.jsx)(A.bq,{children:(0,i.jsx)(A.yv,{placeholder:"Select practitioner"})})}),(0,i.jsx)(A.gC,{children:p.map(e=>(0,i.jsxs)(A.eb,{value:e.id,children:[e.firstName," ",e.lastName]},e.id))})]}),(0,i.jsx)(O.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"appointment-form-dialog.tsx"}),(0,i.jsxs)(T.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"appointment-form-dialog.tsx",children:[(0,i.jsx)(u.$,{type:"button",variant:"outline",onClick:()=>t(!1),disabled:a,"data-sentry-element":"Button","data-sentry-source-file":"appointment-form-dialog.tsx",children:"Cancel"}),(0,i.jsx)(u.$,{type:"submit",disabled:a,"data-sentry-element":"Button","data-sentry-source-file":"appointment-form-dialog.tsx",children:a?"Saving...":D?"Update":"Create"})]})]})})]})})}function eu(e){return(eu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ed(e){var t=function(e,t){if("object"!=eu(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=eu(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eu(t)?t:t+""}function ef(e,t,n){return(t=ed(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ep(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function em(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ep(Object(n),!0).forEach(function(t){ef(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ep(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function eh(e,t){if(null==e)return{};var n={};for(var r in e)if(({}).hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}function ev(e,t){if(null==e)return{};var n,r,a=eh(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],-1===t.indexOf(n)&&({}).propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function eg(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function ey(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,ed(r.key),r)}}function eb(e,t,n){return t&&ey(e.prototype,t),n&&ey(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function ew(e){return(ew=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function ex(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(ex=function(){return!!e})()}function eD(e,t,n){return t=ew(t),function(e,t){if(t&&("object"==eu(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e,ex()?Reflect.construct(t,n||[],ew(e).constructor):t.apply(e,n))}function eS(e,t){return(eS=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function ek(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&eS(e,t)}function eE(e){if(Array.isArray(e))return e}function ej(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function eN(e,t){if(e){if("string"==typeof e)return ej(e,t);var n=({}).toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ej(e,t):void 0}}function eM(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function eT(e,t){return eE(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,s=[],l=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=o.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){c=!0,a=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(c)throw a}}return s}}(e,t)||eN(e,t)||eM()}let eO=function(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=function e(t){var n,r,a="";if("string"==typeof t||"number"==typeof t)a+=t;else if("object"==typeof t)if(Array.isArray(t))for(n=0;n<t.length;n++)t[n]&&(r=e(t[n]))&&(a&&(a+=" "),a+=r);else for(n in t)t[n]&&(a&&(a+=" "),a+=n);return a}(e))&&(r&&(r+=" "),r+=t);return r};var eC=n(62889),eA=n.n(eC),eP=function(){};function eR(e){return"default"+e.charAt(0).toUpperCase()+e.substr(1)}function e_(){return(e_=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}function eI(){var e=this.constructor.getDerivedStateFromProps(this.props,this.state);null!=e&&this.setState(e)}function eL(e){this.setState((function(t){var n=this.constructor.getDerivedStateFromProps(e,t);return null!=n?n:null}).bind(this))}function eF(e,t){try{var n=this.props,r=this.state;this.props=e,this.state=t,this.__reactInternalSnapshotFlag=!0,this.__reactInternalSnapshot=this.getSnapshotBeforeUpdate(n,r)}finally{this.props=n,this.state=r}}eI.__suppressDeprecationWarning=!0,eL.__suppressDeprecationWarning=!0,eF.__suppressDeprecationWarning=!0;var ez=n(71876),eq=n.n(ez),eH="milliseconds",e$="seconds",eW="minutes",eY="hours",eU="week",eV="month",eB="year",eG="decade",eK="century",eX={milliseconds:1,seconds:1e3,minutes:6e4,hours:36e5,day:864e5,week:6048e5},eQ={month:1,year:12,decade:120,century:1200};function eZ(e,t,n){var r,a,o,i,s,l,c,u,d,f,p,m,h,v,g,y,b;switch(e=new Date(e),n){case eH:case e$:case eW:case eY:case"day":case eU:return a=new Date(+(r=e)+t*eX[n]),o=r,i=a,s=o.getTimezoneOffset(),l=i.getTimezoneOffset(),new Date(+i+(l-s)*eX.minutes);case eV:case eB:case eG:case eK:return c=e,u=t*eQ[n],f=c.getFullYear(),p=c.getMonth(),m=c.getDate(),v=Math.trunc((h=12*f+p+u)/12),g=h%12,y=Math.min(m,[31,(d=v)%4==0&&d%100!=0||d%400==0?29:28,31,30,31,30,31,31,30,31,30,31][g]),(b=new Date(c)).setFullYear(v),b.setDate(1),b.setMonth(g),b.setDate(y),b}throw TypeError('Invalid units: "'+n+'"')}function eJ(e,t,n){return eZ(e,-t,n)}function e0(e,t,n){switch(e=new Date(e),t){case eK:case eG:case eB:e=ts(e,0);case eV:e=ti(e,1);case eU:case"day":e=ta(e,0);case eY:e=tr(e,0);case eW:e=tn(e,0);case e$:e=tt(e,0)}return t===eG&&(e=eJ(e,tl(e)%10,"year")),t===eK&&(e=eJ(e,tl(e)%100,"year")),t===eU&&(e=td(e,0,n)),e}function e1(e,t,n){switch(e=e0(e=new Date(e),t,n),t){case eK:case eG:case eB:case eV:case eU:(e=eJ(e=eZ(e,1,t),1,"day")).setHours(23,59,59,999);break;case"day":e.setHours(23,59,59,999);break;case eY:case eW:case e$:e=eJ(e=eZ(e,1,t),1,eH)}return e}var e2=tm(function(e,t){return e===t}),e4=tm(function(e,t){return e!==t}),e3=tm(function(e,t){return e>t}),e5=tm(function(e,t){return e>=t}),e8=tm(function(e,t){return e<t}),e7=tm(function(e,t){return e<=t});function e6(){return new Date(Math.min.apply(Math,arguments))}function e9(){return new Date(Math.max.apply(Math,arguments))}function te(e,t,n,r){return r=r||"day",(!t||e5(e,t,r))&&(!n||e7(e,n,r))}var tt=tp("Milliseconds"),tn=tp("Seconds"),tr=tp("Minutes"),ta=tp("Hours"),to=tp("Day"),ti=tp("Date"),ts=tp("Month"),tl=tp("FullYear");function tc(e,t){return void 0===t?tl(e0(e,eG)):eZ(e,t+10,eB)}function tu(e,t){return void 0===t?tl(e0(e,eK)):eZ(e,t+100,eB)}function td(e,t,n){var r=(to(e)+7-(n||0))%7;return void 0===t?r:eZ(e,t-r,"day")}function tf(e,t,n,r){var a,o,i;switch(n){case eH:case e$:case eW:case eY:case"day":case eU:a=t.getTime()-e.getTime();break;case eV:case eB:case eG:case eK:a=(tl(t)-tl(e))*12+ts(t)-ts(e);break;default:throw TypeError('Invalid units: "'+n+'"')}switch(n){case eH:o=1;break;case e$:o=1e3;break;case eW:o=6e4;break;case eY:o=36e5;break;case"day":o=864e5;break;case eU:o=6048e5;break;case eV:o=1;break;case eB:o=12;break;case eG:o=120;break;case eK:o=1200;break;default:throw TypeError('Invalid units: "'+n+'"')}return i=a/o,r?i:Math.round(i)}function tp(e){var t=function(e){switch(e){case"Milliseconds":return 36e5;case"Seconds":return 3600;case"Minutes":return 60;case"Hours":return 1;default:return null}}(e);return function(n,r){if(void 0===r)return n["get"+e]();var a=new Date(n);return a["set"+e](r),t&&a["get"+e]()!=r&&("Hours"===e||r>=t&&a.getHours()-n.getHours()<Math.floor(r/t))&&a["set"+e](r+t),a}}function tm(e){return function(t,n,r){return e(+e0(t,r),+e0(n,r))}}function th(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function tv(e){return function(e){if(Array.isArray(e))return ej(e)}(e)||th(e)||eN(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var tg=n(75145),ty=n.n(tg);function tb(e){return e&&e.ownerDocument||document}var tw=/([A-Z])/g,tx=/^ms-/;function tD(e){return e.replace(tw,"-$1").toLowerCase().replace(tx,"-ms-")}var tS=/^((translate|rotate|scale)(X|Y|Z|3d)?|matrix(3d)?|perspective|skew(X|Y)?)$/i;let tk=function(e,t){var n,r,a,o="",i="";if("string"==typeof t){return e.style.getPropertyValue(tD(t))||((a=tb(n=e))&&a.defaultView||window).getComputedStyle(n,void 0).getPropertyValue(tD(t))}Object.keys(t).forEach(function(n){var r=t[n];r||0===r?n&&tS.test(n)?i+=n+"("+r+") ":o+=tD(n)+": "+r+";":e.style.removeProperty(tD(n))}),i&&(o+="transform: "+i+";"),e.style.cssText+=";"+o};function tE(e,t){return e.contains?e.contains(t):e.compareDocumentPosition?e===t||!!(16&e.compareDocumentPosition(t)):void 0}function tj(e){return"window"in e&&e.window===e?e:"nodeType"in e&&e.nodeType===document.DOCUMENT_NODE&&(e.defaultView||!1)}function tN(e){var t="pageXOffset"===e?"scrollLeft":"scrollTop";return function(n,r){var a=tj(n);if(void 0===r)return a?a[e]:n[t];a?a.scrollTo(a[e],r):n[t]=r}}let tM=tN("pageXOffset"),tT=tN("pageYOffset");function tO(e){var t=tb(e),n={top:0,left:0,height:0,width:0},r=t&&t.documentElement;return r&&tE(r,e)?(void 0!==e.getBoundingClientRect&&(n=e.getBoundingClientRect()),n={top:n.top+tT(r)-(r.clientTop||0),left:n.left+tM(r)-(r.clientLeft||0),width:n.width,height:n.height}):n}function tC(e,t){var n,r={top:0,left:0};if("fixed"===tk(e,"position"))n=e.getBoundingClientRect();else{var a=t||function(e){for(var t,n=tb(e),r=e&&e.offsetParent;(t=r)&&"offsetParent"in t&&"HTML"!==r.nodeName&&"static"===tk(r,"position");)r=r.offsetParent;return r||n.documentElement}(e);n=tO(e),"html"!==(a.nodeName&&a.nodeName.toLowerCase())&&(r=tO(a));var o=String(tk(a,"borderTopWidth")||0);r.top+=parseInt(o,10)-tT(a)||0;var i=String(tk(a,"borderLeftWidth")||0);r.left+=parseInt(i,10)-tM(a)||0}var s=String(tk(e,"marginTop")||0),l=String(tk(e,"marginLeft")||0);return e_({},n,{top:n.top-r.top-(parseInt(s,10)||0),left:n.left-r.left-(parseInt(l,10)||0)})}let tA=!!("undefined"!=typeof window&&window.document&&window.document.createElement);var tP=new Date().getTime(),tR="clearTimeout",t_=function(e){var t=new Date().getTime(),n=setTimeout(e,Math.max(0,16-(t-tP)));return tP=t,n},tI=function(e,t){return e+(e?t[0].toUpperCase()+t.substr(1):t)+"AnimationFrame"};tA&&["","webkit","moz","o","ms"].some(function(e){var t=tI(e,"request");return t in window&&(tR=tI(e,"cancel"),t_=function(e){return window[t](e)}),!!t_});var tL=function(e){"function"==typeof window[tR]&&window[tR](e)},tF=t_,tz=n(89859),tq=n.n(tz);function tH(){return(0,s.useState)(null)}let t$=e=>e&&"function"!=typeof e?t=>{e.current=t}:e;var tW="bottom",tY="right",tU="left",tV="auto",tB=["top",tW,tY,tU],tG="start",tK="viewport",tX="popper",tQ=tB.reduce(function(e,t){return e.concat([t+"-"+tG,t+"-end"])},[]),tZ=[].concat(tB,[tV]).reduce(function(e,t){return e.concat([t,t+"-"+tG,t+"-end"])},[]),tJ=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];let t0=function(e){let t=function(){let e=(0,s.useRef)(!0),t=(0,s.useRef)(()=>e.current);return(0,s.useEffect)(()=>(e.current=!0,()=>{e.current=!1}),[]),t.current}();return[e[0],(0,s.useCallback)(n=>{if(t())return e[1](n)},[t,e[1]])]};function t1(e){return e.split("-")[0]}function t2(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function t4(e){var t=t2(e).Element;return e instanceof t||e instanceof Element}function t3(e){var t=t2(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function t5(e){if("undefined"==typeof ShadowRoot)return!1;var t=t2(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}var t8=Math.max,t7=Math.min,t6=Math.round;function t9(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function ne(){return!/^((?!chrome|android).)*safari/i.test(t9())}function nt(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var r=e.getBoundingClientRect(),a=1,o=1;t&&t3(e)&&(a=e.offsetWidth>0&&t6(r.width)/e.offsetWidth||1,o=e.offsetHeight>0&&t6(r.height)/e.offsetHeight||1);var i=(t4(e)?t2(e):window).visualViewport,s=!ne()&&n,l=(r.left+(s&&i?i.offsetLeft:0))/a,c=(r.top+(s&&i?i.offsetTop:0))/o,u=r.width/a,d=r.height/o;return{width:u,height:d,top:c,right:l+u,bottom:c+d,left:l,x:l,y:c}}function nn(e){var t=nt(e),n=e.offsetWidth,r=e.offsetHeight;return 1>=Math.abs(t.width-n)&&(n=t.width),1>=Math.abs(t.height-r)&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function nr(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&t5(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function na(e){return e?(e.nodeName||"").toLowerCase():null}function no(e){return t2(e).getComputedStyle(e)}function ni(e){return((t4(e)?e.ownerDocument:e.document)||window.document).documentElement}function ns(e){return"html"===na(e)?e:e.assignedSlot||e.parentNode||(t5(e)?e.host:null)||ni(e)}function nl(e){return t3(e)&&"fixed"!==no(e).position?e.offsetParent:null}function nc(e){for(var t=t2(e),n=nl(e);n&&["table","td","th"].indexOf(na(n))>=0&&"static"===no(n).position;)n=nl(n);return n&&("html"===na(n)||"body"===na(n)&&"static"===no(n).position)?t:n||function(e){var t=/firefox/i.test(t9());if(/Trident/i.test(t9())&&t3(e)&&"fixed"===no(e).position)return null;var n=ns(e);for(t5(n)&&(n=n.host);t3(n)&&0>["html","body"].indexOf(na(n));){var r=no(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}function nu(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function nd(e,t,n){return t8(e,t7(t,n))}function nf(){return{top:0,right:0,bottom:0,left:0}}function np(e){return Object.assign({},nf(),e)}function nm(e,t){return t.reduce(function(t,n){return t[n]=e,t},{})}function nh(e){return e.split("-")[1]}var nv={top:"auto",right:"auto",bottom:"auto",left:"auto"};function ng(e){var t,n,r,a,o,i,s,l=e.popper,c=e.popperRect,u=e.placement,d=e.variation,f=e.offsets,p=e.position,m=e.gpuAcceleration,h=e.adaptive,v=e.roundOffsets,g=e.isFixed,y=f.x,b=void 0===y?0:y,w=f.y,x=void 0===w?0:w,D="function"==typeof v?v({x:b,y:x}):{x:b,y:x};b=D.x,x=D.y;var S=f.hasOwnProperty("x"),k=f.hasOwnProperty("y"),E=tU,j="top",N=window;if(h){var M=nc(l),T="clientHeight",O="clientWidth";M===t2(l)&&"static"!==no(M=ni(l)).position&&"absolute"===p&&(T="scrollHeight",O="scrollWidth"),("top"===u||(u===tU||u===tY)&&"end"===d)&&(j=tW,x-=(g&&M===N&&N.visualViewport?N.visualViewport.height:M[T])-c.height,x*=m?1:-1),(u===tU||("top"===u||u===tW)&&"end"===d)&&(E=tY,b-=(g&&M===N&&N.visualViewport?N.visualViewport.width:M[O])-c.width,b*=m?1:-1)}var C=Object.assign({position:p},h&&nv),A=!0===v?(t={x:b,y:x},n=t2(l),r=t.x,a=t.y,{x:t6(r*(o=n.devicePixelRatio||1))/o||0,y:t6(a*o)/o||0}):{x:b,y:x};return(b=A.x,x=A.y,m)?Object.assign({},C,((s={})[j]=k?"0":"",s[E]=S?"0":"",s.transform=1>=(N.devicePixelRatio||1)?"translate("+b+"px, "+x+"px)":"translate3d("+b+"px, "+x+"px, 0)",s)):Object.assign({},C,((i={})[j]=k?x+"px":"",i[E]=S?b+"px":"",i.transform="",i))}var ny={passive:!0},nb={left:"right",right:"left",bottom:"top",top:"bottom"};function nw(e){return e.replace(/left|right|bottom|top/g,function(e){return nb[e]})}var nx={start:"end",end:"start"};function nD(e){return e.replace(/start|end/g,function(e){return nx[e]})}function nS(e){var t=t2(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function nk(e){return nt(ni(e)).left+nS(e).scrollLeft}function nE(e){var t=no(e),n=t.overflow,r=t.overflowX,a=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+a+r)}function nj(e,t){void 0===t&&(t=[]);var n,r=function e(t){return["html","body","#document"].indexOf(na(t))>=0?t.ownerDocument.body:t3(t)&&nE(t)?t:e(ns(t))}(e),a=r===(null==(n=e.ownerDocument)?void 0:n.body),o=t2(r),i=a?[o].concat(o.visualViewport||[],nE(r)?r:[]):r,s=t.concat(i);return a?s:s.concat(nj(ns(i)))}function nN(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function nM(e,t,n){var r,a,o,i,s,l,c,u,d,f;return t===tK?nN(function(e,t){var n=t2(e),r=ni(e),a=n.visualViewport,o=r.clientWidth,i=r.clientHeight,s=0,l=0;if(a){o=a.width,i=a.height;var c=ne();(c||!c&&"fixed"===t)&&(s=a.offsetLeft,l=a.offsetTop)}return{width:o,height:i,x:s+nk(e),y:l}}(e,n)):t4(t)?((r=nt(t,!1,"fixed"===n)).top=r.top+t.clientTop,r.left=r.left+t.clientLeft,r.bottom=r.top+t.clientHeight,r.right=r.left+t.clientWidth,r.width=t.clientWidth,r.height=t.clientHeight,r.x=r.left,r.y=r.top,r):nN((a=ni(e),i=ni(a),s=nS(a),l=null==(o=a.ownerDocument)?void 0:o.body,c=t8(i.scrollWidth,i.clientWidth,l?l.scrollWidth:0,l?l.clientWidth:0),u=t8(i.scrollHeight,i.clientHeight,l?l.scrollHeight:0,l?l.clientHeight:0),d=-s.scrollLeft+nk(a),f=-s.scrollTop,"rtl"===no(l||i).direction&&(d+=t8(i.clientWidth,l?l.clientWidth:0)-c),{width:c,height:u,x:d,y:f}))}function nT(e){var t,n=e.reference,r=e.element,a=e.placement,o=a?t1(a):null,i=a?nh(a):null,s=n.x+n.width/2-r.width/2,l=n.y+n.height/2-r.height/2;switch(o){case"top":t={x:s,y:n.y-r.height};break;case tW:t={x:s,y:n.y+n.height};break;case tY:t={x:n.x+n.width,y:l};break;case tU:t={x:n.x-r.width,y:l};break;default:t={x:n.x,y:n.y}}var c=o?nu(o):null;if(null!=c){var u="y"===c?"height":"width";switch(i){case tG:t[c]=t[c]-(n[u]/2-r[u]/2);break;case"end":t[c]=t[c]+(n[u]/2-r[u]/2)}}return t}function nO(e,t){void 0===t&&(t={});var n,r,a,o,i,s,l,c,u=t,d=u.placement,f=void 0===d?e.placement:d,p=u.strategy,m=void 0===p?e.strategy:p,h=u.boundary,v=u.rootBoundary,g=u.elementContext,y=void 0===g?tX:g,b=u.altBoundary,w=u.padding,x=void 0===w?0:w,D=np("number"!=typeof x?x:nm(x,tB)),S=e.rects.popper,k=e.elements[void 0!==b&&b?y===tX?"reference":tX:y],E=(n=t4(k)?k:k.contextElement||ni(e.elements.popper),r=void 0===h?"clippingParents":h,a=void 0===v?tK:v,l=(s=[].concat("clippingParents"===r?(o=nj(ns(n)),!t4(i=["absolute","fixed"].indexOf(no(n).position)>=0&&t3(n)?nc(n):n)?[]:o.filter(function(e){return t4(e)&&nr(e,i)&&"body"!==na(e)})):[].concat(r),[a]))[0],(c=s.reduce(function(e,t){var r=nM(n,t,m);return e.top=t8(r.top,e.top),e.right=t7(r.right,e.right),e.bottom=t7(r.bottom,e.bottom),e.left=t8(r.left,e.left),e},nM(n,l,m))).width=c.right-c.left,c.height=c.bottom-c.top,c.x=c.left,c.y=c.top,c),j=nt(e.elements.reference),N=nT({reference:j,element:S,strategy:"absolute",placement:f}),M=nN(Object.assign({},S,N)),T=y===tX?M:j,O={top:E.top-T.top+D.top,bottom:T.bottom-E.bottom+D.bottom,left:E.left-T.left+D.left,right:T.right-E.right+D.right},C=e.modifiersData.offset;if(y===tX&&C){var A=C[f];Object.keys(O).forEach(function(e){var t=[tY,tW].indexOf(e)>=0?1:-1,n=["top",tW].indexOf(e)>=0?"y":"x";O[e]+=A[n]*t})}return O}function nC(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function nA(e){return["top",tY,tW,tU].some(function(t){return e[t]>=0})}var nP={placement:"bottom",modifiers:[],strategy:"absolute"};function nR(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(e){return!(e&&"function"==typeof e.getBoundingClientRect)})}var n_=function(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,a=t.defaultOptions,o=void 0===a?nP:a;return function(e,t,n){void 0===n&&(n=o);var a,i,s={placement:"bottom",orderedModifiers:[],options:Object.assign({},nP,o),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},l=[],c=!1,u={state:s,setOptions:function(n){var a,i,c,f,p,m,h="function"==typeof n?n(s.options):n;d(),s.options=Object.assign({},o,s.options,h),s.scrollParents={reference:t4(e)?nj(e):e.contextElement?nj(e.contextElement):[],popper:nj(t)};var v=(i=Object.keys(a=[].concat(r,s.options.modifiers).reduce(function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e},{})).map(function(e){return a[e]}),c=new Map,f=new Set,p=[],i.forEach(function(e){c.set(e.name,e)}),i.forEach(function(e){f.has(e.name)||function e(t){f.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach(function(t){if(!f.has(t)){var n=c.get(t);n&&e(n)}}),p.push(t)}(e)}),m=p,tJ.reduce(function(e,t){return e.concat(m.filter(function(e){return e.phase===t}))},[]));return s.orderedModifiers=v.filter(function(e){return e.enabled}),s.orderedModifiers.forEach(function(e){var t=e.name,n=e.options,r=e.effect;if("function"==typeof r){var a=r({state:s,name:t,instance:u,options:void 0===n?{}:n});l.push(a||function(){})}}),u.update()},forceUpdate:function(){if(!c){var e=s.elements,t=e.reference,n=e.popper;if(nR(t,n)){s.rects={reference:(r=nc(n),a="fixed"===s.options.strategy,o=t3(r),f=t3(r)&&(l=t6((i=r.getBoundingClientRect()).width)/r.offsetWidth||1,d=t6(i.height)/r.offsetHeight||1,1!==l||1!==d),p=ni(r),m=nt(t,f,a),h={scrollLeft:0,scrollTop:0},v={x:0,y:0},(o||!o&&!a)&&(("body"!==na(r)||nE(p))&&(h=function(e){return e!==t2(e)&&t3(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:nS(e)}(r)),t3(r)?(v=nt(r,!0),v.x+=r.clientLeft,v.y+=r.clientTop):p&&(v.x=nk(p))),{x:m.left+h.scrollLeft-v.x,y:m.top+h.scrollTop-v.y,width:m.width,height:m.height}),popper:nn(n)},s.reset=!1,s.placement=s.options.placement,s.orderedModifiers.forEach(function(e){return s.modifiersData[e.name]=Object.assign({},e.data)});for(var r,a,o,i,l,d,f,p,m,h,v,g=0;g<s.orderedModifiers.length;g++){if(!0===s.reset){s.reset=!1,g=-1;continue}var y=s.orderedModifiers[g],b=y.fn,w=y.options,x=void 0===w?{}:w,D=y.name;"function"==typeof b&&(s=b({state:s,options:x,name:D,instance:u})||s)}}}},update:(a=function(){return new Promise(function(e){u.forceUpdate(),e(s)})},function(){return i||(i=new Promise(function(e){Promise.resolve().then(function(){i=void 0,e(a())})})),i}),destroy:function(){d(),c=!0}};if(!nR(e,t))return u;function d(){l.forEach(function(e){return e()}),l=[]}return u.setOptions(n).then(function(e){!c&&n.onFirstUpdate&&n.onFirstUpdate(e)}),u}}({defaultModifiers:[{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,a=t.rects.popper,o=t.modifiersData.preventOverflow,i=nO(t,{elementContext:"reference"}),s=nO(t,{altBoundary:!0}),l=nC(i,r),c=nC(s,a,o),u=nA(l),d=nA(c);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:u,hasPopperEscaped:d},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":d})}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=nT({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,a=n.adaptive,o=n.roundOffsets,i=void 0===o||o,s={placement:t1(t.placement),variation:nh(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:void 0===r||r,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,ng(Object.assign({},s,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:void 0===a||a,roundOffsets:i})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,ng(Object.assign({},s,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:i})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,a=r.scroll,o=void 0===a||a,i=r.resize,s=void 0===i||i,l=t2(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return o&&c.forEach(function(e){e.addEventListener("scroll",n.update,ny)}),s&&l.addEventListener("resize",n.update,ny),function(){o&&c.forEach(function(e){e.removeEventListener("scroll",n.update,ny)}),s&&l.removeEventListener("resize",n.update,ny)}},data:{}},{name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,a=n.offset,o=void 0===a?[0,0]:a,i=tZ.reduce(function(e,n){var r,a,i,s,l,c;return e[n]=(r=t.rects,i=[tU,"top"].indexOf(a=t1(n))>=0?-1:1,l=(s="function"==typeof o?o(Object.assign({},r,{placement:n})):o)[0],c=s[1],l=l||0,c=(c||0)*i,[tU,tY].indexOf(a)>=0?{x:c,y:l}:{x:l,y:c}),e},{}),s=i[t.placement],l=s.x,c=s.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=c),t.modifiersData[r]=i}},{name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var a=n.mainAxis,o=void 0===a||a,i=n.altAxis,s=void 0===i||i,l=n.fallbackPlacements,c=n.padding,u=n.boundary,d=n.rootBoundary,f=n.altBoundary,p=n.flipVariations,m=void 0===p||p,h=n.allowedAutoPlacements,v=t.options.placement,g=t1(v)===v,y=l||(g||!m?[nw(v)]:function(e){if(t1(e)===tV)return[];var t=nw(e);return[nD(e),t,nD(t)]}(v)),b=[v].concat(y).reduce(function(e,n){var r,a,o,i,s,l,f,p,v,g,y,b;return e.concat(t1(n)===tV?(a=(r={placement:n,boundary:u,rootBoundary:d,padding:c,flipVariations:m,allowedAutoPlacements:h}).placement,o=r.boundary,i=r.rootBoundary,s=r.padding,l=r.flipVariations,p=void 0===(f=r.allowedAutoPlacements)?tZ:f,0===(y=(g=(v=nh(a))?l?tQ:tQ.filter(function(e){return nh(e)===v}):tB).filter(function(e){return p.indexOf(e)>=0})).length&&(y=g),Object.keys(b=y.reduce(function(e,n){return e[n]=nO(t,{placement:n,boundary:o,rootBoundary:i,padding:s})[t1(n)],e},{})).sort(function(e,t){return b[e]-b[t]})):n)},[]),w=t.rects.reference,x=t.rects.popper,D=new Map,S=!0,k=b[0],E=0;E<b.length;E++){var j=b[E],N=t1(j),M=nh(j)===tG,T=["top",tW].indexOf(N)>=0,O=T?"width":"height",C=nO(t,{placement:j,boundary:u,rootBoundary:d,altBoundary:f,padding:c}),A=T?M?tY:tU:M?tW:"top";w[O]>x[O]&&(A=nw(A));var P=nw(A),R=[];if(o&&R.push(C[N]<=0),s&&R.push(C[A]<=0,C[P]<=0),R.every(function(e){return e})){k=j,S=!1;break}D.set(j,R)}if(S)for(var _=m?3:1,I=function(e){var t=b.find(function(t){var n=D.get(t);if(n)return n.slice(0,e).every(function(e){return e})});if(t)return k=t,"break"},L=_;L>0&&"break"!==I(L);L--);t.placement!==k&&(t.modifiersData[r]._skip=!0,t.placement=k,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}},{name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name,a=n.mainAxis,o=n.altAxis,i=n.boundary,s=n.rootBoundary,l=n.altBoundary,c=n.padding,u=n.tether,d=void 0===u||u,f=n.tetherOffset,p=void 0===f?0:f,m=nO(t,{boundary:i,rootBoundary:s,padding:c,altBoundary:l}),h=t1(t.placement),v=nh(t.placement),g=!v,y=nu(h),b="x"===y?"y":"x",w=t.modifiersData.popperOffsets,x=t.rects.reference,D=t.rects.popper,S="function"==typeof p?p(Object.assign({},t.rects,{placement:t.placement})):p,k="number"==typeof S?{mainAxis:S,altAxis:S}:Object.assign({mainAxis:0,altAxis:0},S),E=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,j={x:0,y:0};if(w){if(void 0===a||a){var N,M="y"===y?"top":tU,T="y"===y?tW:tY,O="y"===y?"height":"width",C=w[y],A=C+m[M],P=C-m[T],R=d?-D[O]/2:0,_=v===tG?x[O]:D[O],I=v===tG?-D[O]:-x[O],L=t.elements.arrow,F=d&&L?nn(L):{width:0,height:0},z=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:nf(),q=z[M],H=z[T],$=nd(0,x[O],F[O]),W=g?x[O]/2-R-$-q-k.mainAxis:_-$-q-k.mainAxis,Y=g?-x[O]/2+R+$+H+k.mainAxis:I+$+H+k.mainAxis,U=t.elements.arrow&&nc(t.elements.arrow),V=U?"y"===y?U.clientTop||0:U.clientLeft||0:0,B=null!=(N=null==E?void 0:E[y])?N:0,G=nd(d?t7(A,C+W-B-V):A,C,d?t8(P,C+Y-B):P);w[y]=G,j[y]=G-C}if(void 0!==o&&o){var K,X,Q="x"===y?"top":tU,Z="x"===y?tW:tY,J=w[b],ee="y"===b?"height":"width",et=J+m[Q],en=J-m[Z],er=-1!==["top",tU].indexOf(h),ea=null!=(X=null==E?void 0:E[b])?X:0,eo=er?et:J-x[ee]-D[ee]-ea+k.altAxis,ei=er?J+x[ee]+D[ee]-ea-k.altAxis:en,es=d&&er?(K=nd(eo,J,ei))>ei?ei:K:nd(d?eo:et,J,d?ei:en);w[b]=es,j[b]=es-J}t.modifiersData[r]=j}},requiresIfExists:["offset"]},{name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,r=e.name,a=e.options,o=n.elements.arrow,i=n.modifiersData.popperOffsets,s=t1(n.placement),l=nu(s),c=[tU,tY].indexOf(s)>=0?"height":"width";if(o&&i){var u,d=(u=a.padding,np("number"!=typeof(u="function"==typeof u?u(Object.assign({},n.rects,{placement:n.placement})):u)?u:nm(u,tB))),f=nn(o),p="y"===l?"top":tU,m="y"===l?tW:tY,h=n.rects.reference[c]+n.rects.reference[l]-i[l]-n.rects.popper[c],v=i[l]-n.rects.reference[l],g=nc(o),y=g?"y"===l?g.clientHeight||0:g.clientWidth||0:0,b=d[p],w=y-f[c]-d[m],x=y/2-f[c]/2+(h/2-v/2),D=nd(b,x,w);n.modifiersData[r]=((t={})[l]=D,t.centerOffset=D-x,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;if(null!=r)("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&nr(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]}]}),nI=function(e){return{position:e,top:"0",left:"0",opacity:"0",pointerEvents:"none"}},nL={name:"applyStyles",enabled:!1},nF={name:"ariaDescribedBy",enabled:!0,phase:"afterWrite",effect:function(e){var t=e.state;return function(){var e=t.elements,n=e.reference,r=e.popper;if("removeAttribute"in n){var a=(n.getAttribute("aria-describedby")||"").split(",").filter(function(e){return e.trim()!==r.id});a.length?n.setAttribute("aria-describedby",a.join(",")):n.removeAttribute("aria-describedby")}}},fn:function(e){var t,n=e.state.elements,r=n.popper,a=n.reference,o=null==(t=r.getAttribute("role"))?void 0:t.toLowerCase();if(r.id&&"tooltip"===o&&"setAttribute"in a){var i=a.getAttribute("aria-describedby");if(i&&-1!==i.split(",").indexOf(r.id))return;a.setAttribute("aria-describedby",i?i+","+r.id:r.id)}}},nz=[];let nq=function(e,t,n){var r=void 0===n?{}:n,a=r.enabled,o=void 0===a||a,i=r.placement,l=void 0===i?"bottom":i,c=r.strategy,u=void 0===c?"absolute":c,d=r.modifiers,f=void 0===d?nz:d,p=eh(r,["enabled","placement","strategy","modifiers"]),m=(0,s.useRef)(),h=(0,s.useCallback)(function(){var e;null==(e=m.current)||e.update()},[]),v=(0,s.useCallback)(function(){var e;null==(e=m.current)||e.forceUpdate()},[]),g=t0((0,s.useState)({placement:l,update:h,forceUpdate:v,attributes:{},styles:{popper:nI(u),arrow:{}}})),y=g[0],b=g[1],w=(0,s.useMemo)(function(){return{name:"updateStateModifier",enabled:!0,phase:"write",requires:["computeStyles"],fn:function(e){var t=e.state,n={},r={};Object.keys(t.elements).forEach(function(e){n[e]=t.styles[e],r[e]=t.attributes[e]}),b({state:t,styles:n,attributes:r,update:h,forceUpdate:v,placement:t.placement})}}},[h,v,b]);return(0,s.useEffect)(function(){m.current&&o&&m.current.setOptions({placement:l,strategy:u,modifiers:[].concat(f,[w,nL])})},[u,l,w,o]),(0,s.useEffect)(function(){if(o&&null!=e&&null!=t)return m.current=n_(e,t,e_({},p,{placement:l,strategy:u,modifiers:[].concat(f,[nF,w])})),function(){null!=m.current&&(m.current.destroy(),m.current=void 0,b(function(e){return e_({},e,{attributes:{},styles:{popper:nI(u)}})}))}},[o,e,t]),y};var nH=!1,n$=!1;try{var nW={get passive(){return nH=!0},get once(){return n$=nH=!0}};tA&&(window.addEventListener("test",nW,nW),window.removeEventListener("test",nW,!0))}catch(e){}let nY=function(e,t,n,r){if(r&&"boolean"!=typeof r&&!n$){var a=r.once,o=r.capture,i=n;!n$&&a&&(i=n.__once||function e(r){this.removeEventListener(t,e,o),n.call(this,r)},n.__once=i),e.addEventListener(t,i,nH?r:o)}e.addEventListener(t,n,r)},nU=function(e,t,n,r){var a=r&&"boolean"!=typeof r?r.capture:r;e.removeEventListener(t,n,a),n.__once&&e.removeEventListener(t,n.__once,a)},nV=function(e,t,n,r){return nY(e,t,n,r),function(){nU(e,t,n,r)}},nB=function(e){let t=(0,s.useRef)(e);return(0,s.useEffect)(()=>{t.current=e},[e]),t};function nG(e){let t=nB(e);return(0,s.useCallback)(function(...e){return t.current&&t.current(...e)},[t])}var nK=n(65682),nX=n.n(nK);let nQ=function(e){return tb(e&&"setState"in e?tq().findDOMNode(e):null!=e?e:null)};var nZ=function(){},nJ=function(e){return e&&("current"in e?e.current:e)};let n0=function(e,t,n){var r=void 0===n?{}:n,a=r.disabled,o=r.clickTrigger,i=void 0===o?"click":o,l=(0,s.useRef)(!1),c=t||nZ,u=(0,s.useCallback)(function(t){var n,r=nJ(e);nX()(!!r,"RootClose captured a close event but does not have a ref to compare it to. useRootClose(), should be passed a ref that resolves to a DOM node"),l.current=!r||!!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)||0!==t.button||!!tE(r,null!=(n=null==t.composedPath?void 0:t.composedPath()[0])?n:t.target)},[e]),d=nG(function(e){l.current||c(e)}),f=nG(function(e){27===e.keyCode&&c(e)});(0,s.useEffect)(function(){if(!a&&null!=e){var t=window.event,n=nQ(nJ(e)),r=nV(n,i,u,!0),o=nV(n,i,function(e){if(e===t){t=void 0;return}d(e)}),s=nV(n,"keyup",function(e){if(e===t){t=void 0;return}f(e)}),l=[];return"ontouchstart"in n.documentElement&&(l=[].slice.call(n.body.children).map(function(e){return nV(e,"mousemove",nZ)})),function(){r(),o(),s(),l.forEach(function(e){return e()})}}},[e,a,i,u,d,f])};var n1=function(e){var t;return"undefined"==typeof document?null:null==e?tb().body:("function"==typeof e&&(e=e()),e&&"current"in e&&(e=e.current),null!=(t=e)&&t.nodeType&&e||null)};function n2(e,t){var n=(0,s.useState)(function(){return n1(e)}),r=n[0],a=n[1];if(!r){var o=n1(e);o&&a(o)}return(0,s.useEffect)(function(){t&&r&&t(r)},[t,r]),(0,s.useEffect)(function(){var t=n1(e);t!==r&&a(t)},[e,r]),r}var n4=l().forwardRef(function(e,t){var n,r,a,o,i,c,u,d,f,p,m,h,v,g,y,b,w,x,D,S=e.flip,k=e.offset,E=e.placement,j=e.containerPadding,N=e.popperConfig,M=e.transition,T=tH(),O=T[0],C=T[1],A=tH(),P=A[0],R=A[1],_=(0,s.useMemo)(()=>(function(e,t){let n=t$(e),r=t$(t);return e=>{n&&n(e),r&&r(e)}})(C,t),[C,t]),I=n2(e.container),L=n2(e.target),F=(0,s.useState)(!e.show),z=F[0],q=F[1],H=nq(L,O,(c=(n={placement:E,enableEvents:!!e.show,containerPadding:(void 0===j?5:j)||5,flip:S,offset:k,arrowElement:P,popperConfig:void 0===N?{}:N}).enabled,u=n.enableEvents,d=n.placement,f=n.flip,p=n.offset,m=n.fixed,h=n.containerPadding,v=n.arrowElement,b=(y=void 0===(g=n.popperConfig)?{}:g).modifiers,w={},x=Array.isArray(b)?(null==b||b.forEach(function(e){w[e.name]=e}),w):b||w,e_({},y,{placement:d,enabled:c,strategy:m?"fixed":y.strategy,modifiers:(void 0===(D=e_({},x,{eventListeners:{enabled:u},preventOverflow:e_({},x.preventOverflow,{options:h?e_({padding:h},null==(r=x.preventOverflow)?void 0:r.options):null==(a=x.preventOverflow)?void 0:a.options}),offset:{options:e_({offset:p},null==(o=x.offset)?void 0:o.options)},arrow:e_({},x.arrow,{enabled:!!v,options:e_({},null==(i=x.arrow)?void 0:i.options,{element:v})}),flip:e_({enabled:!!f},x.flip)}))&&(D={}),Array.isArray(D))?D:Object.keys(D).map(function(e){return D[e].name=e,D[e]})}))),$=H.styles,W=H.attributes,Y=eh(H,["styles","attributes"]);e.show?z&&q(!1):e.transition||z||q(!0);var U=e.show||M&&!z;if(n0(O,e.onHide,{disabled:!e.rootClose||e.rootCloseDisabled,clickTrigger:e.rootCloseEvent}),!U)return null;var V=e.children(e_({},Y,{show:!!e.show,props:e_({},W.popper,{style:$.popper,ref:_}),arrowProps:e_({},W.arrow,{style:$.arrow,ref:R})}));if(M){var B=e.onExit,G=e.onExiting,K=e.onEnter,X=e.onEntering,Q=e.onEntered;V=l().createElement(M,{in:e.show,appear:!0,onExit:B,onExiting:G,onExited:function(){q(!0),e.onExited&&e.onExited.apply(e,arguments)},onEnter:K,onEntering:X,onEntered:Q},V)}return I?tq().createPortal(V,I):null});n4.displayName="Overlay",n4.propTypes={show:eq().bool,placement:eq().oneOf(tZ),target:eq().any,container:eq().any,flip:eq().bool,children:eq().func.isRequired,containerPadding:eq().number,popperConfig:eq().object,rootClose:eq().bool,rootCloseEvent:eq().oneOf(["click","mousedown"]),rootCloseDisabled:eq().bool,onHide:function(e){for(var t,n=arguments.length,r=Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return e.rootClose?(t=eq().func).isRequired.apply(t,[e].concat(r)):eq().func.apply(eq(),[e].concat(r))},transition:eq().elementType,onEnter:eq().func,onEntering:eq().func,onEntered:eq().func,onExit:eq().func,onExiting:eq().func,onExited:eq().func};var n3=n(29736),n5=n.n(n3);function n8(e,t){var n=tj(e);return n?n.innerHeight:t?e.clientHeight:tO(e).height}var n7=Function.prototype.bind.call(Function.prototype.call,[].slice);function n6(e,t,n){e.closest&&!n&&e.closest(t);var a=e;do{if(function(e,t){if(!r){var n=document.body,a=n.matches||n.matchesSelector||n.webkitMatchesSelector||n.mozMatchesSelector||n.msMatchesSelector;r=function(e,t){return a.call(e,t)}}return r(e,t)}(a,t))return a;a=a.parentElement}while(a&&a!==n&&a.nodeType===document.ELEMENT_NODE);return null}var n9=n(86389),re=n.n(n9),rt=n(96390),rn=n.n(rt),rr=Number.isNaN||function(e){return"number"==typeof e&&e!=e};function ra(e,t){if(e.length!==t.length)return!1;for(var n,r,a=0;a<e.length;a++)if(!((n=e[a])===(r=t[a])||rr(n)&&rr(r))&&1)return!1;return!0}function ro(e,t){void 0===t&&(t=ra);var n=null;function r(){for(var r=[],a=0;a<arguments.length;a++)r[a]=arguments[a];if(n&&n.lastThis===this&&t(r,n.lastArgs))return n.lastResult;var o=e.apply(this,r);return n={lastResult:o,lastArgs:r,lastThis:this},o}return r.clear=function(){n=null},r}function ri(e,t){var n=tj(e);return n?n.innerWidth:t?e.clientWidth:tO(e).width}var rs=n(76111),rl=n.n(rs);function rc(e){if((!a&&0!==a||e)&&tA){var t=document.createElement("div");t.style.position="absolute",t.style.top="-9999px",t.style.width="50px",t.style.height="50px",t.style.overflow="scroll",document.body.appendChild(t),a=t.offsetWidth-t.clientWidth,document.body.removeChild(t)}return a}function ru(e){return eE(e)||th(e)||eN(e)||eM()}function rd(e,t){return e.replace(RegExp("(^|\\s)"+t+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}var rf=n(16912),rp=n.n(rf),rm=n(94288),rh=n.n(rm),rv=n(79243),rg=n.n(rv),ry=n(82772),rb=n.n(ry);function rw(e){return e.children}n(32774),n(85917),n(8432),n(52154),n(53360),n(96854),n(28096),n(11855);var rx={PREVIOUS:"PREV",NEXT:"NEXT",TODAY:"TODAY",DATE:"DATE"},rD={MONTH:"month",WEEK:"week",WORK_WEEK:"work_week",DAY:"day",AGENDA:"agenda"},rS=Object.keys(rD).map(function(e){return rD[e]});eq().oneOfType([eq().string,eq().func]),eq().any,eq().func,eq().oneOfType([eq().arrayOf(eq().oneOf(rS)),eq().objectOf(function(e,t){if(-1!==rS.indexOf(t)&&"boolean"==typeof e[t])return null;for(var n=arguments.length,r=Array(n>2?n-2:0),a=2;a<n;a++)r[a-2]=arguments[a];return eq().elementType.apply(eq(),[e,t].concat(r))})]),eq().oneOfType([eq().oneOf(["overlap","no-overlap"]),eq().func]);var rk={seconds:1e3,minutes:6e4,hours:36e5,day:864e5};function rE(e,t){var n=e0(e,"month");return e0(n,"week",t.startOfWeek())}function rj(e,t){var n=e1(e,"month");return e1(n,"week",t.startOfWeek())}function rN(e,t){for(var n=rE(e,t),r=rj(e,t),a=[];e7(n,r,"day");)a.push(n),n=eZ(n,1,"day");return a}function rM(e,t){var n=e0(e,t);return e2(n,e)?n:eZ(n,1,t)}function rT(e,t){for(var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"day",r=e,a=[];e7(r,t,n);)a.push(r),r=eZ(r,1,n);return a}function rO(e,t){return null==t&&null==e?null:(null==t&&(t=new Date),null==e&&(e=new Date),tt(e=tn(e=tr(e=ta(e=e0(e,"day"),ta(t)),tr(t)),tn(t)),tt(t)))}function rC(e){return 0===ta(e)&&0===tr(e)&&0===tn(e)&&0===tt(e)}function rA(e,t,n){return n&&"milliseconds"!==n?Math.round(Math.abs(e0(e,n)/rk[n]-e0(t,n)/rk[n])):Math.abs(e-t)}var rP=eq().oneOfType([eq().string,eq().func]);function rR(e,t,n,r,a){var o="function"==typeof r?r(n,a,e):t.call(e,n,r,a);return eA()(null==o||"string"==typeof o,"`localizer format(..)` must return a string, null, or undefined"),o}function r_(e,t,n){return new Date(e.getFullYear(),e.getMonth(),e.getDate(),0,t+n,0,0)}function rI(e,t){return e.getTimezoneOffset()-t.getTimezoneOffset()}function rL(e,t){return rA(e,t,"minutes")+rI(e,t)}function rF(e){var t=e0(e,"day");return rA(t,e,"minutes")+rI(t,e)}function rz(e,t){return e8(e,t,"day")}function rq(e,t,n){return e2(e,t,"minutes")?e5(t,n,"minutes"):e3(t,n,"minutes")}function rH(e,t){var n,r;return"day"==(n="day")&&(n="date"),Math.abs(o[n](e,void 0,void 0)-o[n](t,void 0,r))}function r$(e){var t=e.evtA,n=t.start,r=t.end,a=t.allDay,o=e.evtB,i=o.start,s=o.end,l=o.allDay,c=e0(n,"day")-e0(i,"day"),u=rH(n,r),d=rH(i,s);return c||d-u||!!l-!!a||n-i||r-s}function rW(e){var t=e.event,n=t.start,r=t.end,a=e.range,o=a.start,i=a.end,s=e0(n,"day"),l=e7(s,i,"day"),c=e4(s,r,"minutes")?e3(r,o,"minutes"):e5(r,o,"minutes");return l&&c}function rY(e,t){return e2(e,t,"day")}function rU(e,t){return rC(e)&&rC(t)}var rV=eb(function e(t){var n=this;eg(this,e),eA()("function"==typeof t.format,"date localizer `format(..)` must be a function"),eA()("function"==typeof t.firstOfWeek,"date localizer `firstOfWeek(..)` must be a function"),this.propType=t.propType||rP,this.formats=t.formats,this.format=function(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return rR.apply(void 0,[n,t.format].concat(r))},this.startOfWeek=t.firstOfWeek,this.merge=t.merge||rO,this.inRange=t.inRange||te,this.lt=t.lt||e8,this.lte=t.lte||e7,this.gt=t.gt||e3,this.gte=t.gte||e5,this.eq=t.eq||e2,this.neq=t.neq||e4,this.startOf=t.startOf||e0,this.endOf=t.endOf||e1,this.add=t.add||eZ,this.range=t.range||rT,this.diff=t.diff||rA,this.ceil=t.ceil||rM,this.min=t.min||e6,this.max=t.max||e9,this.minutes=t.minutes||tr,this.daySpan=t.daySpan||rH,this.firstVisibleDay=t.firstVisibleDay||rE,this.lastVisibleDay=t.lastVisibleDay||rj,this.visibleDays=t.visibleDays||rN,this.getSlotDate=t.getSlotDate||r_,this.getTimezoneOffset=t.getTimezoneOffset||function(e){return e.getTimezoneOffset()},this.getDstOffset=t.getDstOffset||rI,this.getTotalMin=t.getTotalMin||rL,this.getMinutesFromMidnight=t.getMinutesFromMidnight||rF,this.continuesPrior=t.continuesPrior||rz,this.continuesAfter=t.continuesAfter||rq,this.sortEvents=t.sortEvents||r$,this.inEventRange=t.inEventRange||rW,this.isSameDate=t.isSameDate||rY,this.startAndEndAreDateOnly=t.startAndEndAreDateOnly||rU,this.segmentOffset=t.browserTZOffset?t.browserTZOffset():0}),rB=function(e){function t(){var e;eg(this,t);for(var n=arguments.length,r=Array(n),a=0;a<n;a++)r[a]=arguments[a];return(e=eD(this,t,[].concat(r))).navigate=function(t){e.props.onNavigate(t)},e.view=function(t){e.props.onView(t)},e}return ek(t,e),eb(t,[{key:"render",value:function(){var e=this.props,t=e.localizer.messages,n=e.label;return l().createElement("div",{className:"rbc-toolbar"},l().createElement("span",{className:"rbc-btn-group"},l().createElement("button",{type:"button",onClick:this.navigate.bind(null,rx.TODAY)},t.today),l().createElement("button",{type:"button",onClick:this.navigate.bind(null,rx.PREVIOUS)},t.previous),l().createElement("button",{type:"button",onClick:this.navigate.bind(null,rx.NEXT)},t.next)),l().createElement("span",{className:"rbc-toolbar-label"},n),l().createElement("span",{className:"rbc-btn-group"},this.viewNamesGroup(t)))}},{key:"viewNamesGroup",value:function(e){var t=this,n=this.props.views,r=this.props.view;if(n.length>1)return n.map(function(n){return l().createElement("button",{type:"button",key:n,className:eO({"rbc-active":r===n}),onClick:t.view.bind(null,n)},e[n])})}}])}(l().Component);function rG(e,t){e&&e.apply(null,[].concat(t))}var rK={date:"Date",time:"Time",event:"Event",allDay:"All Day",week:"Week",work_week:"Work Week",day:"Day",month:"Month",previous:"Back",next:"Next",yesterday:"Yesterday",tomorrow:"Tomorrow",today:"Today",agenda:"Agenda",noEventsInRange:"There are no events in this range.",showMore:function(e){return"+".concat(e," more")}},rX=["style","className","event","selected","isAllDay","onSelect","onDoubleClick","onKeyPress","localizer","continuesPrior","continuesAfter","accessors","getters","children","components","slotStart","slotEnd"],rQ=function(e){function t(){return eg(this,t),eD(this,t,arguments)}return ek(t,e),eb(t,[{key:"render",value:function(){var e=this.props,t=e.style,n=e.className,r=e.event,a=e.selected,o=e.isAllDay,i=e.onSelect,s=e.onDoubleClick,c=e.onKeyPress,u=e.localizer,d=e.continuesPrior,f=e.continuesAfter,p=e.accessors,m=e.getters,h=e.children,v=e.components,g=v.event,y=v.eventWrapper,b=e.slotStart,w=e.slotEnd,x=ev(e,rX);delete x.resizable;var D=p.title(r),S=p.tooltip(r),k=p.end(r),E=p.start(r),j=p.allDay(r),N=o||j||u.diff(E,u.ceil(k,"day"),"day")>1,M=m.eventProp(r,E,k,a),T=l().createElement("div",{className:"rbc-event-content",title:S||void 0},g?l().createElement(g,{event:r,continuesPrior:d,continuesAfter:f,title:D,isAllDay:j,localizer:u,slotStart:b,slotEnd:w}):D);return l().createElement(y,Object.assign({},this.props,{type:"date"}),l().createElement("div",Object.assign({},x,{style:em(em({},M.style),t),className:eO("rbc-event",n,M.className,{"rbc-selected":a,"rbc-event-allday":N,"rbc-event-continues-prior":d,"rbc-event-continues-after":f}),onClick:function(e){return i&&i(r,e)},onDoubleClick:function(e){return s&&s(r,e)},onKeyDown:function(e){return c&&c(r,e)}}),"function"==typeof h?h(T):T))}}])}(l().Component);function rZ(e,t){return!!e&&null!=t&&n5()(e,t)}function rJ(e,t){return(e.right-e.left)/t}function r0(e,t,n,r){var a=rJ(e,r);return n?r-1-Math.floor((t-e.left)/a):Math.floor((t-e.left)/a)}function r1(e){var t,n,r,a=e.containerRef,o=e.accessors,i=e.getters,c=e.selected,u=e.components,d=e.localizer,f=e.position,p=e.show,m=e.events,h=e.slotStart,v=e.slotEnd,g=e.onSelect,y=e.onDoubleClick,b=e.onKeyPress,w=e.handleDragStart,x=e.popperRef,D=e.target,S=e.offset;n=(t={ref:x,callback:p}).ref,r=t.callback,(0,s.useEffect)(function(){var e=function(e){n.current&&!n.current.contains(e.target)&&r()};return document.addEventListener("mousedown",e),function(){document.removeEventListener("mousedown",e)}},[n,r]),(0,s.useLayoutEffect)(function(){var e,t,n,r,o,i,s,l,c,u,d,f,p,m,h,v,g,y,b,w,k=(t=(e={target:D,offset:S,container:a.current,box:x.current}).target,n=e.offset,r=e.container,o=e.box,s=(i=tO(t)).top,l=i.left,c=i.width,u=i.height,f=(d=tO(r)).top,p=d.left,m=d.width,h=d.height,g=(v=tO(o)).width,y=v.height,b=n.x,w=n.y,{topOffset:s+y>f+h?s-y-w:s+w+u,leftOffset:l+g>p+m?l+b-g+c:l+b}),E=k.topOffset,j=k.leftOffset;x.current.style.top="".concat(E,"px"),x.current.style.left="".concat(j,"px")},[S.x,S.y,D]);var k=f.width;return l().createElement("div",{style:{minWidth:k+k/2},className:"rbc-overlay",ref:x},l().createElement("div",{className:"rbc-overlay-header"},d.format(h,"dayHeaderFormat")),m.map(function(e,t){return l().createElement(rQ,{key:t,type:"popup",localizer:d,event:e,getters:i,onSelect:g,accessors:o,components:u,onDoubleClick:y,onKeyPress:b,continuesPrior:d.lt(o.end(e),h,"day"),continuesAfter:d.gte(o.start(e),v,"day"),slotStart:h,slotEnd:v,selected:rZ(e,c),draggable:!0,onDragStart:function(){return w(e)},onDragEnd:function(){return p()}})}))}var r2=l().forwardRef(function(e,t){return l().createElement(r1,Object.assign({},e,{popperRef:t}))});function r4(e){var t=e.containerRef,n=e.popupOffset,r=void 0===n?5:n,a=e.overlay,o=e.accessors,i=e.localizer,c=e.components,u=e.getters,d=e.selected,f=e.handleSelectEvent,p=e.handleDoubleClickEvent,m=e.handleKeyPressEvent,h=e.handleDragStart,v=e.onHide,g=e.overlayDisplay,y=(0,s.useRef)(null);if(!a.position)return null;var b=r;isNaN(r)||(b={x:r,y:r});var w=a.position,x=a.events,D=a.date,S=a.end;return l().createElement(n4,{rootClose:!0,flip:!0,show:!0,placement:"bottom",onHide:v,target:a.target},function(e){var n=e.props;return l().createElement(r2,Object.assign({},n,{containerRef:t,ref:y,target:a.target,offset:b,accessors:o,getters:u,selected:d,components:c,localizer:i,position:w,show:g,events:x,slotStart:D,slotEnd:S,onSelect:f,onDoubleClick:p,onKeyPress:m,handleDragStart:h}))})}r2.propTypes={accessors:eq().object.isRequired,getters:eq().object.isRequired,selected:eq().object,components:eq().object.isRequired,localizer:eq().object.isRequired,position:eq().object.isRequired,show:eq().func.isRequired,events:eq().array.isRequired,slotStart:eq().instanceOf(Date).isRequired,slotEnd:eq().instanceOf(Date),onSelect:eq().func,onDoubleClick:eq().func,onKeyPress:eq().func,handleDragStart:eq().func,style:eq().object,offset:eq().shape({x:eq().number,y:eq().number})};var r3=l().forwardRef(function(e,t){return l().createElement(r4,Object.assign({},e,{containerRef:t}))});function r5(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:document;return nV(n,e,t,{passive:!1})}function r8(e,t){var n,r;return n=t.clientX,r=t.clientY,!!n6(document.elementFromPoint(n,r),".rbc-event",e)}function r7(e){var t=e;return e.touches&&e.touches.length&&(t=e.touches[0]),{clientX:t.clientX,clientY:t.clientY,pageX:t.pageX,pageY:t.pageY}}r3.propTypes={popupOffset:eq().oneOfType([eq().number,eq().shape({x:eq().number,y:eq().number})]),overlay:eq().shape({position:eq().object,events:eq().array,date:eq().instanceOf(Date),end:eq().instanceOf(Date)}),accessors:eq().object.isRequired,localizer:eq().object.isRequired,components:eq().object.isRequired,getters:eq().object.isRequired,selected:eq().object,handleSelectEvent:eq().func,handleDoubleClickEvent:eq().func,handleKeyPressEvent:eq().func,handleDragStart:eq().func,onHide:eq().func,overlayDisplay:eq().func};var r6=eb(function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.global,a=n.longPressThreshold,o=n.validContainers;eg(this,e),this._initialEvent=null,this.selecting=!1,this.isDetached=!1,this.container=t,this.globalMouse=!t||void 0!==r&&r,this.longPressThreshold=void 0===a?250:a,this.validContainers=void 0===o?[]:o,this._listeners=Object.create(null),this._handleInitialEvent=this._handleInitialEvent.bind(this),this._handleMoveEvent=this._handleMoveEvent.bind(this),this._handleTerminatingEvent=this._handleTerminatingEvent.bind(this),this._keyListener=this._keyListener.bind(this),this._dropFromOutsideListener=this._dropFromOutsideListener.bind(this),this._dragOverFromOutsideListener=this._dragOverFromOutsideListener.bind(this),this._removeTouchMoveWindowListener=r5("touchmove",function(){},window),this._removeKeyDownListener=r5("keydown",this._keyListener),this._removeKeyUpListener=r5("keyup",this._keyListener),this._removeDropFromOutsideListener=r5("drop",this._dropFromOutsideListener),this._removeDragOverFromOutsideListener=r5("dragover",this._dragOverFromOutsideListener),this._addInitialEventListener()},[{key:"on",value:function(e,t){var n=this._listeners[e]||(this._listeners[e]=[]);return n.push(t),{remove:function(){var e=n.indexOf(t);-1!==e&&n.splice(e,1)}}}},{key:"emit",value:function(e){for(var t,n=arguments.length,r=Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return(this._listeners[e]||[]).forEach(function(e){void 0===t&&(t=e.apply(void 0,r))}),t}},{key:"teardown",value:function(){this._initialEvent=null,this._initialEventData=null,this._selectRect=null,this.selecting=!1,this._lastClickData=null,this.isDetached=!0,this._listeners=Object.create(null),this._removeTouchMoveWindowListener&&this._removeTouchMoveWindowListener(),this._removeInitialEventListener&&this._removeInitialEventListener(),this._removeEndListener&&this._removeEndListener(),this._onEscListener&&this._onEscListener(),this._removeMoveListener&&this._removeMoveListener(),this._removeKeyUpListener&&this._removeKeyUpListener(),this._removeKeyDownListener&&this._removeKeyDownListener(),this._removeDropFromOutsideListener&&this._removeDropFromOutsideListener(),this._removeDragOverFromOutsideListener&&this._removeDragOverFromOutsideListener()}},{key:"isSelected",value:function(e){var t=this._selectRect;return!!t&&!!this.selecting&&r9(t,ae(e))}},{key:"filter",value:function(e){return this._selectRect&&this.selecting?e.filter(this.isSelected,this):[]}},{key:"_addLongPressListener",value:function(e,t){var n=this,r=null,a=null,o=null,i=function(t){r=setTimeout(function(){l(),e(t)},n.longPressThreshold),a=r5("touchmove",function(){return l()}),o=r5("touchend",function(){return l()})},s=r5("touchstart",i),l=function(){r&&clearTimeout(r),a&&a(),o&&o(),r=null,a=null,o=null};return t&&i(t),function(){l(),s()}}},{key:"_addInitialEventListener",value:function(){var e=this,t=r5("mousedown",function(t){e._removeInitialEventListener(),e._handleInitialEvent(t),e._removeInitialEventListener=r5("mousedown",e._handleInitialEvent)}),n=r5("touchstart",function(t){e._removeInitialEventListener(),e._removeInitialEventListener=e._addLongPressListener(e._handleInitialEvent,t)});this._removeInitialEventListener=function(){t(),n()}}},{key:"_dropFromOutsideListener",value:function(e){var t=r7(e),n=t.pageX,r=t.pageY,a=t.clientX,o=t.clientY;this.emit("dropFromOutside",{x:n,y:r,clientX:a,clientY:o}),e.preventDefault()}},{key:"_dragOverFromOutsideListener",value:function(e){var t=r7(e),n=t.pageX,r=t.pageY,a=t.clientX,o=t.clientY;this.emit("dragOverFromOutside",{x:n,y:r,clientX:a,clientY:o}),e.preventDefault()}},{key:"_handleInitialEvent",value:function(e){if(this._initialEvent=e,!this.isDetached){var t,n=r7(e),r=n.clientX,a=n.clientY,o=n.pageX,i=n.pageY,s=this.container();if(3!==e.which&&2!==e.button&&(!s||tE(s,document.elementFromPoint(r,a)))){if(!this.globalMouse&&s&&!tE(s,e.target)){var l=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return"object"!==eu(e)&&(e={top:e,left:e,right:e,bottom:e}),e}(0),c=l.top,u=l.left,d=l.bottom,f=l.right;if(!r9({top:(t=ae(s)).top-c,left:t.left-u,bottom:t.bottom+d,right:t.right+f},{top:i,left:o}))return}if(!1!==this.emit("beforeSelect",this._initialEventData={isTouch:/^touch/.test(e.type),x:o,y:i,clientX:r,clientY:a}))switch(e.type){case"mousedown":this._removeEndListener=r5("mouseup",this._handleTerminatingEvent),this._onEscListener=r5("keydown",this._handleTerminatingEvent),this._removeMoveListener=r5("mousemove",this._handleMoveEvent);break;case"touchstart":this._handleMoveEvent(e),this._removeEndListener=r5("touchend",this._handleTerminatingEvent),this._removeMoveListener=r5("touchmove",this._handleMoveEvent)}}}}},{key:"_isWithinValidContainer",value:function(e){var t=e.target,n=this.validContainers;return!n||!n.length||!t||n.some(function(e){return!!t.closest(e)})}},{key:"_handleTerminatingEvent",value:function(e){var t=this.selecting,n=this._selectRect;if(!t&&e.type.includes("key")&&(e=this._initialEvent),this.selecting=!1,this._removeEndListener&&this._removeEndListener(),this._removeMoveListener&&this._removeMoveListener(),this._selectRect=null,this._initialEvent=null,this._initialEventData=null,e){var r=!this.container||tE(this.container(),e.target),a=this._isWithinValidContainer(e);return"Escape"!==e.key&&a?!t&&r?this._handleClickEvent(e):t?this.emit("select",n):this.emit("reset"):this.emit("reset")}}},{key:"_handleClickEvent",value:function(e){var t=r7(e),n=t.pageX,r=t.pageY,a=t.clientX,o=t.clientY,i=new Date().getTime();return this._lastClickData&&i-this._lastClickData.timestamp<250?(this._lastClickData=null,this.emit("doubleClick",{x:n,y:r,clientX:a,clientY:o})):(this._lastClickData={timestamp:i},this.emit("click",{x:n,y:r,clientX:a,clientY:o}))}},{key:"_handleMoveEvent",value:function(e){if(null!==this._initialEventData&&!this.isDetached){var t=this._initialEventData,n=t.x,r=t.y,a=r7(e),o=a.pageX,i=a.pageY,s=Math.abs(n-o),l=Math.abs(r-i),c=Math.min(o,n),u=Math.min(i,r),d=this.selecting,f=this.isClick(o,i);(!f||d||s||l)&&(d||f||this.emit("selectStart",this._initialEventData),f||(this.selecting=!0,this._selectRect={top:u,left:c,x:o,y:i,right:c+s,bottom:u+l},this.emit("selecting",this._selectRect)),e.preventDefault())}}},{key:"_keyListener",value:function(e){this.ctrl=e.metaKey||e.ctrlKey}},{key:"isClick",value:function(e,t){var n=this._initialEventData,r=n.x,a=n.y;return!n.isTouch&&5>=Math.abs(e-r)&&5>=Math.abs(t-a)}}]);function r9(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=ae(e),a=r.top,o=r.left,i=r.right,s=r.bottom,l=ae(t),c=l.top,u=l.left,d=l.right,f=l.bottom;return!((void 0===s?a:s)-n<c||a+n>(void 0===f?c:f)||(void 0===i?o:i)-n<u||o+n>(void 0===d?u:d))}function ae(e){if(!e.getBoundingClientRect)return e;var t=e.getBoundingClientRect(),n=t.left+at("left"),r=t.top+at("top");return{top:r,left:n,right:(e.offsetWidth||0)+n,bottom:(e.offsetHeight||0)+r}}function at(e){return"left"===e?window.pageXOffset||document.body.scrollLeft||0:"top"===e?window.pageYOffset||document.body.scrollTop||0:void 0}var an=function(e){function t(e,n){var r;return eg(this,t),(r=eD(this,t,[e,n])).state={selecting:!1},r.containerRef=(0,s.createRef)(),r}return ek(t,e),eb(t,[{key:"componentDidMount",value:function(){this.props.selectable&&this._selectable()}},{key:"componentWillUnmount",value:function(){this._teardownSelectable()}},{key:"componentDidUpdate",value:function(e){!e.selectable&&this.props.selectable&&this._selectable(),e.selectable&&!this.props.selectable&&this._teardownSelectable()}},{key:"render",value:function(){var e=this.props,t=e.range,n=e.getNow,r=e.getters,a=e.date,o=e.components.dateCellWrapper,i=e.localizer,s=this.state,c=s.selecting,u=s.startIdx,d=s.endIdx,f=n();return l().createElement("div",{className:"rbc-row-bg",ref:this.containerRef},t.map(function(e,n){var s=r.dayProp(e),p=s.className,m=s.style;return l().createElement(o,{key:n,value:e,range:t},l().createElement("div",{style:m,className:eO("rbc-day-bg",p,c&&n>=u&&n<=d&&"rbc-selected-cell",i.isSameDate(e,f)&&"rbc-today",a&&i.neq(a,e,"month")&&"rbc-off-range-bg")}))}))}},{key:"_selectable",value:function(){var e=this,t=this.containerRef.current,n=this._selector=new r6(this.props.container,{longPressThreshold:this.props.longPressThreshold}),r=function(n,r){if(!r8(t,n)&&(a=n.clientX,o=n.clientY,!n6(document.elementFromPoint(a,o),".rbc-show-more",t))){var a,o,i,s,l=ae(t),c=e.props,u=c.range,d=c.rtl;if(i=n.x,(s=n.y)>=l.top&&s<=l.bottom&&i>=l.left&&i<=l.right){var f=r0(l,n.x,d,u.length);e._selectSlot({startIdx:f,endIdx:f,action:r,box:n})}}e._initial={},e.setState({selecting:!1})};n.on("selecting",function(r){var a=e.props,o=a.range,i=a.rtl,s=-1,l=-1;if(e.state.selecting||(rG(e.props.onSelectStart,[r]),e._initial={x:r.x,y:r.y}),n.isSelected(t)){var c,u,d,f,p,m,h,v,g,y,b,w=ae(t),x=(c=e._initial,u=o.length,d=-1,f=-1,p=u-1,m=rJ(w,u),h=r0(w,r.x,i,u),v=w.top<r.y&&w.bottom>r.y,g=w.top<c.y&&w.bottom>c.y,y=c.y>w.bottom,b=w.top>c.y,r.top<w.top&&r.bottom>w.bottom&&(d=0,f=p),v&&(b?(d=0,f=h):y&&(d=h,f=p)),g&&(d=f=i?p-Math.floor((c.x-w.left)/m):Math.floor((c.x-w.left)/m),v?h<d?d=h:f=h:c.y<r.y?f=p:d=0),{startIdx:d,endIdx:f});s=x.startIdx,l=x.endIdx}e.setState({selecting:!0,startIdx:s,endIdx:l})}),n.on("beforeSelect",function(t){if("ignoreEvents"===e.props.selectable)return!r8(e.containerRef.current,t)}),n.on("click",function(e){return r(e,"click")}),n.on("doubleClick",function(e){return r(e,"doubleClick")}),n.on("select",function(t){e._selectSlot(em(em({},e.state),{},{action:"select",bounds:t})),e._initial={},e.setState({selecting:!1}),rG(e.props.onSelectEnd,[e.state])})}},{key:"_teardownSelectable",value:function(){this._selector&&(this._selector.teardown(),this._selector=null)}},{key:"_selectSlot",value:function(e){var t=e.endIdx,n=e.startIdx,r=e.action,a=e.bounds,o=e.box;-1!==t&&-1!==n&&this.props.onSelectSlot&&this.props.onSelectSlot({start:n,end:t,action:r,bounds:a,box:o,resourceId:this.props.resourceId})}}])}(l().Component),ar={propTypes:{slotMetrics:eq().object.isRequired,selected:eq().object,isAllDay:eq().bool,accessors:eq().object.isRequired,localizer:eq().object.isRequired,components:eq().object.isRequired,getters:eq().object.isRequired,onSelect:eq().func,onDoubleClick:eq().func,onKeyPress:eq().func},defaultProps:{segments:[],selected:{}},renderEvent:function(e,t){var n=e.selected;e.isAllDay;var r=e.accessors,a=e.getters,o=e.onSelect,i=e.onDoubleClick,s=e.onKeyPress,c=e.localizer,u=e.slotMetrics,d=e.components,f=e.resizable,p=u.continuesPrior(t),m=u.continuesAfter(t);return l().createElement(rQ,{event:t,getters:a,localizer:c,accessors:r,components:d,onSelect:o,onDoubleClick:i,onKeyPress:s,continuesPrior:p,continuesAfter:m,slotStart:u.first,slotEnd:u.last,selected:rZ(t,n),resizable:f})},renderSpan:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:" ",a=Math.abs(t)/e*100+"%";return l().createElement("div",{key:n,className:"rbc-row-segment",style:{WebkitFlexBasis:a,flexBasis:a,maxWidth:a}},r)}},aa=function(e){function t(){return eg(this,t),eD(this,t,arguments)}return ek(t,e),eb(t,[{key:"render",value:function(){var e=this,t=this.props,n=t.segments,r=t.slotMetrics.slots,a=t.className,o=1;return l().createElement("div",{className:eO(a,"rbc-row")},n.reduce(function(t,n,a){var i=n.event,s=n.left,l=n.right,c=n.span,u="_lvl_"+a,d=s-o,f=ar.renderEvent(e.props,i);return d&&t.push(ar.renderSpan(r,d,"".concat(u,"_gap"))),t.push(ar.renderSpan(r,c,u,f)),o=l+1,t},[]))}}])}(l().Component);function ao(e){var t=e.dateRange,n=e.unit,r=e.localizer;return{first:t[0],last:r.add(t[t.length-1],1,void 0===n?"day":n)}}function ai(e){var t,n,r,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1/0,o=[],i=[];for(t=0;t<e.length;t++){for(n=0,r=e[t];n<o.length&&function(e,t){return t.some(function(t){return t.left<=e.right&&t.right>=e.left})}(r,o[n]);n++);n>=a?i.push(r):(o[n]||(o[n]=[])).push(r)}for(t=0;t<o.length;t++)o[t].sort(function(e,t){return e.left-t.left});return{levels:o,extra:i}}function as(e,t,n,r,a){var o={start:r.start(e),end:r.end(e)};return a.inEventRange({event:o,range:{start:t,end:n}})}function al(e,t,n,r){var a={start:n.start(e),end:n.end(e),allDay:n.allDay(e)},o={start:n.start(t),end:n.end(t),allDay:n.allDay(t)};return r.sortEvents({evtA:a,evtB:o})}aa.defaultProps=em({},ar.defaultProps);var ac=function(e,t){return e.left<=t&&e.right>=t},au=function(e,t){return e.filter(function(e){return ac(e,t)}).map(function(e){return e.event})},ad=function(e){function t(){return eg(this,t),eD(this,t,arguments)}return ek(t,e),eb(t,[{key:"render",value:function(){for(var e=this.props,t=e.segments,n=e.slotMetrics.slots,r=ai(t).levels[0],a=1,o=1,i=[];a<=n;){var s="_lvl_"+a,c=r.filter(function(e){return ac(e,a)})[0]||{},u=c.event,d=c.left,f=c.right,p=c.span;if(!u){if(this.getHiddenEventsForSlot(t,a).length>0){var m=a-o;m&&i.push(ar.renderSpan(n,m,s+"_gap")),i.push(ar.renderSpan(n,1,s,this.renderShowMore(t,a))),o=a+=1;continue}a++;continue}var h=Math.max(0,d-o);if(this.canRenderSlotEvent(d,p)){var v=ar.renderEvent(this.props,u);h&&i.push(ar.renderSpan(n,h,s+"_gap")),i.push(ar.renderSpan(n,p,s,v)),o=a=f+1}else h&&i.push(ar.renderSpan(n,h,s+"_gap")),i.push(ar.renderSpan(n,1,s,this.renderShowMore(t,a))),o=a+=1}return l().createElement("div",{className:"rbc-row"},i)}},{key:"getHiddenEventsForSlot",value:function(e,t){var n=au(e,t),r=ai(e).levels[0].filter(function(e){return ac(e,t)}).map(function(e){return e.event});return n.filter(function(e){return!r.some(function(t){return t===e})})}},{key:"canRenderSlotEvent",value:function(e,t){var n=this.props.segments;return rn()(e,e+t).every(function(e){return 1===au(n,e).length})}},{key:"renderShowMore",value:function(e,t){var n=this,r=this.props,a=r.localizer,o=r.slotMetrics,i=r.components,s=o.getEventsForSlot(t),c=au(e,t),u=c.length;if(null!=i&&i.showMore){var d=i.showMore,f=o.getDateForSlot(t-1);return!!u&&l().createElement(d,{localizer:a,slotDate:f,slot:t,count:u,events:s,remainingEvents:c})}return!!u&&l().createElement("button",{type:"button",key:"sm_"+t,className:eO("rbc-button-link","rbc-show-more"),onClick:function(e){return n.showMore(t,e)}},a.messages.showMore(u,c,s))}},{key:"showMore",value:function(e,t){t.preventDefault(),t.stopPropagation(),this.props.onShowMore(e,t.target)}}])}(l().Component);ad.defaultProps=em({},ar.defaultProps);var af=function(e){var t=e.children;return l().createElement("div",{className:"rbc-row-content-scroll-container"},t)},ap=function(e,t){return e[0].range===t[0].range&&e[0].events===t[0].events},am=function(e){function t(){var e;eg(this,t);for(var n=arguments.length,r=Array(n),a=0;a<n;a++)r[a]=arguments[a];return(e=eD(this,t,[].concat(r))).handleSelectSlot=function(t){var n=e.props,r=n.range;(0,n.onSelectSlot)(r.slice(t.start,t.end+1),t)},e.handleShowMore=function(t,n){var r,a,o,i=e.props,s=i.range,l=i.onShowMore,c=e.slotMetrics(e.props),u=(r=e.containerRef.current,a=".rbc-row-bg",n7(r.querySelectorAll(a)))[0];u&&(o=u.children[t-1]),l(c.getEventsForSlot(t),s[t-1],o,t,n)},e.getContainer=function(){var t=e.props.container;return t?t():e.containerRef.current},e.renderHeadingCell=function(t,n){var r=e.props,a=r.renderHeader,o=r.getNow,i=r.localizer;return a({date:t,key:"header_".concat(n),className:eO("rbc-date-cell",i.isSameDate(t,o())&&"rbc-now")})},e.renderDummy=function(){var t=e.props,n=t.className,r=t.range,a=t.renderHeader,o=t.showAllEvents;return l().createElement("div",{className:n,ref:e.containerRef},l().createElement("div",{className:eO("rbc-row-content",o&&"rbc-row-content-scrollable")},a&&l().createElement("div",{className:"rbc-row",ref:e.headingRowRef},r.map(e.renderHeadingCell)),l().createElement("div",{className:"rbc-row",ref:e.eventRowRef},l().createElement("div",{className:"rbc-row-segment"},l().createElement("div",{className:"rbc-event"},l().createElement("div",{className:"rbc-event-content"},"\xa0"))))))},e.containerRef=(0,s.createRef)(),e.headingRowRef=(0,s.createRef)(),e.eventRowRef=(0,s.createRef)(),e.slotMetrics=function e(){return ro(function(t){for(var n=t.range,r=t.events,a=t.maxRows,o=t.minRows,i=t.accessors,s=t.localizer,l=ao({dateRange:n,localizer:s}),c=l.first,u=l.last,d=r.map(function(e){var t,r,a,o,l,c,u,d;return r=(t=ao({dateRange:n,localizer:s})).first,a=t.last,o=s.diff(r,a,"day"),l=s.max(s.startOf(i.start(e),"day"),r),c=s.min(s.ceil(i.end(e),"day"),a),u=re()(n,function(e){return s.isSameDate(e,l)}),{event:e,span:d=Math.max((d=Math.min(d=s.diff(l,c,"day"),o))-s.segmentOffset,1),left:u+1,right:Math.max(u+d,1)}}),f=ai(d,Math.max(a-1,1)),p=f.levels,m=f.extra,h=m.length>0?o-1:o;p.length<h;)p.push([]);return{first:c,last:u,levels:p,extra:m,range:n,slots:n.length,clone:function(n){return e()(em(em({},t),n))},getDateForSlot:function(e){return n[e]},getSlotForDate:function(e){return n.find(function(t){return s.isSameDate(t,e)})},getEventsForSlot:function(e){return d.filter(function(t){return t.left<=e&&t.right>=e}).map(function(e){return e.event})},continuesPrior:function(e){return s.continuesPrior(i.start(e),c)},continuesAfter:function(e){var t=i.start(e),n=i.end(e);return s.continuesAfter(t,n,u)}}},ap)}(),e}return ek(t,e),eb(t,[{key:"getRowLimit",value:function(){var e,t=n8(this.eventRowRef.current),n=null!=(e=this.headingRowRef)&&e.current?n8(this.headingRowRef.current):0;return Math.max(Math.floor((n8(this.containerRef.current)-n)/t),1)}},{key:"render",value:function(){var e=this.props,t=e.date,n=e.rtl,r=e.range,a=e.className,o=e.selected,i=e.selectable,s=e.renderForMeasure,c=e.accessors,u=e.getters,d=e.components,f=e.getNow,p=e.renderHeader,m=e.onSelect,h=e.localizer,v=e.onSelectStart,g=e.onSelectEnd,y=e.onDoubleClick,b=e.onKeyPress,w=e.resourceId,x=e.longPressThreshold,D=e.isAllDay,S=e.resizable,k=e.showAllEvents;if(s)return this.renderDummy();var E=this.slotMetrics(this.props),j=E.levels,N=E.extra,M=k?af:rw,T=d.weekWrapper,O={selected:o,accessors:c,getters:u,localizer:h,components:d,onSelect:m,onDoubleClick:y,onKeyPress:b,resourceId:w,slotMetrics:E,resizable:S};return l().createElement("div",{className:a,role:"rowgroup",ref:this.containerRef},l().createElement(an,{localizer:h,date:t,getNow:f,rtl:n,range:r,selectable:i,container:this.getContainer,getters:u,onSelectStart:v,onSelectEnd:g,onSelectSlot:this.handleSelectSlot,components:d,longPressThreshold:x,resourceId:w}),l().createElement("div",{className:eO("rbc-row-content",k&&"rbc-row-content-scrollable"),role:"row"},p&&l().createElement("div",{className:"rbc-row ",ref:this.headingRowRef},r.map(this.renderHeadingCell)),l().createElement(M,null,l().createElement(T,Object.assign({isAllDay:D},O,{rtl:this.props.rtl}),j.map(function(e,t){return l().createElement(aa,Object.assign({key:t,segments:e},O))}),!!N.length&&l().createElement(ad,Object.assign({segments:N,onShowMore:this.handleShowMore},O))))))}}])}(l().Component);am.defaultProps={minRows:0,maxRows:1/0};var ah=function(e){var t=e.label;return l().createElement("span",{role:"columnheader","aria-sort":"none"},t)},av=function(e){var t=e.label,n=e.drilldownView,r=e.onDrillDown;return n?l().createElement("button",{type:"button",className:"rbc-button-link",onClick:r},t):l().createElement("span",null,t)},ag=["date","className"],ay=function(e){function t(){var e;eg(this,t);for(var n=arguments.length,r=Array(n),a=0;a<n;a++)r[a]=arguments[a];return(e=eD(this,t,[].concat(r))).getContainer=function(){return e.containerRef.current},e.renderWeek=function(t,n){var r,a,o,i,s,c,u,d,f,p=e.props,m=p.events,h=p.components,v=p.selectable,g=p.getNow,y=p.selected,b=p.date,w=p.localizer,x=p.longPressThreshold,D=p.accessors,S=p.getters,k=p.showAllEvents,E=e.state,j=E.needLimitMeasure,N=E.rowLimit,M=(r=tv(m),a=t[0],o=t[t.length-1],i=r.filter(function(e){return as(e,a,o,D,w)}),s=tv(i),c=[],u=[],s.forEach(function(e){var t=D.start(e),n=D.end(e);w.daySpan(t,n)>1?c.push(e):u.push(e)}),d=c.sort(function(e,t){return al(e,t,D,w)}),f=u.sort(function(e,t){return al(e,t,D,w)}),[].concat(tv(d),tv(f)));return l().createElement(am,{key:n,ref:0===n?e.slotRowRef:void 0,container:e.getContainer,className:"rbc-month-row",getNow:g,date:b,range:t,events:M,maxRows:k?1/0:N,selected:y,selectable:v,components:h,accessors:D,getters:S,localizer:w,renderHeader:e.readerDateHeading,renderForMeasure:j,onShowMore:e.handleShowMore,onSelect:e.handleSelectEvent,onDoubleClick:e.handleDoubleClickEvent,onKeyPress:e.handleKeyPressEvent,onSelectSlot:e.handleSelectSlot,longPressThreshold:x,rtl:e.props.rtl,resizable:e.props.resizable,showAllEvents:k})},e.readerDateHeading=function(t){var n=t.date,r=t.className,a=ev(t,ag),o=e.props,i=o.date,s=o.getDrilldownView,c=o.localizer,u=c.neq(i,n,"month"),d=c.isSameDate(n,i),f=s(n),p=c.format(n,"dateFormat"),m=e.props.components.dateHeader||av;return l().createElement("div",Object.assign({},a,{className:eO(r,u&&"rbc-off-range",d&&"rbc-current"),role:"cell"}),l().createElement(m,{label:p,date:n,drilldownView:f,isOffRange:u,onDrillDown:function(t){return e.handleHeadingClick(n,f,t)}}))},e.handleSelectSlot=function(t,n){e._pendingSelection=e._pendingSelection.concat(t),clearTimeout(e._selectTimer),e._selectTimer=setTimeout(function(){return e.selectDates(n)})},e.handleHeadingClick=function(t,n,r){r.preventDefault(),e.clearSelection(),rG(e.props.onDrillDown,[t,n])},e.handleSelectEvent=function(){e.clearSelection();for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];rG(e.props.onSelectEvent,n)},e.handleDoubleClickEvent=function(){e.clearSelection();for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];rG(e.props.onDoubleClickEvent,n)},e.handleKeyPressEvent=function(){e.clearSelection();for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];rG(e.props.onKeyPressEvent,n)},e.handleShowMore=function(t,n,r,a,o){var i=e.props,s=i.popup,l=i.onDrillDown,c=i.onShowMore,u=i.getDrilldownView,d=i.doShowMoreDrillDown;if(e.clearSelection(),s){var f=tC(r,e.containerRef.current);e.setState({overlay:{date:n,events:t,position:f,target:o}})}else d&&rG(l,[n,u(n)||rD.DAY]);rG(c,[t,n,a])},e.overlayDisplay=function(){e.setState({overlay:null})},e.state={rowLimit:5,needLimitMeasure:!0,date:null},e.containerRef=(0,s.createRef)(),e.slotRowRef=(0,s.createRef)(),e._bgRows=[],e._pendingSelection=[],e}return ek(t,e),eb(t,[{key:"componentDidMount",value:function(){var e,t=this;this.state.needLimitMeasure&&this.measureRowLimit(this.props),window.addEventListener("resize",this._resizeListener=function(){e||tF(function(){e=!1,t.setState({needLimitMeasure:!0})})},!1)}},{key:"componentDidUpdate",value:function(){this.state.needLimitMeasure&&this.measureRowLimit(this.props)}},{key:"componentWillUnmount",value:function(){window.removeEventListener("resize",this._resizeListener,!1)}},{key:"render",value:function(){var e=this.props,t=e.date,n=e.localizer,r=e.className,a=n.visibleDays(t,n),o=ty()(a,7);return this._weekCount=o.length,l().createElement("div",{className:eO("rbc-month-view",r),role:"table","aria-label":"Month View",ref:this.containerRef},l().createElement("div",{className:"rbc-row rbc-month-header",role:"row"},this.renderHeaders(o[0])),o.map(this.renderWeek),this.props.popup&&this.renderOverlay())}},{key:"renderHeaders",value:function(e){var t=this.props,n=t.localizer,r=t.components,a=e[0],o=e[e.length-1],i=r.header||ah;return n.range(a,o,"day").map(function(e,t){return l().createElement("div",{key:"header_"+t,className:"rbc-header"},l().createElement(i,{date:e,localizer:n,label:n.format(e,"weekdayFormat")}))})}},{key:"renderOverlay",value:function(){var e,t,n=this,r=null!=(e=null==(t=this.state)?void 0:t.overlay)?e:{},a=this.props,o=a.accessors,i=a.localizer,s=a.components,c=a.getters,u=a.selected,d=a.popupOffset,f=a.handleDragStart;return l().createElement(r3,{overlay:r,accessors:o,localizer:i,components:s,getters:c,selected:u,popupOffset:d,ref:this.containerRef,handleKeyPressEvent:this.handleKeyPressEvent,handleSelectEvent:this.handleSelectEvent,handleDoubleClickEvent:this.handleDoubleClickEvent,handleDragStart:f,show:!!r.position,overlayDisplay:this.overlayDisplay,onHide:function(){return n.setState({overlay:null})}})}},{key:"measureRowLimit",value:function(){this.setState({needLimitMeasure:!1,rowLimit:this.slotRowRef.current.getRowLimit()})}},{key:"selectDates",value:function(e){var t=this._pendingSelection.slice();this._pendingSelection=[],t.sort(function(e,t){return e-t});var n=new Date(t[0]),r=new Date(t[t.length-1]);r.setDate(t[t.length-1].getDate()+1),rG(this.props.onSelectSlot,{slots:t,start:n,end:r,action:e.action,bounds:e.bounds,box:e.box})}},{key:"clearSelection",value:function(){clearTimeout(this._selectTimer),this._pendingSelection=[]}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n=e.date;return{date:n,needLimitMeasure:e.localizer.neq(n,t.date,"month")}}}])}(l().Component);ay.range=function(e,t){var n=t.localizer;return{start:n.firstVisibleDay(e,n),end:n.lastVisibleDay(e,n)}},ay.navigate=function(e,t,n){var r=n.localizer;switch(t){case rx.PREVIOUS:return r.add(e,-1,"month");case rx.NEXT:return r.add(e,1,"month");default:return e}},ay.title=function(e,t){return t.localizer.format(e,"monthHeaderFormat")};var ab=function(e){var t=e.min,n=e.max,r=e.step,a=e.slots,o=e.localizer;return"".concat(+o.startOf(t,"minutes"))+"".concat(+o.startOf(n,"minutes"))+"".concat(r,"-").concat(a)};function aw(e){for(var t=e.min,n=e.max,r=e.step,a=e.timeslots,o=e.localizer,i=ab({start:t,end:n,step:r,timeslots:a,localizer:o}),s=1+o.getTotalMin(t,n),l=o.getMinutesFromMidnight(t),c=Math.ceil((s-1)/(r*a)),u=c*a,d=Array(c),f=Array(u),p=0;p<c;p++){d[p]=Array(a);for(var m=0;m<a;m++){var h=p*a+m,v=h*r;f[h]=d[p][m]=o.getSlotDate(t,l,v)}}var g=f.length*r;function y(e){return Math.min(o.diff(t,e,"minutes")+o.getDstOffset(t,e),s)}return f.push(o.getSlotDate(t,l,g)),{groups:d,update:function(e){return ab(e)!==i?aw(e):this},dateIsInGroup:function(e,t){var r=d[t+1];return o.inRange(e,d[t][0],r?r[0]:n,"minutes")},nextSlot:function(e){var t=f[Math.min(f.findIndex(function(t){return t===e||o.eq(t,e)})+1,f.length-1)];return o.eq(t,e)&&(t=o.add(e,r,"minutes")),t},closestSlotToPosition:function(e){var t=Math.min(f.length-1,Math.max(0,Math.floor(e*u)));return f[t]},closestSlotFromPoint:function(e,t){var n=Math.abs(t.top-t.bottom);return this.closestSlotToPosition((e.y-t.top)/n)},closestSlotFromDate:function(e){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(o.lt(e,t,"minutes"))return f[0];if(o.gt(e,n,"minutes"))return f[f.length-1];var i=o.diff(t,e,"minutes");return f[(i-i%r)/r+a]},startsBeforeDay:function(e){return o.lt(e,t,"day")},startsAfterDay:function(e){return o.gt(e,n,"day")},startsBefore:function(e){return o.lt(o.merge(t,e),t,"minutes")},startsAfter:function(e){return o.gt(o.merge(n,e),n,"minutes")},getRange:function(e,a,i,s){i||(e=o.min(n,o.max(t,e))),s||(a=o.min(n,o.max(t,a)));var l=y(e),c=y(a),d=c>r*u&&!o.eq(n,a)?(l-r)/(r*u)*100:l/(r*u)*100;return{top:d,height:c/(r*u)*100-d,start:y(e),startDate:e,end:y(a),endDate:a}},getCurrentTimePosition:function(e){return y(e)/(r*u)*100}}}var ax=eb(function e(t,n){var r=n.accessors,a=n.slotMetrics;eg(this,e);var o=a.getRange(r.start(t),r.end(t)),i=o.start,s=o.startDate,l=o.end,c=o.endDate,u=o.top,d=o.height;this.start=i,this.end=l,this.startMs=+s,this.endMs=+c,this.top=u,this.height=d,this.data=t},[{key:"_width",get:function(){return this.rows?100/(this.rows.reduce(function(e,t){return Math.max(e,t.leaves.length+1)},0)+1):this.leaves?(100-this.container._width)/(this.leaves.length+1):this.row._width}},{key:"width",get:function(){var e=this._width,t=Math.min(100,1.7*this._width);if(this.rows)return t;if(this.leaves)return this.leaves.length>0?t:e;var n=this.row.leaves;return n.indexOf(this)===n.length-1?e:t}},{key:"xOffset",get:function(){if(this.rows)return 0;if(this.leaves)return this.container._width;var e=this.row,t=e.leaves,n=e.xOffset,r=e._width;return n+(t.indexOf(this)+1)*r}}]);function aD(e){for(var t=e.events,n=e.minimumStartDifference,r=e.slotMetrics,a=e.accessors,o=function(e){for(var t=rl()(e,["startMs",function(e){return-e.endMs}]),n=[];t.length>0;){var r=t.shift();n.push(r);for(var a=0;a<t.length;a++){var o=t[a];if(!(r.endMs>o.startMs)){if(a>0){var i=t.splice(a,1)[0];n.push(i)}break}}}return n}(t.map(function(e){return new ax(e,{slotMetrics:r,accessors:a})})),i=[],s=0;s<o.length;s++)if(function(){var e,t=o[s],r=i.find(function(e){return e.end>t.start||Math.abs(t.start-e.start)<n});if(!r)return t.rows=[],i.push(t),1;t.container=r;for(var a=null,l=r.rows.length-1;!a&&l>=0;l--)e=r.rows[l],(Math.abs(t.start-e.start)<n||t.start>e.start&&t.start<e.end)&&(a=r.rows[l]);a?(a.leaves.push(t),t.row=a):(t.leaves=[],r.rows.push(t))}())continue;return o.map(function(e){return{event:e.data,style:{top:e.top,height:e.height,width:e.width,xOffset:Math.max(0,e.xOffset)}}})}var aS={overlap:aD,"no-overlap":function(e){var t=aD({events:e.events,minimumStartDifference:e.minimumStartDifference,slotMetrics:e.slotMetrics,accessors:e.accessors});t.sort(function(e,t){return(e=e.style,t=t.style,e.top!==t.top)?e.top>t.top?1:-1:e.height!==t.height?e.top+e.height<t.top+t.height?1:-1:0});for(var n=0;n<t.length;++n)t[n].friends=[],delete t[n].style.left,delete t[n].style.left,delete t[n].idx,delete t[n].size;for(var r=0;r<t.length-1;++r)for(var a=t[r],o=a.style.top,i=a.style.top+a.style.height,s=r+1;s<t.length;++s){var l=t[s],c=l.style.top,u=l.style.top+l.style.height;(c>=o&&u<=i||u>o&&u<=i||c>=o&&c<i)&&(a.friends.push(l),l.friends.push(a))}for(var d=0;d<t.length;++d){for(var f=t[d],p=[],m=0;m<100;++m)p.push(1);for(var h=0;h<f.friends.length;++h)void 0!==f.friends[h].idx&&(p[f.friends[h].idx]=0);f.idx=p.indexOf(1)}for(var v=0;v<t.length;++v){var g=0;if(!t[v].size){var y=[];g=100/(function e(t,n,r){for(var a=0;a<t.friends.length;++a)if(!(r.indexOf(t.friends[a])>-1)){n=n>t.friends[a].idx?n:t.friends[a].idx,r.push(t.friends[a]);var o=e(t.friends[a],n,r);n=n>o?n:o}return n}(t[v],0,y)+1),t[v].size=g;for(var b=0;b<y.length;++b)y[b].size=g}}for(var w=0;w<t.length;++w){var x=t[w];x.style.left=x.idx*x.size;for(var D=0,S=0;S<x.friends.length;++S){var k=x.friends[S].idx;D=D>k?D:k}D<=x.idx&&(x.size=100-x.idx*x.size);var E=3*(0!==x.idx);x.style.width="calc(".concat(x.size,"% - ").concat(E,"px)"),x.style.height="calc(".concat(x.style.height,"% - 2px)"),x.style.xOffset="calc(".concat(x.style.left,"% + ").concat(E,"px)")}return t}},ak=function(e){function t(){return eg(this,t),eD(this,t,arguments)}return ek(t,e),eb(t,[{key:"render",value:function(){var e=this.props,t=e.renderSlot,n=e.resource,r=e.group,a=e.getters,o=e.components,i=(void 0===o?{}:o).timeSlotWrapper,s=void 0===i?rw:i,c=a?a.slotGroupProp(r):{};return l().createElement("div",Object.assign({className:"rbc-timeslot-group"},c),r.map(function(e,r){var o=a?a.slotProp(e,n):{};return l().createElement(s,{key:r,value:e,resource:n},l().createElement("div",Object.assign({},o,{className:eO("rbc-time-slot",o.className)}),t&&t(e,r)))}))}}])}(s.Component);function aE(e){return"string"==typeof e?e:e+"%"}function aj(e){var t=e.style,n=e.className,r=e.event,a=e.accessors,o=e.rtl,i=e.selected,s=e.label,c=e.continuesPrior,u=e.continuesAfter,d=e.getters,f=e.onClick,p=e.onDoubleClick,m=e.isBackgroundEvent,h=e.onKeyPress,v=e.components,g=v.event,y=v.eventWrapper,b=a.title(r),w=a.tooltip(r),x=a.end(r),D=a.start(r),S=d.eventProp(r,D,x,i),k=[l().createElement("div",{key:"1",className:"rbc-event-label"},s),l().createElement("div",{key:"2",className:"rbc-event-content"},g?l().createElement(g,{event:r,title:b}):b)],E=t.height,j=t.top,N=t.width,M=t.xOffset,T=em(em({},S.style),{},ef({top:aE(j),height:aE(E),width:aE(N)},o?"right":"left",aE(M)));return l().createElement(y,Object.assign({type:"time"},e),l().createElement("div",{role:"button",tabIndex:0,onClick:f,onDoubleClick:p,style:T,onKeyDown:h,title:w?("string"==typeof s?s+": ":"")+w:void 0,className:eO(m?"rbc-background-event":"rbc-event",n,S.className,{"rbc-selected":i,"rbc-event-continues-earlier":c,"rbc-event-continues-later":u})},k))}var aN=function(e){var t=e.children,n=e.className,r=e.style,a=e.innerRef;return l().createElement("div",{className:n,style:r,ref:a},t)},aM=l().forwardRef(function(e,t){return l().createElement(aN,Object.assign({},e,{innerRef:t}))}),aT=["dayProp"],aO=["eventContainerWrapper","timeIndicatorWrapper"],aC=function(e){function t(){var e;eg(this,t);for(var n=arguments.length,r=Array(n),a=0;a<n;a++)r[a]=arguments[a];return(e=eD(this,t,[].concat(r))).state={selecting:!1,timeIndicatorPosition:null},e.intervalTriggered=!1,e.renderEvents=function(t){var n=t.events,r=t.isBackgroundEvent,a=e.props,o=a.rtl,i=a.selected,s=a.accessors,c=a.localizer,u=a.getters,d=a.components,f=a.step,p=a.timeslots,m=a.dayLayoutAlgorithm,h=a.resizable,v=e.slotMetrics,g=c.messages;return(function(e){e.events,e.minimumStartDifference,e.slotMetrics,e.accessors;var t,n=e.dayLayoutAlgorithm,r=n;return(n in aS&&(r=aS[n]),(t=r)&&t.constructor&&t.call&&t.apply)?r.apply(this,arguments):[]})({events:n,accessors:s,slotMetrics:v,minimumStartDifference:Math.ceil(f*p/2),dayLayoutAlgorithm:m}).map(function(t,n){var a,f,p=t.event,m=t.style,y=s.end(p),b=s.start(p),w=null!=(a=s.eventId(p))?a:"evt_"+n,x="eventTimeRangeFormat",D=v.startsBeforeDay(b),S=v.startsAfterDay(y);D?x="eventTimeRangeEndFormat":S&&(x="eventTimeRangeStartFormat"),f=D&&S?g.allDay:c.format({start:b,end:y},x);var k=D||v.startsBefore(b),E=S||v.startsAfter(y);return l().createElement(aj,{style:m,event:p,label:f,key:w,getters:u,rtl:o,components:d,continuesPrior:k,continuesAfter:E,accessors:s,resource:e.props.resource,selected:rZ(p,i),onClick:function(t){return e._select(em(em(em({},p),e.props.resource&&{sourceResource:e.props.resource}),r&&{isBackgroundEvent:!0}),t)},onDoubleClick:function(t){return e._doubleClick(p,t)},isBackgroundEvent:r,onKeyPress:function(t){return e._keyPress(p,t)},resizable:h})})},e._selectable=function(){var t=e.containerRef.current,n=e.props,r=n.longPressThreshold,a=n.localizer,o=e._selector=new r6(function(){return t},{longPressThreshold:r}),i=function(t){var n=e.props.onSelecting,r=e.state||{},o=s(t),i=o.startDate,l=o.endDate;n&&(a.eq(r.startDate,i,"minutes")&&a.eq(r.endDate,l,"minutes")||!1===n({start:i,end:l,resourceId:e.props.resource}))||(e.state.start!==o.start||e.state.end!==o.end||e.state.selecting!==o.selecting)&&e.setState(o)},s=function(n){var r=e.slotMetrics.closestSlotFromPoint(n,ae(t));e.state.selecting||(e._initialSlot=r);var o=e._initialSlot;a.lte(o,r)?r=e.slotMetrics.nextSlot(r):a.gt(o,r)&&(o=e.slotMetrics.nextSlot(o));var i=e.slotMetrics.getRange(a.min(o,r),a.max(o,r));return em(em({},i),{},{selecting:!0,top:"".concat(i.top,"%"),height:"".concat(i.height,"%")})},l=function(t,n){if(!r8(e.containerRef.current,t)){var r=s(t),a=r.startDate,o=r.endDate;e._selectSlot({startDate:a,endDate:o,action:n,box:t})}e.setState({selecting:!1})};o.on("selecting",i),o.on("selectStart",i),o.on("beforeSelect",function(t){if("ignoreEvents"===e.props.selectable)return!r8(e.containerRef.current,t)}),o.on("click",function(e){return l(e,"click")}),o.on("doubleClick",function(e){return l(e,"doubleClick")}),o.on("select",function(t){e.state.selecting&&(e._selectSlot(em(em({},e.state),{},{action:"select",bounds:t})),e.setState({selecting:!1}))}),o.on("reset",function(){e.state.selecting&&e.setState({selecting:!1})})},e._teardownSelectable=function(){e._selector&&(e._selector.teardown(),e._selector=null)},e._selectSlot=function(t){for(var n=t.startDate,r=t.endDate,a=t.action,o=t.bounds,i=t.box,s=n,l=[];e.props.localizer.lte(s,r);)l.push(s),s=new Date(+s+60*e.props.step*1e3);rG(e.props.onSelectSlot,{slots:l,start:n,end:r,resourceId:e.props.resource,action:a,bounds:o,box:i})},e._select=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];rG(e.props.onSelectEvent,n)},e._doubleClick=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];rG(e.props.onDoubleClickEvent,n)},e._keyPress=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];rG(e.props.onKeyPressEvent,n)},e.slotMetrics=aw(e.props),e.containerRef=(0,s.createRef)(),e}return ek(t,e),eb(t,[{key:"componentDidMount",value:function(){this.props.selectable&&this._selectable(),this.props.isNow&&this.setTimeIndicatorPositionUpdateInterval()}},{key:"componentWillUnmount",value:function(){this._teardownSelectable(),this.clearTimeIndicatorInterval()}},{key:"componentDidUpdate",value:function(e,t){this.props.selectable&&!e.selectable&&this._selectable(),!this.props.selectable&&e.selectable&&this._teardownSelectable();var n=this.props,r=n.getNow,a=n.isNow,o=n.localizer,i=n.date,s=n.min,l=n.max,c=o.neq(e.getNow(),r(),"minutes");if(e.isNow!==a||c){if(this.clearTimeIndicatorInterval(),a){var u=!c&&o.eq(e.date,i,"minutes")&&t.timeIndicatorPosition===this.state.timeIndicatorPosition;this.setTimeIndicatorPositionUpdateInterval(u)}}else a&&(o.neq(e.min,s,"minutes")||o.neq(e.max,l,"minutes"))&&this.positionTimeIndicator()}},{key:"setTimeIndicatorPositionUpdateInterval",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.intervalTriggered||t||this.positionTimeIndicator(),this._timeIndicatorTimeout=window.setTimeout(function(){e.intervalTriggered=!0,e.positionTimeIndicator(),e.setTimeIndicatorPositionUpdateInterval()},6e4)}},{key:"clearTimeIndicatorInterval",value:function(){this.intervalTriggered=!1,window.clearTimeout(this._timeIndicatorTimeout)}},{key:"positionTimeIndicator",value:function(){var e=this.props,t=e.min,n=e.max,r=(0,e.getNow)();if(r>=t&&r<=n){var a=this.slotMetrics.getCurrentTimePosition(r);this.intervalTriggered=!0,this.setState({timeIndicatorPosition:a})}else this.clearTimeIndicatorInterval()}},{key:"render",value:function(){var e=this.props,t=e.date,n=e.max,r=e.rtl,a=e.isNow,o=e.resource,i=e.accessors,s=e.localizer,c=e.getters,u=c.dayProp,d=ev(c,aT),f=e.components,p=f.eventContainerWrapper,m=f.timeIndicatorWrapper,h=ev(f,aO);this.slotMetrics=this.slotMetrics.update(this.props);var v=this.slotMetrics,g=this.state,y=g.selecting,b=g.top,w=g.height,x=g.startDate,D=g.endDate,S=u(n,o),k=S.className,E=S.style,j={className:"rbc-current-time-indicator",style:{top:"".concat(this.state.timeIndicatorPosition,"%")}},N=h.dayColumnWrapper||aM;return l().createElement(N,{ref:this.containerRef,date:t,style:E,className:eO(k,"rbc-day-slot","rbc-time-column",a&&"rbc-now",a&&"rbc-today",y&&"rbc-slot-selecting"),slotMetrics:v,resource:o},v.groups.map(function(e,t){return l().createElement(ak,{key:t,group:e,resource:o,getters:d,components:h})}),l().createElement(p,{localizer:s,resource:o,accessors:i,getters:d,components:h,slotMetrics:v},l().createElement("div",{className:eO("rbc-events-container",r&&"rtl")},this.renderEvents({events:this.props.backgroundEvents,isBackgroundEvent:!0}),this.renderEvents({events:this.props.events}))),y&&l().createElement("div",{className:"rbc-slot-selection",style:{top:b,height:w}},l().createElement("span",null,s.format({start:x,end:D},"selectRangeFormat"))),a&&this.intervalTriggered&&l().createElement(m,j,l().createElement("div",j)))}}])}(l().Component);aC.defaultProps={dragThroughEvents:!0,timeslots:2};var aA=function(e){var t=e.label;return l().createElement(l().Fragment,null,t)},aP=function(e){function t(){var e;eg(this,t);for(var n=arguments.length,r=Array(n),a=0;a<n;a++)r[a]=arguments[a];return(e=eD(this,t,[].concat(r))).handleHeaderClick=function(t,n,r){r.preventDefault(),rG(e.props.onDrillDown,[t,n])},e.renderRow=function(t){var n=e.props,r=n.events,a=n.rtl,o=n.selectable,i=n.getNow,s=n.range,c=n.getters,u=n.localizer,d=n.accessors,f=n.components,p=n.resizable,m=d.resourceId(t),h=t?r.filter(function(e){return d.resource(e)===m}):r;return l().createElement(am,{isAllDay:!0,rtl:a,getNow:i,minRows:2,maxRows:e.props.allDayMaxRows+1,range:s,events:h,resourceId:m,className:"rbc-allday-cell",selectable:o,selected:e.props.selected,components:f,accessors:d,getters:c,localizer:u,onSelect:e.props.onSelectEvent,onShowMore:e.props.onShowMore,onDoubleClick:e.props.onDoubleClickEvent,onKeyPress:e.props.onKeyPressEvent,onSelectSlot:e.props.onSelectSlot,longPressThreshold:e.props.longPressThreshold,resizable:p})},e}return ek(t,e),eb(t,[{key:"renderHeaderCells",value:function(e){var t=this,n=this.props,r=n.localizer,a=n.getDrilldownView,o=n.getNow,i=n.getters.dayProp,s=n.components.header,c=void 0===s?ah:s,u=o();return e.map(function(e,n){var o=a(e),s=r.format(e,"dayFormat"),d=i(e),f=d.className,p=d.style,m=l().createElement(c,{date:e,label:s,localizer:r});return l().createElement("div",{key:n,style:p,className:eO("rbc-header",f,r.isSameDate(e,u)&&"rbc-today")},o?l().createElement("button",{type:"button",className:"rbc-button-link",onClick:function(n){return t.handleHeaderClick(e,o,n)}},m):l().createElement("span",null,m))})}},{key:"render",value:function(){var e=this,t=this.props,n=t.width,r=t.rtl,a=t.resources,o=t.range,i=t.events,s=t.getNow,c=t.accessors,u=t.selectable,d=t.components,f=t.getters,p=t.scrollRef,m=t.localizer,h=t.isOverflowing,v=t.components,g=v.timeGutterHeader,y=v.resourceHeader,b=void 0===y?aA:y,w=t.resizable,x={};h&&(x[r?"marginLeft":"marginRight"]="".concat(rc()-1,"px"));var D=a.groupEvents(i);return l().createElement("div",{style:x,ref:p,className:eO("rbc-time-header",h&&"rbc-overflowing")},l().createElement("div",{className:"rbc-label rbc-time-header-gutter",style:{width:n,minWidth:n,maxWidth:n}},g&&l().createElement(g,null)),a.map(function(t,n){var a=eT(t,2),i=a[0],p=a[1];return l().createElement("div",{className:"rbc-time-header-content",key:i||n},p&&l().createElement("div",{className:"rbc-row rbc-row-resource",key:"resource_".concat(n)},l().createElement("div",{className:"rbc-header"},l().createElement(b,{index:n,label:c.resourceTitle(p),resource:p}))),l().createElement("div",{className:"rbc-row rbc-time-header-cell".concat(o.length<=1?" rbc-time-header-cell-single-day":"")},e.renderHeaderCells(o)),l().createElement(am,{isAllDay:!0,rtl:r,getNow:s,minRows:2,maxRows:e.props.allDayMaxRows+1,range:o,events:D.get(i)||[],resourceId:p&&i,className:"rbc-allday-cell",selectable:u,selected:e.props.selected,components:d,accessors:c,getters:f,localizer:m,onSelect:e.props.onSelectEvent,onShowMore:e.props.onShowMore,onDoubleClick:e.props.onDoubleClickEvent,onKeyDown:e.props.onKeyPressEvent,onSelectSlot:e.props.onSelectSlot,longPressThreshold:e.props.longPressThreshold,resizable:w}))}))}}])}(l().Component),aR=function(e){function t(){var e;eg(this,t);for(var n=arguments.length,r=Array(n),a=0;a<n;a++)r[a]=arguments[a];return(e=eD(this,t,[].concat(r))).handleHeaderClick=function(t,n,r){r.preventDefault(),rG(e.props.onDrillDown,[t,n])},e}return ek(t,e),eb(t,[{key:"renderHeaderCells",value:function(e){var t=this,n=this.props,r=n.localizer,a=n.getDrilldownView,o=n.getNow,i=n.getters.dayProp,s=n.components,c=s.header,u=void 0===c?ah:c,d=s.resourceHeader,f=void 0===d?aA:d,p=n.resources,m=n.accessors,h=n.events,v=n.rtl,g=n.selectable,y=n.components,b=n.getters,w=n.resizable,x=o(),D=p.groupEvents(h);return e.map(function(n,s){var c=a(n),d=r.format(n,"dayFormat"),h=i(n),S=h.className,k=h.style,E=l().createElement(u,{date:n,label:d,localizer:r});return l().createElement("div",{key:s,className:"rbc-time-header-content rbc-resource-grouping"},l().createElement("div",{className:"rbc-row rbc-time-header-cell".concat(e.length<=1?" rbc-time-header-cell-single-day":"")},l().createElement("div",{style:k,className:eO("rbc-header",S,r.isSameDate(n,x)&&"rbc-today")},c?l().createElement("button",{type:"button",className:"rbc-button-link",onClick:function(e){return t.handleHeaderClick(n,c,e)}},E):l().createElement("span",null,E))),l().createElement("div",{className:"rbc-row"},p.map(function(e,t){var a=eT(e,2),o=a[0],i=a[1];return l().createElement("div",{key:"resource_".concat(o,"_").concat(t),className:eO("rbc-header",S,r.isSameDate(n,x)&&"rbc-today")},l().createElement(f,{index:t,label:m.resourceTitle(i),resource:i}))})),l().createElement("div",{className:"rbc-row rbc-m-b-negative-3 rbc-h-full"},p.map(function(e,a){var i=eT(e,2),s=i[0],c=i[1],u=(D.get(s)||[]).filter(function(e){return r.isSameDate(e.start,n)||r.isSameDate(e.end,n)});return l().createElement(am,{key:"resource_".concat(s,"_").concat(a),isAllDay:!0,rtl:v,getNow:o,minRows:2,maxRows:t.props.allDayMaxRows+1,range:[n],events:u,resourceId:c&&s,className:"rbc-allday-cell",selectable:g,selected:t.props.selected,components:y,accessors:m,getters:b,localizer:r,onSelect:t.props.onSelectEvent,onShowMore:t.props.onShowMore,onDoubleClick:t.props.onDoubleClickEvent,onKeyDown:t.props.onKeyPressEvent,onSelectSlot:t.props.onSelectSlot,longPressThreshold:t.props.longPressThreshold,resizable:w})})))})}},{key:"render",value:function(){var e=this.props,t=e.width,n=e.rtl,r=e.range,a=e.scrollRef,o=e.isOverflowing,i=e.components.timeGutterHeader,s={};return o&&(s[n?"marginLeft":"marginRight"]="".concat(rc()-1,"px")),l().createElement("div",{style:s,ref:a,className:eO("rbc-time-header",o&&"rbc-overflowing")},l().createElement("div",{className:"rbc-label rbc-time-header-gutter",style:{width:t,minWidth:t,maxWidth:t}},i&&l().createElement(i,null)),this.renderHeaderCells(r))}}])}(l().Component),a_=function(e){var t=e.min,n=e.max,r=e.timeslots,a=e.step,o=e.localizer,i=e.getNow,c=e.resource,u=e.components,d=e.getters,f=e.gutterRef,p=u.timeGutterWrapper,m=(0,s.useMemo)(function(){var e,r,a,i;return r=(e={min:t,max:n,localizer:o}).min,a=e.max,(i=e.localizer).getTimezoneOffset(r)!==i.getTimezoneOffset(a)?{start:i.add(r,-1,"day"),end:i.add(a,-1,"day")}:{start:r,end:a}},[null==t?void 0:t.toISOString(),null==n?void 0:n.toISOString(),o]),h=m.start,v=m.end,g=eT((0,s.useState)(aw({min:h,max:v,timeslots:r,step:a,localizer:o})),2),y=g[0],b=g[1];(0,s.useEffect)(function(){y&&b(y.update({min:h,max:v,timeslots:r,step:a,localizer:o}))},[null==h?void 0:h.toISOString(),null==v?void 0:v.toISOString(),r,a]);var w=(0,s.useCallback)(function(e,t){if(t)return null;var n=y.dateIsInGroup(i(),t);return l().createElement("span",{className:eO("rbc-label",n&&"rbc-now")},o.format(e,"timeGutterFormat"))},[y,o,i]);return l().createElement(p,{slotMetrics:y},l().createElement("div",{className:"rbc-time-gutter rbc-time-column",ref:f},y.groups.map(function(e,t){return l().createElement(ak,{key:t,group:e,resource:c,components:u,renderSlot:w,getters:d})})))},aI=l().forwardRef(function(e,t){return l().createElement(a_,Object.assign({gutterRef:t},e))}),aL={},aF=function(e){function t(e){var n;return eg(this,t),(n=eD(this,t,[e])).handleScroll=function(e){n.scrollRef.current&&(n.scrollRef.current.scrollLeft=e.target.scrollLeft)},n.handleResize=function(){tL(n.rafHandle),n.rafHandle=tF(n.checkOverflow)},n.handleKeyPressEvent=function(){n.clearSelection();for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];rG(n.props.onKeyPressEvent,t)},n.handleSelectEvent=function(){n.clearSelection();for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];rG(n.props.onSelectEvent,t)},n.handleDoubleClickEvent=function(){n.clearSelection();for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];rG(n.props.onDoubleClickEvent,t)},n.handleShowMore=function(e,t,r,a,o){var i=n.props,s=i.popup,l=i.onDrillDown,c=i.onShowMore,u=i.getDrilldownView,d=i.doShowMoreDrillDown;if(n.clearSelection(),s){var f=tC(r,n.containerRef.current);n.setState({overlay:{date:t,events:e,position:em(em({},f),{},{width:"200px"}),target:o}})}else d&&rG(l,[t,u(t)||rD.DAY]);rG(c,[e,t,a])},n.handleSelectAllDaySlot=function(e,t){var r=n.props.onSelectSlot,a=new Date(e[0]),o=new Date(e[e.length-1]);o.setDate(e[e.length-1].getDate()+1),rG(r,{slots:e,start:a,end:o,action:t.action,resourceId:t.resourceId})},n.overlayDisplay=function(){n.setState({overlay:null})},n.checkOverflow=function(){if(!n._updatingOverflow){var e=n.contentRef.current;if(null!=e&&e.scrollHeight){var t=e.scrollHeight>e.clientHeight;n.state.isOverflowing!==t&&(n._updatingOverflow=!0,n.setState({isOverflowing:t},function(){n._updatingOverflow=!1}))}}},n.memoizedResources=ro(function(e,t){return{map:function(n){return e?e.map(function(e,r){return n([t.resourceId(e),e],r)}):[n([aL,null],0)]},groupEvents:function(n){var r=new Map;return e?n.forEach(function(e){var n=t.resource(e)||aL;if(Array.isArray(n))n.forEach(function(t){var n=r.get(t)||[];n.push(e),r.set(t,n)});else{var a=r.get(n)||[];a.push(e),r.set(n,a)}}):r.set(aL,n),r}}}),n.state={gutterWidth:void 0,isOverflowing:null},n.scrollRef=l().createRef(),n.contentRef=l().createRef(),n.containerRef=l().createRef(),n._scrollRatio=null,n.gutterRef=(0,s.createRef)(),n}return ek(t,e),eb(t,[{key:"getSnapshotBeforeUpdate",value:function(){return this.checkOverflow(),null}},{key:"componentDidMount",value:function(){null==this.props.width&&this.measureGutter(),this.calculateScroll(),this.applyScroll(),window.addEventListener("resize",this.handleResize)}},{key:"componentWillUnmount",value:function(){window.removeEventListener("resize",this.handleResize),tL(this.rafHandle),this.measureGutterAnimationFrameRequest&&window.cancelAnimationFrame(this.measureGutterAnimationFrameRequest)}},{key:"componentDidUpdate",value:function(){this.applyScroll()}},{key:"renderDayColumn",value:function(e,t,n,r,a,o,i,s,c,u){var d=this.props,f=d.min,p=d.max,m=(r.get(t)||[]).filter(function(t){return o.inRange(e,i.start(t),i.end(t),"day")}),h=(a.get(t)||[]).filter(function(t){return o.inRange(e,i.start(t),i.end(t),"day")});return l().createElement(aC,Object.assign({},this.props,{localizer:o,min:o.merge(e,f),max:o.merge(e,p),resource:n&&t,components:s,isNow:o.isSameDate(e,u),key:"".concat(t,"-").concat(e),date:e,events:m,backgroundEvents:h,dayLayoutAlgorithm:c}))}},{key:"renderResourcesFirst",value:function(e,t,n,r,a,o,i,s,l){var c=this;return t.map(function(t){var u=eT(t,2),d=u[0],f=u[1];return e.map(function(e){return c.renderDayColumn(e,d,f,n,r,a,o,s,l,i)})})}},{key:"renderRangeFirst",value:function(e,t,n,r,a,o,i,s,c){var u=this;return e.map(function(e){return l().createElement("div",{style:{display:"flex",minHeight:"100%",flex:1},key:e},t.map(function(t){var d=eT(t,2),f=d[0],p=d[1];return l().createElement("div",{style:{flex:1},key:o.resourceId(p)},u.renderDayColumn(e,f,p,n,r,a,o,s,c,i))}))})}},{key:"renderEvents",value:function(e,t,n,r){var a=this.props,o=a.accessors,i=a.localizer,s=a.resourceGroupingLayout,l=a.components,c=a.dayLayoutAlgorithm,u=this.memoizedResources(this.props.resources,o),d=u.groupEvents(t),f=u.groupEvents(n);return s?this.renderRangeFirst(e,u,d,f,i,o,r,l,c):this.renderResourcesFirst(e,u,d,f,i,o,r,l,c)}},{key:"render",value:function(){var e,t=this.props,n=t.events,r=t.backgroundEvents,a=t.range,o=t.width,i=t.rtl,s=t.selected,c=t.getNow,u=t.resources,d=t.components,f=t.accessors,p=t.getters,m=t.localizer,h=t.min,v=t.max,g=t.showMultiDayTimes,y=t.longPressThreshold,b=t.resizable,w=t.resourceGroupingLayout;o=o||this.state.gutterWidth;var x=a[0],D=a[a.length-1];this.slots=a.length;var S=[],k=[],E=[];n.forEach(function(e){if(as(e,x,D,f,m)){var t=f.start(e),n=f.end(e);f.allDay(e)||m.startAndEndAreDateOnly(t,n)||!g&&!m.isSameDate(t,n)?S.push(e):k.push(e)}}),r.forEach(function(e){as(e,x,D,f,m)&&E.push(e)}),S.sort(function(e,t){return al(e,t,f,m)});var j={range:a,events:S,width:o,rtl:i,getNow:c,localizer:m,selected:s,allDayMaxRows:this.props.showAllEvents?1/0:null!=(e=this.props.allDayMaxRows)?e:1/0,resources:this.memoizedResources(u,f),selectable:this.props.selectable,accessors:f,getters:p,components:d,scrollRef:this.scrollRef,isOverflowing:this.state.isOverflowing,longPressThreshold:y,onSelectSlot:this.handleSelectAllDaySlot,onSelectEvent:this.handleSelectEvent,onShowMore:this.handleShowMore,onDoubleClickEvent:this.props.onDoubleClickEvent,onKeyPressEvent:this.props.onKeyPressEvent,onDrillDown:this.props.onDrillDown,getDrilldownView:this.props.getDrilldownView,resizable:b};return l().createElement("div",{className:eO("rbc-time-view",u&&"rbc-time-view-resources"),ref:this.containerRef},u&&u.length>1&&w?l().createElement(aR,j):l().createElement(aP,j),this.props.popup&&this.renderOverlay(),l().createElement("div",{ref:this.contentRef,className:"rbc-time-content",onScroll:this.handleScroll},l().createElement(aI,{date:x,ref:this.gutterRef,localizer:m,min:m.merge(x,h),max:m.merge(x,v),step:this.props.step,getNow:this.props.getNow,timeslots:this.props.timeslots,components:d,className:"rbc-time-gutter",getters:p}),this.renderEvents(a,k,E,c())))}},{key:"renderOverlay",value:function(){var e,t,n=this,r=null!=(e=null==(t=this.state)?void 0:t.overlay)?e:{},a=this.props,o=a.accessors,i=a.localizer,s=a.components,c=a.getters,u=a.selected,d=a.popupOffset,f=a.handleDragStart;return l().createElement(r3,{overlay:r,accessors:o,localizer:i,components:s,getters:c,selected:u,popupOffset:d,ref:this.containerRef,handleKeyPressEvent:this.handleKeyPressEvent,handleSelectEvent:this.handleSelectEvent,handleDoubleClickEvent:this.handleDoubleClickEvent,handleDragStart:f,show:!!r.position,overlayDisplay:this.overlayDisplay,onHide:function(){return n.setState({overlay:null})}})}},{key:"clearSelection",value:function(){clearTimeout(this._selectTimer),this._pendingSelection=[]}},{key:"measureGutter",value:function(){var e=this;this.measureGutterAnimationFrameRequest&&window.cancelAnimationFrame(this.measureGutterAnimationFrameRequest),this.measureGutterAnimationFrameRequest=window.requestAnimationFrame(function(){var t,n=null!=(t=e.gutterRef)&&t.current?ri(e.gutterRef.current):void 0;n&&e.state.gutterWidth!==n&&e.setState({gutterWidth:n})})}},{key:"applyScroll",value:function(){if(null!=this._scrollRatio&&!0===this.props.enableAutoScroll){var e=this.contentRef.current;e.scrollTop=e.scrollHeight*this._scrollRatio,this._scrollRatio=null}}},{key:"calculateScroll",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,t=e.min,n=e.max,r=e.scrollToTime,a=e.localizer,o=a.diff(a.merge(r,t),r,"milliseconds"),i=a.diff(t,n,"milliseconds");this._scrollRatio=o/i}}])}(s.Component);aF.defaultProps={step:30,timeslots:2,resourceGroupingLayout:!1};var az=["date","localizer","min","max","scrollToTime","enableAutoScroll"],aq=function(e){function t(){return eg(this,t),eD(this,t,arguments)}return ek(t,e),eb(t,[{key:"render",value:function(){var e=this.props,n=e.date,r=e.localizer,a=e.min,o=void 0===a?r.startOf(new Date,"day"):a,i=e.max,s=void 0===i?r.endOf(new Date,"day"):i,c=e.scrollToTime,u=void 0===c?r.startOf(new Date,"day"):c,d=e.enableAutoScroll,f=ev(e,az),p=t.range(n,{localizer:r});return l().createElement(aF,Object.assign({},f,{range:p,eventOffset:10,localizer:r,min:o,max:s,scrollToTime:u,enableAutoScroll:void 0===d||d}))}}])}(l().Component);aq.range=function(e,t){return[t.localizer.startOf(e,"day")]},aq.navigate=function(e,t,n){var r=n.localizer;switch(t){case rx.PREVIOUS:return r.add(e,-1,"day");case rx.NEXT:return r.add(e,1,"day");default:return e}},aq.title=function(e,t){return t.localizer.format(e,"dayHeaderFormat")};var aH=["date","localizer","min","max","scrollToTime","enableAutoScroll"],a$=function(e){function t(){return eg(this,t),eD(this,t,arguments)}return ek(t,e),eb(t,[{key:"render",value:function(){var e=this.props,n=e.date,r=e.localizer,a=e.min,o=void 0===a?r.startOf(new Date,"day"):a,i=e.max,s=void 0===i?r.endOf(new Date,"day"):i,c=e.scrollToTime,u=void 0===c?r.startOf(new Date,"day"):c,d=e.enableAutoScroll,f=ev(e,aH),p=t.range(n,this.props);return l().createElement(aF,Object.assign({},f,{range:p,eventOffset:15,localizer:r,min:o,max:s,scrollToTime:u,enableAutoScroll:void 0===d||d}))}}])}(l().Component);a$.defaultProps=aF.defaultProps,a$.navigate=function(e,t,n){var r=n.localizer;switch(t){case rx.PREVIOUS:return r.add(e,-1,"week");case rx.NEXT:return r.add(e,1,"week");default:return e}},a$.range=function(e,t){var n=t.localizer,r=n.startOfWeek(),a=n.startOf(e,"week",r),o=n.endOf(e,"week",r);return n.range(a,o)},a$.title=function(e,t){var n=t.localizer,r=ru(a$.range(e,{localizer:n})),a=r[0],o=r.slice(1);return n.format({start:a,end:o.pop()},"dayRangeHeaderFormat")};var aW=["date","localizer","min","max","scrollToTime","enableAutoScroll"];function aY(e,t){return a$.range(e,t).filter(function(e){return -1===[6,0].indexOf(e.getDay())})}var aU=function(e){function t(){return eg(this,t),eD(this,t,arguments)}return ek(t,e),eb(t,[{key:"render",value:function(){var e=this.props,t=e.date,n=e.localizer,r=e.min,a=void 0===r?n.startOf(new Date,"day"):r,o=e.max,i=void 0===o?n.endOf(new Date,"day"):o,s=e.scrollToTime,c=void 0===s?n.startOf(new Date,"day"):s,u=e.enableAutoScroll,d=ev(e,aW),f=aY(t,this.props);return l().createElement(aF,Object.assign({},d,{range:f,eventOffset:15,localizer:n,min:a,max:i,scrollToTime:c,enableAutoScroll:void 0===u||u}))}}])}(l().Component);function aV(e){var t=e.accessors,n=e.components,r=e.date,a=e.events,o=e.getters,i=e.length,c=e.localizer,u=e.onDoubleClickEvent,d=e.onSelectEvent,f=e.selected,p=(0,s.useRef)(null),m=(0,s.useRef)(null),h=(0,s.useRef)(null),v=(0,s.useRef)(null),g=(0,s.useRef)(null);(0,s.useEffect)(function(){w()});var y=function(e,r,a){var i=n.event,s=n.date;return(r=r.filter(function(n){return as(n,c.startOf(e,"day"),c.endOf(e,"day"),t,c)})).map(function(n,p){var m=t.title(n),h=t.end(n),v=t.start(n),g=o.eventProp(n,v,h,rZ(n,f)),y=0===p&&c.format(e,"agendaDateFormat"),w=0===p&&l().createElement("td",{rowSpan:r.length,className:"rbc-agenda-date-cell"},s?l().createElement(s,{day:e,label:y}):y);return l().createElement("tr",{key:a+"_"+p,className:g.className,style:g.style},w,l().createElement("td",{className:"rbc-agenda-time-cell"},b(e,n)),l().createElement("td",{className:"rbc-agenda-event-cell",onClick:function(e){return d&&d(n,e)},onDoubleClick:function(e){return u&&u(n,e)}},i?l().createElement(i,{event:n,title:m}):m))},[])},b=function(e,r){var a="",o=n.time,i=c.messages.allDay,s=t.end(r),u=t.start(r);return!t.allDay(r)&&(c.eq(u,s)?i=c.format(u,"agendaTimeFormat"):c.isSameDate(u,s)?i=c.format({start:u,end:s},"agendaTimeRangeFormat"):c.isSameDate(e,u)?i=c.format(u,"agendaTimeFormat"):c.isSameDate(e,s)&&(i=c.format(s,"agendaTimeFormat"))),c.gt(e,u,"day")&&(a="rbc-continues-prior"),c.lt(e,s,"day")&&(a+=" rbc-continues-after"),l().createElement("span",{className:a.trim()},o?l().createElement(o,{event:r,day:e,label:i}):i)},w=function(){if(g.current){var e=p.current,t=g.current.firstChild;if(t){var n,r=v.current.scrollHeight>v.current.clientHeight,a=[],o=a;if(a=[ri(t.children[0]),ri(t.children[1])],(o[0]!==a[0]||o[1]!==a[1])&&(m.current.style.width=a[0]+"px",h.current.style.width=a[1]+"px"),r){var i="rbc-header-overflowing";e.classList?e.classList.add(i):(e.classList?i&&e.classList.contains(i):-1!==(" "+(e.className.baseVal||e.className)+" ").indexOf(" "+i+" "))||("string"==typeof e.className?e.className=e.className+" "+i:e.setAttribute("class",(e.className&&e.className.baseVal||"")+" "+i)),e.style.marginRight=rc()+"px"}else n="rbc-header-overflowing",e.classList?e.classList.remove(n):"string"==typeof e.className?e.className=rd(e.className,n):e.setAttribute("class",rd(e.className&&e.className.baseVal||"",n))}}},x=c.messages,D=c.add(r,void 0===i?30:i,"day"),S=c.range(r,D,"day");return(a=a.filter(function(e){return as(e,c.startOf(r,"day"),c.endOf(D,"day"),t,c)})).sort(function(e,n){return t.start(e)-t.start(n)}),l().createElement("div",{className:"rbc-agenda-view"},0!==a.length?l().createElement(l().Fragment,null,l().createElement("table",{ref:p,className:"rbc-agenda-table"},l().createElement("thead",null,l().createElement("tr",null,l().createElement("th",{className:"rbc-header",ref:m},x.date),l().createElement("th",{className:"rbc-header",ref:h},x.time),l().createElement("th",{className:"rbc-header"},x.event)))),l().createElement("div",{className:"rbc-agenda-content",ref:v},l().createElement("table",{className:"rbc-agenda-table"},l().createElement("tbody",{ref:g},S.map(function(e,t){return y(e,a,t)}))))):l().createElement("span",{className:"rbc-agenda-empty"},x.noEventsInRange))}aU.defaultProps=aF.defaultProps,aU.range=aY,aU.navigate=a$.navigate,aU.title=function(e,t){var n=t.localizer,r=ru(aY(e,{localizer:n})),a=r[0],o=r.slice(1);return n.format({start:a,end:o.pop()},"dayRangeHeaderFormat")},aV.range=function(e,t){var n=t.length,r=t.localizer.add(e,void 0===n?30:n,"day");return{start:e,end:r}},aV.navigate=function(e,t,n){var r=n.length,a=void 0===r?30:r,o=n.localizer;switch(t){case rx.PREVIOUS:return o.add(e,-a,"day");case rx.NEXT:return o.add(e,a,"day");default:return e}},aV.title=function(e,t){var n=t.length,r=t.localizer,a=r.add(e,void 0===n?30:n,"day");return r.format({start:e,end:a},"agendaHeaderFormat")};var aB=ef(ef(ef(ef(ef({},rD.MONTH,ay),rD.WEEK,a$),rD.WORK_WEEK,aU),rD.DAY,aq),rD.AGENDA,aV),aG=["action","date","today"],aK=function(e){return function(t){var n;return n=null,"function"==typeof e?n=e(t):"string"==typeof e&&"object"===eu(t)&&null!=t&&e in t&&(n=t[e]),n}},aX=["view","date","getNow","onNavigate"],aQ=["view","toolbar","events","backgroundEvents","resourceGroupingLayout","style","className","elementProps","date","getNow","length","showMultiDayTimes","onShowMore","doShowMoreDrillDown","components","formats","messages","culture"];function aZ(e){if(Array.isArray(e))return e;for(var t=[],n=0,r=Object.entries(e);n<r.length;n++){var a=eT(r[n],2),o=a[0];a[1]&&t.push(o)}return t}var aJ=function(e){function t(){var e;eg(this,t);for(var n=arguments.length,r=Array(n),a=0;a<n;a++)r[a]=arguments[a];return(e=eD(this,t,[].concat(r))).getViews=function(){var t=e.props.views;return Array.isArray(t)?rb()(t,function(e,t){return e[t]=aB[t]},{}):"object"===eu(t)?rh()(t,function(e,t){return!0===e?aB[t]:e}):aB},e.getView=function(){return e.getViews()[e.props.view]},e.getDrilldownView=function(t){var n=e.props,r=n.view,a=n.drilldownView,o=n.getDrilldownView;return o?o(t,r,Object.keys(e.getViews())):a},e.handleRangeChange=function(t,n,r){var a=e.props,o=a.onRangeChange,i=a.localizer;o&&n.range&&o(n.range(t,{localizer:i}),r)},e.handleNavigate=function(t,n){var r=e.props,a=r.view,o=r.date,i=r.getNow,s=r.onNavigate,l=ev(r,aX),c=e.getView(),u=i();s(o=function(e,t){var n=t.action,r=t.date,a=t.today,o=ev(t,aG);switch(e="string"==typeof e?aB[e]:e,n){case rx.TODAY:r=a||new Date;break;case rx.DATE:break;default:eA()(e&&"function"==typeof e.navigate,"Calendar View components must implement a static `.navigate(date, action)` method.s"),r=e.navigate(r,n,o)}return r}(c,em(em({},l),{},{action:t,date:n||o||u,today:u})),a,t),e.handleRangeChange(o,c)},e.handleViewChange=function(t){t!==e.props.view&&-1!==aZ(e.props.views).indexOf(t)&&e.props.onView(t);var n=e.getViews();e.handleRangeChange(e.props.date||e.props.getNow(),n[t],t)},e.handleSelectEvent=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];rG(e.props.onSelectEvent,n)},e.handleDoubleClickEvent=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];rG(e.props.onDoubleClickEvent,n)},e.handleKeyPressEvent=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];rG(e.props.onKeyPressEvent,n)},e.handleSelectSlot=function(t){rG(e.props.onSelectSlot,t)},e.handleDrillDown=function(t,n){var r=e.props.onDrillDown;if(r)return void r(t,n,e.drilldownView);n&&e.handleViewChange(n),e.handleNavigate(rx.DATE,t)},e.state={context:t.getContext(e.props)},e}return ek(t,e),eb(t,[{key:"render",value:function(){var e=this.props,t=e.view,n=e.toolbar,r=e.events,a=e.backgroundEvents,o=e.resourceGroupingLayout,i=e.style,s=e.className,c=e.elementProps,u=e.date,d=e.getNow,f=e.length,p=e.showMultiDayTimes,m=e.onShowMore,h=e.doShowMoreDrillDown;e.components,e.formats,e.messages,e.culture;var v=ev(e,aQ);u=u||d();var g=this.getView(),y=this.state.context,b=y.accessors,w=y.components,x=y.getters,D=y.localizer,S=y.viewNames,k=w.toolbar||rB,E=g.title(u,{localizer:D,length:f});return l().createElement("div",Object.assign({},c,{className:eO(s,"rbc-calendar",v.rtl&&"rbc-rtl"),style:i}),n&&l().createElement(k,{date:u,view:t,views:S,label:E,onView:this.handleViewChange,onNavigate:this.handleNavigate,localizer:D}),l().createElement(g,Object.assign({},v,{events:r,backgroundEvents:a,date:u,getNow:d,length:f,localizer:D,getters:x,components:w,accessors:b,showMultiDayTimes:p,getDrilldownView:this.getDrilldownView,onNavigate:this.handleNavigate,onDrillDown:this.handleDrillDown,onSelectEvent:this.handleSelectEvent,onDoubleClickEvent:this.handleDoubleClickEvent,onKeyPressEvent:this.handleKeyPressEvent,onSelectSlot:this.handleSelectSlot,onShowMore:m,doShowMoreDrillDown:h,resourceGroupingLayout:o})))}}],[{key:"getDerivedStateFromProps",value:function(e){return{context:t.getContext(e)}}},{key:"getContext",value:function(e){var t,n,r,a=e.startAccessor,o=e.endAccessor,i=e.allDayAccessor,s=e.tooltipAccessor,l=e.titleAccessor,c=e.resourceAccessor,u=e.resourceIdAccessor,d=e.resourceTitleAccessor,f=e.eventIdAccessor,p=e.eventPropGetter,m=e.backgroundEventPropGetter,h=e.slotPropGetter,v=e.slotGroupPropGetter,g=e.dayPropGetter,y=e.view,b=e.views,w=e.localizer,x=e.culture,D=e.messages,S=e.components,k=void 0===S?{}:S,E=e.formats,j=aZ(b),N=(t=void 0===D?{}:D,em(em({},rK),t));return{viewNames:j,localizer:(n=void 0===E?{}:E,r=em(em({},w.formats),n),em(em({},w),{},{messages:N,startOfWeek:function(){return w.startOfWeek(x)},format:function(e,t){return w.format(e,r[t]||t,x)}})),getters:{eventProp:function(){return p&&p.apply(void 0,arguments)||{}},backgroundEventProp:function(){return m&&m.apply(void 0,arguments)||{}},slotProp:function(){return h&&h.apply(void 0,arguments)||{}},slotGroupProp:function(){return v&&v.apply(void 0,arguments)||{}},dayProp:function(){return g&&g.apply(void 0,arguments)||{}}},components:rp()(k[y]||{},rg()(k,j),{eventWrapper:rw,backgroundEventWrapper:rw,eventContainerWrapper:rw,dateCellWrapper:rw,weekWrapper:rw,timeSlotWrapper:rw,timeGutterWrapper:rw,timeIndicatorWrapper:rw}),accessors:{start:aK(a),end:aK(o),allDay:aK(i),tooltip:aK(s),title:aK(l),resource:aK(c),resourceId:aK(u),resourceTitle:aK(d),eventId:aK(f)}}}}])}(l().Component);aJ.defaultProps={events:[],backgroundEvents:[],elementProps:{},popup:!1,toolbar:!0,view:rD.MONTH,views:[rD.MONTH,rD.WEEK,rD.DAY,rD.AGENDA],step:30,length:30,allDayMaxRows:1/0,doShowMoreDrillDown:!0,drilldownView:rD.DAY,titleAccessor:"title",tooltipAccessor:"title",allDayAccessor:"allDay",startAccessor:"start",endAccessor:"end",resourceAccessor:"resourceId",resourceIdAccessor:"id",resourceTitleAccessor:"title",eventIdAccessor:"id",longPressThreshold:250,getNow:function(){return new Date},dayLayoutAlgorithm:"overlap"};var a0=function e(t,n,r){void 0===r&&(r=[]);var a,o=t.displayName||t.name||"Component",i=!!t&&("function"!=typeof t||t.prototype&&t.prototype.isReactComponent),s=Object.keys(n),c=s.map(eR);i||!r.length||eA()(!1);var u=function(e){function a(){for(var t,a=arguments.length,o=Array(a),i=0;i<a;i++)o[i]=arguments[i];(t=e.call.apply(e,[this].concat(o))||this).handlers=Object.create(null),s.forEach(function(e){var r=n[e];t.handlers[r]=function(n){if(t.props[r]){var a;t._notifying=!0;for(var o=arguments.length,i=Array(o>1?o-1:0),s=1;s<o;s++)i[s-1]=arguments[s];(a=t.props)[r].apply(a,[n].concat(i)),t._notifying=!1}t.unmounted||t.setState(function(t){var r,a=t.values;return{values:e_(Object.create(null),a,((r={})[e]=n,r))}})}}),r.length&&(t.attachRef=function(e){t.inner=e});var l=Object.create(null);return s.forEach(function(e){l[e]=t.props[eR(e)]}),t.state={values:l,prevProps:{}},t}a.prototype=Object.create(e.prototype),a.prototype.constructor=a,eS(a,e);var o=a.prototype;return o.shouldComponentUpdate=function(){return!this._notifying},a.getDerivedStateFromProps=function(e,t){var n=t.values,r=t.prevProps,a={values:e_(Object.create(null),n),prevProps:{}};return s.forEach(function(t){a.prevProps[t]=e[t],void 0===e[t]&&void 0!==r[t]&&(a.values[t]=e[eR(t)])}),a},o.componentWillUnmount=function(){this.unmounted=!0},o.render=function(){var e=this,n=this.props,r=n.innerRef,a=eh(n,["innerRef"]);c.forEach(function(e){delete a[e]});var o={};return s.forEach(function(t){var n=e.props[t];o[t]=void 0!==n?n:e.state.values[t]}),l().createElement(t,e_({},a,o,this.handlers,{ref:r||this.attachRef}))},a}(l().Component);!function(e){var t=e.prototype;if(!t||!t.isReactComponent)throw Error("Can only polyfill class components");if("function"==typeof e.getDerivedStateFromProps||"function"==typeof t.getSnapshotBeforeUpdate){var n=null,r=null,a=null;if("function"==typeof t.componentWillMount?n="componentWillMount":"function"==typeof t.UNSAFE_componentWillMount&&(n="UNSAFE_componentWillMount"),"function"==typeof t.componentWillReceiveProps?r="componentWillReceiveProps":"function"==typeof t.UNSAFE_componentWillReceiveProps&&(r="UNSAFE_componentWillReceiveProps"),"function"==typeof t.componentWillUpdate?a="componentWillUpdate":"function"==typeof t.UNSAFE_componentWillUpdate&&(a="UNSAFE_componentWillUpdate"),null!==n||null!==r||null!==a)throw Error("Unsafe legacy lifecycles will not be called for components using new component APIs.\n\n"+(e.displayName||e.name)+" uses "+("function"==typeof e.getDerivedStateFromProps?"getDerivedStateFromProps()":"getSnapshotBeforeUpdate()")+" but also contains the following legacy lifecycles:"+(null!==n?"\n  "+n:"")+(null!==r?"\n  "+r:"")+(null!==a?"\n  "+a:"")+"\n\nThe above lifecycles should be removed. Learn more about this warning here:\nhttps://fb.me/react-async-component-lifecycle-hooks");if("function"==typeof e.getDerivedStateFromProps&&(t.componentWillMount=eI,t.componentWillReceiveProps=eL),"function"==typeof t.getSnapshotBeforeUpdate){if("function"!=typeof t.componentDidUpdate)throw Error("Cannot polyfill getSnapshotBeforeUpdate() for components that do not define componentDidUpdate() on the prototype");t.componentWillUpdate=eF;var o=t.componentDidUpdate;t.componentDidUpdate=function(e,t,n){var r=this.__reactInternalSnapshotFlag?this.__reactInternalSnapshot:n;o.call(this,e,t,r)}}}}(u),u.displayName="Uncontrolled("+o+")",u.propTypes=e_({innerRef:function(){}},(a={},Object.keys(n).forEach(function(e){a[eR(e)]=eP}),a)),r.forEach(function(e){u.prototype[e]=function(){var t;return(t=this.inner)[e].apply(t,arguments)}});var d=u;return l().forwardRef&&((d=l().forwardRef(function(e,t){return l().createElement(u,e_({},e,{innerRef:t,__source:{fileName:"/Users/<USER>/src/uncontrollable/src/uncontrollable.js",lineNumber:128},__self:this}))})).propTypes=u.propTypes),d.ControlledComponent=t,d.deferControlTo=function(t,r,a){return void 0===r&&(r={}),e(t,e_({},n,r),a)},d}(aJ,{view:"onView",date:"onNavigate",selected:"onSelectEvent"}),a1=function(e,t,n){var r=e.start,a=e.end;return n.format(r,"LT",t)+" – "+n.format(a,"LT",t)},a2=function(e,t,n){var r=e.start,a=e.end;return n.format(r,"t",t)+" – "+n.format(a,"t",t)},a4=function(e,t,n){var r=e.start,a=e.end;return n.format(r,"t",t)+" – "+n.format(a,"t",t)},a3=function(e,t,n){var r=e.start,a=e.end;return n.format(r,{time:"short"},t)+" – "+n.format(a,{time:"short"},t)},a5=function(e,t,n){var r=e.start,a=e.end;return"".concat(n.format(r,"p",t)," – ").concat(n.format(a,"p",t))},a8={dateFormat:"dd",dayFormat:"dd eee",weekdayFormat:"ccc",selectRangeFormat:a5,eventTimeRangeFormat:a5,eventTimeRangeStartFormat:function(e,t,n){var r=e.start;return"".concat(n.format(r,"h:mma",t)," – ")},eventTimeRangeEndFormat:function(e,t,n){var r=e.end;return" – ".concat(n.format(r,"h:mma",t))},timeGutterFormat:"p",monthHeaderFormat:"MMMM yyyy",dayHeaderFormat:"cccc MMM dd",dayRangeHeaderFormat:function(e,t,n){var r=e.start,a=e.end;return"".concat(n.format(r,"MMMM dd",t)," – ").concat(n.format(a,e2(r,a,"month")?"dd":"MMMM dd",t))},agendaHeaderFormat:function(e,t,n){var r=e.start,a=e.end;return"".concat(n.format(r,"P",t)," – ").concat(n.format(a,"P",t))},agendaDateFormat:"ccc MMM dd",agendaTimeFormat:"p",agendaTimeRangeFormat:a5},a7=function(e,t,n){var r=e.start,a=e.end;return n.format(r,"LT",t)+" – "+n.format(a,"LT",t)},a6=n(44568),a9=n(83640),oe=n(20501),ot=n(78016);class on{validate(e,t){return!0}constructor(){this.subPriority=0}}class or extends on{constructor(e,t,n,r,a){super(),this.value=e,this.validateValue=t,this.setValue=n,this.priority=r,a&&(this.subPriority=a)}validate(e,t){return this.validateValue(e,this.value,t)}set(e,t,n){return this.setValue(e,t,this.value,n)}}class oa extends on{constructor(e,t){super(),this.priority=10,this.subPriority=-1,this.context=e||(e=>(0,F.w)(t,e))}set(e,t){return t.timestampIsSet?e:(0,F.w)(e,function(e,t){var n;let r="function"==typeof(n=t)&&n.prototype?.constructor===n?new t(0):(0,F.w)(t,0);return r.setFullYear(e.getFullYear(),e.getMonth(),e.getDate()),r.setHours(e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()),r}(e,this.context))}}class oo{run(e,t,n,r){let a=this.parse(e,t,n,r);return a?{setter:new or(a.value,this.validate,this.set,this.priority,this.subPriority),rest:a.rest}:null}validate(e,t,n){return!0}}class oi extends oo{parse(e,t,n){switch(t){case"G":case"GG":case"GGG":return n.era(e,{width:"abbreviated"})||n.era(e,{width:"narrow"});case"GGGGG":return n.era(e,{width:"narrow"});default:return n.era(e,{width:"wide"})||n.era(e,{width:"abbreviated"})||n.era(e,{width:"narrow"})}}set(e,t,n){return t.era=n,e.setFullYear(n,0,1),e.setHours(0,0,0,0),e}constructor(...e){super(...e),this.priority=140,this.incompatibleTokens=["R","u","t","T"]}}let os={month:/^(1[0-2]|0?\d)/,date:/^(3[0-1]|[0-2]?\d)/,dayOfYear:/^(36[0-6]|3[0-5]\d|[0-2]?\d?\d)/,week:/^(5[0-3]|[0-4]?\d)/,hour23h:/^(2[0-3]|[0-1]?\d)/,hour24h:/^(2[0-4]|[0-1]?\d)/,hour11h:/^(1[0-1]|0?\d)/,hour12h:/^(1[0-2]|0?\d)/,minute:/^[0-5]?\d/,second:/^[0-5]?\d/,singleDigit:/^\d/,twoDigits:/^\d{1,2}/,threeDigits:/^\d{1,3}/,fourDigits:/^\d{1,4}/,anyDigitsSigned:/^-?\d+/,singleDigitSigned:/^-?\d/,twoDigitsSigned:/^-?\d{1,2}/,threeDigitsSigned:/^-?\d{1,3}/,fourDigitsSigned:/^-?\d{1,4}/},ol={basicOptionalMinutes:/^([+-])(\d{2})(\d{2})?|Z/,basic:/^([+-])(\d{2})(\d{2})|Z/,basicOptionalSeconds:/^([+-])(\d{2})(\d{2})((\d{2}))?|Z/,extended:/^([+-])(\d{2}):(\d{2})|Z/,extendedOptionalSeconds:/^([+-])(\d{2}):(\d{2})(:(\d{2}))?|Z/};function oc(e,t){return e?{value:t(e.value),rest:e.rest}:e}function ou(e,t){let n=t.match(e);return n?{value:parseInt(n[0],10),rest:t.slice(n[0].length)}:null}function od(e,t){let n=t.match(e);if(!n)return null;if("Z"===n[0])return{value:0,rest:t.slice(1)};let r="+"===n[1]?1:-1,a=n[2]?parseInt(n[2],10):0,o=n[3]?parseInt(n[3],10):0,i=n[5]?parseInt(n[5],10):0;return{value:r*(a*L.s0+o*L.Cg+i*L._m),rest:t.slice(n[0].length)}}function of(e){return ou(os.anyDigitsSigned,e)}function op(e,t){switch(e){case 1:return ou(os.singleDigit,t);case 2:return ou(os.twoDigits,t);case 3:return ou(os.threeDigits,t);case 4:return ou(os.fourDigits,t);default:return ou(RegExp("^\\d{1,"+e+"}"),t)}}function om(e,t){switch(e){case 1:return ou(os.singleDigitSigned,t);case 2:return ou(os.twoDigitsSigned,t);case 3:return ou(os.threeDigitsSigned,t);case 4:return ou(os.fourDigitsSigned,t);default:return ou(RegExp("^-?\\d{1,"+e+"}"),t)}}function oh(e){switch(e){case"morning":return 4;case"evening":return 17;case"pm":case"noon":case"afternoon":return 12;default:return 0}}function ov(e,t){let n,r=t>0,a=r?t:1-t;if(a<=50)n=e||100;else{let t=a+50;n=e+100*Math.trunc(t/100)-100*(e>=t%100)}return r?n:1-n}function og(e){return e%400==0||e%4==0&&e%100!=0}class oy extends oo{parse(e,t,n){let r=e=>({year:e,isTwoDigitYear:"yy"===t});switch(t){case"y":return oc(op(4,e),r);case"yo":return oc(n.ordinalNumber(e,{unit:"year"}),r);default:return oc(op(t.length,e),r)}}validate(e,t){return t.isTwoDigitYear||t.year>0}set(e,t,n){let r=e.getFullYear();if(n.isTwoDigitYear){let t=ov(n.year,r);return e.setFullYear(t,0,1),e.setHours(0,0,0,0),e}let a="era"in t&&1!==t.era?1-n.year:n.year;return e.setFullYear(a,0,1),e.setHours(0,0,0,0),e}constructor(...e){super(...e),this.priority=130,this.incompatibleTokens=["Y","R","u","w","I","i","e","c","t","T"]}}var ob=n(45561);class ow extends oo{parse(e,t,n){let r=e=>({year:e,isTwoDigitYear:"YY"===t});switch(t){case"Y":return oc(op(4,e),r);case"Yo":return oc(n.ordinalNumber(e,{unit:"year"}),r);default:return oc(op(t.length,e),r)}}validate(e,t){return t.isTwoDigitYear||t.year>0}set(e,t,n,r){let a=(0,ob.h)(e,r);if(n.isTwoDigitYear){let t=ov(n.year,a);return e.setFullYear(t,0,r.firstWeekContainsDate),e.setHours(0,0,0,0),(0,Z.k)(e,r)}let o="era"in t&&1!==t.era?1-n.year:n.year;return e.setFullYear(o,0,r.firstWeekContainsDate),e.setHours(0,0,0,0),(0,Z.k)(e,r)}constructor(...e){super(...e),this.priority=130,this.incompatibleTokens=["y","R","u","Q","q","M","L","I","d","D","i","t","T"]}}var ox=n(65244);class oD extends oo{parse(e,t){return"R"===t?om(4,e):om(t.length,e)}set(e,t,n){let r=(0,F.w)(e,0);return r.setFullYear(n,0,4),r.setHours(0,0,0,0),(0,ox.b)(r)}constructor(...e){super(...e),this.priority=130,this.incompatibleTokens=["G","y","Y","u","Q","q","M","L","w","d","D","e","c","t","T"]}}class oS extends oo{parse(e,t){return"u"===t?om(4,e):om(t.length,e)}set(e,t,n){return e.setFullYear(n,0,1),e.setHours(0,0,0,0),e}constructor(...e){super(...e),this.priority=130,this.incompatibleTokens=["G","y","Y","R","w","I","i","e","c","t","T"]}}class ok extends oo{parse(e,t,n){switch(t){case"Q":case"QQ":return op(t.length,e);case"Qo":return n.ordinalNumber(e,{unit:"quarter"});case"QQQ":return n.quarter(e,{width:"abbreviated",context:"formatting"})||n.quarter(e,{width:"narrow",context:"formatting"});case"QQQQQ":return n.quarter(e,{width:"narrow",context:"formatting"});default:return n.quarter(e,{width:"wide",context:"formatting"})||n.quarter(e,{width:"abbreviated",context:"formatting"})||n.quarter(e,{width:"narrow",context:"formatting"})}}validate(e,t){return t>=1&&t<=4}set(e,t,n){return e.setMonth((n-1)*3,1),e.setHours(0,0,0,0),e}constructor(...e){super(...e),this.priority=120,this.incompatibleTokens=["Y","R","q","M","L","w","I","d","D","i","e","c","t","T"]}}class oE extends oo{parse(e,t,n){switch(t){case"q":case"qq":return op(t.length,e);case"qo":return n.ordinalNumber(e,{unit:"quarter"});case"qqq":return n.quarter(e,{width:"abbreviated",context:"standalone"})||n.quarter(e,{width:"narrow",context:"standalone"});case"qqqqq":return n.quarter(e,{width:"narrow",context:"standalone"});default:return n.quarter(e,{width:"wide",context:"standalone"})||n.quarter(e,{width:"abbreviated",context:"standalone"})||n.quarter(e,{width:"narrow",context:"standalone"})}}validate(e,t){return t>=1&&t<=4}set(e,t,n){return e.setMonth((n-1)*3,1),e.setHours(0,0,0,0),e}constructor(...e){super(...e),this.priority=120,this.incompatibleTokens=["Y","R","Q","M","L","w","I","d","D","i","e","c","t","T"]}}class oj extends oo{parse(e,t,n){let r=e=>e-1;switch(t){case"M":return oc(ou(os.month,e),r);case"MM":return oc(op(2,e),r);case"Mo":return oc(n.ordinalNumber(e,{unit:"month"}),r);case"MMM":return n.month(e,{width:"abbreviated",context:"formatting"})||n.month(e,{width:"narrow",context:"formatting"});case"MMMMM":return n.month(e,{width:"narrow",context:"formatting"});default:return n.month(e,{width:"wide",context:"formatting"})||n.month(e,{width:"abbreviated",context:"formatting"})||n.month(e,{width:"narrow",context:"formatting"})}}validate(e,t){return t>=0&&t<=11}set(e,t,n){return e.setMonth(n,1),e.setHours(0,0,0,0),e}constructor(...e){super(...e),this.incompatibleTokens=["Y","R","q","Q","L","w","I","D","i","e","c","t","T"],this.priority=110}}class oN extends oo{parse(e,t,n){let r=e=>e-1;switch(t){case"L":return oc(ou(os.month,e),r);case"LL":return oc(op(2,e),r);case"Lo":return oc(n.ordinalNumber(e,{unit:"month"}),r);case"LLL":return n.month(e,{width:"abbreviated",context:"standalone"})||n.month(e,{width:"narrow",context:"standalone"});case"LLLLL":return n.month(e,{width:"narrow",context:"standalone"});default:return n.month(e,{width:"wide",context:"standalone"})||n.month(e,{width:"abbreviated",context:"standalone"})||n.month(e,{width:"narrow",context:"standalone"})}}validate(e,t){return t>=0&&t<=11}set(e,t,n){return e.setMonth(n,1),e.setHours(0,0,0,0),e}constructor(...e){super(...e),this.priority=110,this.incompatibleTokens=["Y","R","q","Q","M","w","I","D","i","e","c","t","T"]}}var oM=n(27768);class oT extends oo{parse(e,t,n){switch(t){case"w":return ou(os.week,e);case"wo":return n.ordinalNumber(e,{unit:"week"});default:return op(t.length,e)}}validate(e,t){return t>=1&&t<=53}set(e,t,n,r){return(0,Z.k)(function(e,t,n){let r=(0,z.a)(e,n?.in),a=(0,oM.N)(r,n)-t;return r.setDate(r.getDate()-7*a),(0,z.a)(r,n?.in)}(e,n,r),r)}constructor(...e){super(...e),this.priority=100,this.incompatibleTokens=["y","R","u","q","Q","M","L","I","d","D","i","t","T"]}}var oO=n(67144);class oC extends oo{parse(e,t,n){switch(t){case"I":return ou(os.week,e);case"Io":return n.ordinalNumber(e,{unit:"week"});default:return op(t.length,e)}}validate(e,t){return t>=1&&t<=53}set(e,t,n){return(0,ox.b)(function(e,t,n){let r=(0,z.a)(e,void 0),a=(0,oO.s)(r,void 0)-t;return r.setDate(r.getDate()-7*a),r}(e,n))}constructor(...e){super(...e),this.priority=100,this.incompatibleTokens=["y","Y","u","q","Q","M","L","w","d","D","e","c","t","T"]}}let oA=[31,28,31,30,31,30,31,31,30,31,30,31],oP=[31,29,31,30,31,30,31,31,30,31,30,31];class oR extends oo{parse(e,t,n){switch(t){case"d":return ou(os.date,e);case"do":return n.ordinalNumber(e,{unit:"date"});default:return op(t.length,e)}}validate(e,t){let n=og(e.getFullYear()),r=e.getMonth();return n?t>=1&&t<=oP[r]:t>=1&&t<=oA[r]}set(e,t,n){return e.setDate(n),e.setHours(0,0,0,0),e}constructor(...e){super(...e),this.priority=90,this.subPriority=1,this.incompatibleTokens=["Y","R","q","Q","w","I","D","i","e","c","t","T"]}}class o_ extends oo{parse(e,t,n){switch(t){case"D":case"DD":return ou(os.dayOfYear,e);case"Do":return n.ordinalNumber(e,{unit:"date"});default:return op(t.length,e)}}validate(e,t){return og(e.getFullYear())?t>=1&&t<=366:t>=1&&t<=365}set(e,t,n){return e.setMonth(0,n),e.setHours(0,0,0,0),e}constructor(...e){super(...e),this.priority=90,this.subpriority=1,this.incompatibleTokens=["Y","R","q","Q","M","L","w","I","d","E","i","e","c","t","T"]}}var oI=n(95291);function oL(e,t,n){let r=(0,ot.q)(),a=n?.weekStartsOn??n?.locale?.options?.weekStartsOn??r.weekStartsOn??r.locale?.options?.weekStartsOn??0,o=(0,z.a)(e,n?.in),i=o.getDay(),s=7-a,l=t<0||t>6?t-(i+s)%7:((t%7+7)%7+s)%7-(i+s)%7;return(0,oI.f)(o,l,n)}class oF extends oo{parse(e,t,n){switch(t){case"E":case"EE":case"EEE":return n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"});case"EEEEE":return n.day(e,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"});default:return n.day(e,{width:"wide",context:"formatting"})||n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"})}}validate(e,t){return t>=0&&t<=6}set(e,t,n,r){return(e=oL(e,n,r)).setHours(0,0,0,0),e}constructor(...e){super(...e),this.priority=90,this.incompatibleTokens=["D","i","e","c","t","T"]}}class oz extends oo{parse(e,t,n,r){let a=e=>{let t=7*Math.floor((e-1)/7);return(e+r.weekStartsOn+6)%7+t};switch(t){case"e":case"ee":return oc(op(t.length,e),a);case"eo":return oc(n.ordinalNumber(e,{unit:"day"}),a);case"eee":return n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"});case"eeeee":return n.day(e,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"});default:return n.day(e,{width:"wide",context:"formatting"})||n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"})}}validate(e,t){return t>=0&&t<=6}set(e,t,n,r){return(e=oL(e,n,r)).setHours(0,0,0,0),e}constructor(...e){super(...e),this.priority=90,this.incompatibleTokens=["y","R","u","q","Q","M","L","I","d","D","E","i","c","t","T"]}}class oq extends oo{parse(e,t,n,r){let a=e=>{let t=7*Math.floor((e-1)/7);return(e+r.weekStartsOn+6)%7+t};switch(t){case"c":case"cc":return oc(op(t.length,e),a);case"co":return oc(n.ordinalNumber(e,{unit:"day"}),a);case"ccc":return n.day(e,{width:"abbreviated",context:"standalone"})||n.day(e,{width:"short",context:"standalone"})||n.day(e,{width:"narrow",context:"standalone"});case"ccccc":return n.day(e,{width:"narrow",context:"standalone"});case"cccccc":return n.day(e,{width:"short",context:"standalone"})||n.day(e,{width:"narrow",context:"standalone"});default:return n.day(e,{width:"wide",context:"standalone"})||n.day(e,{width:"abbreviated",context:"standalone"})||n.day(e,{width:"short",context:"standalone"})||n.day(e,{width:"narrow",context:"standalone"})}}validate(e,t){return t>=0&&t<=6}set(e,t,n,r){return(e=oL(e,n,r)).setHours(0,0,0,0),e}constructor(...e){super(...e),this.priority=90,this.incompatibleTokens=["y","R","u","q","Q","M","L","I","d","D","E","i","e","t","T"]}}class oH extends oo{parse(e,t,n){let r=e=>0===e?7:e;switch(t){case"i":case"ii":return op(t.length,e);case"io":return n.ordinalNumber(e,{unit:"day"});case"iii":return oc(n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"}),r);case"iiiii":return oc(n.day(e,{width:"narrow",context:"formatting"}),r);case"iiiiii":return oc(n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"}),r);default:return oc(n.day(e,{width:"wide",context:"formatting"})||n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"}),r)}}validate(e,t){return t>=1&&t<=7}set(e,t,n){return(e=function(e,t,n){let r=(0,z.a)(e,void 0),a=function(e,t){let n=(0,z.a)(e,t?.in).getDay();return 0===n?7:n}(r,void 0);return(0,oI.f)(r,t-a,n)}(e,n)).setHours(0,0,0,0),e}constructor(...e){super(...e),this.priority=90,this.incompatibleTokens=["y","Y","u","q","Q","M","L","w","d","D","E","e","c","t","T"]}}class o$ extends oo{parse(e,t,n){switch(t){case"a":case"aa":case"aaa":return n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"});case"aaaaa":return n.dayPeriod(e,{width:"narrow",context:"formatting"});default:return n.dayPeriod(e,{width:"wide",context:"formatting"})||n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"})}}set(e,t,n){return e.setHours(oh(n),0,0,0),e}constructor(...e){super(...e),this.priority=80,this.incompatibleTokens=["b","B","H","k","t","T"]}}class oW extends oo{parse(e,t,n){switch(t){case"b":case"bb":case"bbb":return n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"});case"bbbbb":return n.dayPeriod(e,{width:"narrow",context:"formatting"});default:return n.dayPeriod(e,{width:"wide",context:"formatting"})||n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"})}}set(e,t,n){return e.setHours(oh(n),0,0,0),e}constructor(...e){super(...e),this.priority=80,this.incompatibleTokens=["a","B","H","k","t","T"]}}class oY extends oo{parse(e,t,n){switch(t){case"B":case"BB":case"BBB":return n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"});case"BBBBB":return n.dayPeriod(e,{width:"narrow",context:"formatting"});default:return n.dayPeriod(e,{width:"wide",context:"formatting"})||n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"})}}set(e,t,n){return e.setHours(oh(n),0,0,0),e}constructor(...e){super(...e),this.priority=80,this.incompatibleTokens=["a","b","t","T"]}}class oU extends oo{parse(e,t,n){switch(t){case"h":return ou(os.hour12h,e);case"ho":return n.ordinalNumber(e,{unit:"hour"});default:return op(t.length,e)}}validate(e,t){return t>=1&&t<=12}set(e,t,n){let r=e.getHours()>=12;return r&&n<12?e.setHours(n+12,0,0,0):r||12!==n?e.setHours(n,0,0,0):e.setHours(0,0,0,0),e}constructor(...e){super(...e),this.priority=70,this.incompatibleTokens=["H","K","k","t","T"]}}class oV extends oo{parse(e,t,n){switch(t){case"H":return ou(os.hour23h,e);case"Ho":return n.ordinalNumber(e,{unit:"hour"});default:return op(t.length,e)}}validate(e,t){return t>=0&&t<=23}set(e,t,n){return e.setHours(n,0,0,0),e}constructor(...e){super(...e),this.priority=70,this.incompatibleTokens=["a","b","h","K","k","t","T"]}}class oB extends oo{parse(e,t,n){switch(t){case"K":return ou(os.hour11h,e);case"Ko":return n.ordinalNumber(e,{unit:"hour"});default:return op(t.length,e)}}validate(e,t){return t>=0&&t<=11}set(e,t,n){return e.getHours()>=12&&n<12?e.setHours(n+12,0,0,0):e.setHours(n,0,0,0),e}constructor(...e){super(...e),this.priority=70,this.incompatibleTokens=["h","H","k","t","T"]}}class oG extends oo{parse(e,t,n){switch(t){case"k":return ou(os.hour24h,e);case"ko":return n.ordinalNumber(e,{unit:"hour"});default:return op(t.length,e)}}validate(e,t){return t>=1&&t<=24}set(e,t,n){return e.setHours(n<=24?n%24:n,0,0,0),e}constructor(...e){super(...e),this.priority=70,this.incompatibleTokens=["a","b","h","H","K","t","T"]}}class oK extends oo{parse(e,t,n){switch(t){case"m":return ou(os.minute,e);case"mo":return n.ordinalNumber(e,{unit:"minute"});default:return op(t.length,e)}}validate(e,t){return t>=0&&t<=59}set(e,t,n){return e.setMinutes(n,0,0),e}constructor(...e){super(...e),this.priority=60,this.incompatibleTokens=["t","T"]}}class oX extends oo{parse(e,t,n){switch(t){case"s":return ou(os.second,e);case"so":return n.ordinalNumber(e,{unit:"second"});default:return op(t.length,e)}}validate(e,t){return t>=0&&t<=59}set(e,t,n){return e.setSeconds(n,0),e}constructor(...e){super(...e),this.priority=50,this.incompatibleTokens=["t","T"]}}class oQ extends oo{parse(e,t){return oc(op(t.length,e),e=>Math.trunc(e*Math.pow(10,-t.length+3)))}set(e,t,n){return e.setMilliseconds(n),e}constructor(...e){super(...e),this.priority=30,this.incompatibleTokens=["t","T"]}}var oZ=n(40520);class oJ extends oo{parse(e,t){switch(t){case"X":return od(ol.basicOptionalMinutes,e);case"XX":return od(ol.basic,e);case"XXXX":return od(ol.basicOptionalSeconds,e);case"XXXXX":return od(ol.extendedOptionalSeconds,e);default:return od(ol.extended,e)}}set(e,t,n){return t.timestampIsSet?e:(0,F.w)(e,e.getTime()-(0,oZ.G)(e)-n)}constructor(...e){super(...e),this.priority=10,this.incompatibleTokens=["t","T","x"]}}class o0 extends oo{parse(e,t){switch(t){case"x":return od(ol.basicOptionalMinutes,e);case"xx":return od(ol.basic,e);case"xxxx":return od(ol.basicOptionalSeconds,e);case"xxxxx":return od(ol.extendedOptionalSeconds,e);default:return od(ol.extended,e)}}set(e,t,n){return t.timestampIsSet?e:(0,F.w)(e,e.getTime()-(0,oZ.G)(e)-n)}constructor(...e){super(...e),this.priority=10,this.incompatibleTokens=["t","T","X"]}}class o1 extends oo{parse(e){return of(e)}set(e,t,n){return[(0,F.w)(e,1e3*n),{timestampIsSet:!0}]}constructor(...e){super(...e),this.priority=40,this.incompatibleTokens="*"}}class o2 extends oo{parse(e){return of(e)}set(e,t,n){return[(0,F.w)(e,n),{timestampIsSet:!0}]}constructor(...e){super(...e),this.priority=20,this.incompatibleTokens="*"}}let o4={G:new oi,y:new oy,Y:new ow,R:new oD,u:new oS,Q:new ok,q:new oE,M:new oj,L:new oN,w:new oT,I:new oC,d:new oR,D:new o_,E:new oF,e:new oz,c:new oq,i:new oH,a:new o$,b:new oW,B:new oY,h:new oU,H:new oV,K:new oB,k:new oG,m:new oK,s:new oX,S:new oQ,X:new oJ,x:new o0,t:new o1,T:new o2},o3=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,o5=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,o8=/^'([^]*?)'?$/,o7=/''/g,o6=/\S/,o9=/[a-zA-Z]/;n(99124),n(68843);var ie=n(32218),it=n(39979);let ir=function(e){var t=e.startOfWeek,n=e.getDay,r=e.format,a=e.locales;return new rV({formats:a8,firstOfWeek:function(e){return n(t(new Date,{locale:a[e]}))},format:function(e,t,n){return r(new Date(e),t,{locale:a[n]})}})}({format:N.GP,parse:function(e,t,n,r){let a=()=>(0,F.w)(r?.in||n,NaN),o=Object.assign({},(0,ot.q)()),i=r?.locale??o.locale??a6.c,s=r?.firstWeekContainsDate??r?.locale?.options?.firstWeekContainsDate??o.firstWeekContainsDate??o.locale?.options?.firstWeekContainsDate??1,l=r?.weekStartsOn??r?.locale?.options?.weekStartsOn??o.weekStartsOn??o.locale?.options?.weekStartsOn??0;if(!t)return e?a():(0,z.a)(n,r?.in);let c={firstWeekContainsDate:s,weekStartsOn:l,locale:i},u=[new oa(r?.in,n)],d=t.match(o5).map(e=>{let t=e[0];return t in a9.m?(0,a9.m[t])(e,i.formatLong):e}).join("").match(o3),f=[];for(let n of d){!r?.useAdditionalWeekYearTokens&&(0,oe.xM)(n)&&(0,oe.Ss)(n,t,e),!r?.useAdditionalDayOfYearTokens&&(0,oe.ef)(n)&&(0,oe.Ss)(n,t,e);let o=n[0],s=o4[o];if(s){let{incompatibleTokens:t}=s;if(Array.isArray(t)){let e=f.find(e=>t.includes(e.token)||e.token===o);if(e)throw RangeError(`The format string mustn't contain \`${e.fullToken}\` and \`${n}\` at the same time`)}else if("*"===s.incompatibleTokens&&f.length>0)throw RangeError(`The format string mustn't contain \`${n}\` and any other token at the same time`);f.push({token:o,fullToken:n});let r=s.run(e,n,i.match,c);if(!r)return a();u.push(r.setter),e=r.rest}else{if(o.match(o9))throw RangeError("Format string contains an unescaped latin alphabet character `"+o+"`");if("''"===n?n="'":"'"===o&&(n=n.match(o8)[1].replace(o7,"'")),0!==e.indexOf(n))return a();e=e.slice(n.length)}}if(e.length>0&&o6.test(e))return a();let p=u.map(e=>e.priority).sort((e,t)=>t-e).filter((e,t,n)=>n.indexOf(e)===t).map(e=>u.filter(t=>t.priority===e).sort((e,t)=>t.subPriority-e.subPriority)).map(e=>e[0]),m=(0,z.a)(n,r?.in);if(isNaN(+m))return a();let h={};for(let e of p){if(!e.validate(m,c))return a();let t=e.set(m,h,c);Array.isArray(t)?(m=t[0],Object.assign(h,t[1])):m=t}return m},startOfWeek:()=>(0,Z.k)(new Date,{locale:ea}),getDay:function(e,t){return(0,z.a)(e,t?.in).getDay()},locales:{"zh-CN":ea}});function ia({onNewAppointment:e,onEditAppointment:t,onDeleteAppointment:n}){let[r,a]=(0,s.useState)([]),[o,l]=(0,s.useState)(!0),[c,d]=(0,s.useState)("month"),[f,p]=(0,s.useState)(new Date),m=(0,s.useMemo)(()=>r.map(e=>{let t=function(e,t){let n,r,a=()=>(0,F.w)(void 0,NaN),o=(void 0)??2,i=function(e){let t,n={},r=e.split(q.dateTimeDelimiter);if(r.length>2)return n;if(/:/.test(r[0])?t=r[0]:(n.date=r[0],t=r[1],q.timeZoneDelimiter.test(n.date)&&(n.date=e.split(q.timeZoneDelimiter)[0],t=e.substr(n.date.length,e.length))),t){let e=q.timezone.exec(t);e?(n.time=t.replace(e[1],""),n.timezone=e[1]):n.time=t}return n}(e);if(i.date){let e=function(e,t){let n=RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+t)+"})|(\\d{2}|[+-]\\d{"+(2+t)+"})$)"),r=e.match(n);if(!r)return{year:NaN,restDateString:""};let a=r[1]?parseInt(r[1]):null,o=r[2]?parseInt(r[2]):null;return{year:null===o?a:100*o,restDateString:e.slice((r[1]||r[2]).length)}}(i.date,o);n=function(e,t){var n,r,a,o,i,s,l,c;if(null===t)return new Date(NaN);let u=e.match(H);if(!u)return new Date(NaN);let d=!!u[4],f=Y(u[1]),p=Y(u[2])-1,m=Y(u[3]),h=Y(u[4]),v=Y(u[5])-1;if(d){return(n=0,r=h,a=v,r>=1&&r<=53&&a>=0&&a<=6)?function(e,t,n){let r=new Date(0);r.setUTCFullYear(e,0,4);let a=r.getUTCDay()||7;return r.setUTCDate(r.getUTCDate()+((t-1)*7+n+1-a)),r}(t,h,v):new Date(NaN)}{let e=new Date(0);return(o=t,i=p,s=m,i>=0&&i<=11&&s>=1&&s<=(V[i]||(B(o)?29:28))&&(l=t,(c=f)>=1&&c<=(B(l)?366:365)))?(e.setUTCFullYear(t,p,Math.max(f,m)),e):new Date(NaN)}}(e.restDateString,e.year)}if(!n||isNaN(+n))return a();let s=+n,l=0;if(i.time&&isNaN(l=function(e){var t,n,r;let a=e.match($);if(!a)return NaN;let o=U(a[1]),i=U(a[2]),s=U(a[3]);return(t=o,n=i,r=s,24===t?0===n&&0===r:r>=0&&r<60&&n>=0&&n<60&&t>=0&&t<25)?o*L.s0+i*L.Cg+1e3*s:NaN}(i.time)))return a();if(i.timezone){if(isNaN(r=function(e){var t,n;if("Z"===e)return 0;let r=e.match(W);if(!r)return 0;let a="+"===r[1]?-1:1,o=parseInt(r[2]),i=r[3]&&parseInt(r[3])||0;return(t=0,(n=i)>=0&&n<=59)?a*(o*L.s0+i*L.Cg):NaN}(i.timezone)))return a()}else{let e=new Date(s+l),n=(0,z.a)(0,t?.in);return n.setFullYear(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()),n.setHours(e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds()),n}return(0,z.a)(s+l+r,t?.in)}(e.appointmentDate),n=function(e,t,n){let r=(0,z.a)(e,void 0);return r.setTime(r.getTime()+t*L.Cg),r}(t,e.durationInMinutes),r=function(e){let t=e.patient.fullName,n="consultation"===e.appointmentType?"咨询":"治疗";if("treatment"===e.appointmentType&&e.treatment)return`${t} - ${e.treatment.name}`;if("consultation"===e.appointmentType){let n=e.consultationType?({initial:"初次咨询","follow-up":"复诊咨询","price-inquiry":"价格咨询"})[e.consultationType]:"咨询";return`${t} - ${n}`}return`${t} - ${n}`}(e);return{id:e.id,title:r,start:t,end:n,resource:e,status:e.status,appointmentType:e.appointmentType}}),[r]),h=(0,s.useCallback)(async()=>{try{l(!0);let e=await w.RG.getAll({limit:1e3});a(e.docs)}catch(e){console.error("Failed to fetch appointments:",e),es.error.fetchFailed()}finally{l(!1)}},[]),y=(0,s.useCallback)(e=>{t?.(e.resource)},[t]),b=(0,s.useCallback)(({start:t})=>{e?.()},[e]),x=(0,s.useCallback)(e=>({style:{backgroundColor:({scheduled:"#3b82f6",confirmed:"#10b981",completed:"#6b7280",cancelled:"#ef4444","no-show":"#f59e0b"})[e.status]||"#6b7280",borderColor:({consultation:"#8b5cf6",treatment:"#06b6d4"})[e.appointmentType]||"#6b7280",borderWidth:"2px",borderStyle:"solid",color:"white",fontSize:"12px",padding:"2px 4px"}}),[]);return(0,i.jsxs)(ie.Zp,{className:"h-full","data-sentry-element":"Card","data-sentry-component":"AppointmentCalendar","data-sentry-source-file":"appointment-calendar.tsx",children:[(0,i.jsx)(ie.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"appointment-calendar.tsx",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)(ie.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"appointment-calendar.tsx",children:[(0,i.jsx)(v.A,{className:"h-5 w-5","data-sentry-element":"IconCalendar","data-sentry-source-file":"appointment-calendar.tsx"}),"预约日历"]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsxs)(u.$,{variant:"outline",size:"sm",onClick:h,disabled:o,"data-sentry-element":"Button","data-sentry-source-file":"appointment-calendar.tsx",children:[(0,i.jsx)(it.A,{className:"h-4 w-4 mr-2","data-sentry-element":"IconRefresh","data-sentry-source-file":"appointment-calendar.tsx"}),"刷新"]}),(0,i.jsxs)(u.$,{size:"sm",onClick:e,"data-sentry-element":"Button","data-sentry-source-file":"appointment-calendar.tsx",children:[(0,i.jsx)(g.A,{className:"h-4 w-4 mr-2","data-sentry-element":"IconPlus","data-sentry-source-file":"appointment-calendar.tsx"}),"新建预约"]})]})]})}),(0,i.jsx)(ie.Wu,{className:"p-4","data-sentry-element":"CardContent","data-sentry-source-file":"appointment-calendar.tsx",children:o?(0,i.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)(v.A,{className:"h-12 w-12 mx-auto mb-4 text-muted-foreground animate-pulse"}),(0,i.jsx)("p",{className:"text-muted-foreground",children:"加载预约数据中..."})]})}):(0,i.jsx)("div",{className:"h-[600px]",children:(0,i.jsx)(a0,{localizer:ir,events:m,startAccessor:"start",endAccessor:"end",view:c,onView:d,date:f,onNavigate:p,onSelectEvent:y,onSelectSlot:b,selectable:!0,eventPropGetter:x,messages:ei,culture:"zh-CN",formats:{monthHeaderFormat:"yyyy年MM月",dayHeaderFormat:"MM月dd日",dayRangeHeaderFormat:({start:e,end:t})=>`${(0,N.GP)(e,"MM月dd日",{locale:ea})} - ${(0,N.GP)(t,"MM月dd日",{locale:ea})}`,agendaDateFormat:"MM月dd日",agendaTimeFormat:"HH:mm",agendaTimeRangeFormat:({start:e,end:t})=>`${(0,N.GP)(e,"HH:mm",{locale:ea})} - ${(0,N.GP)(t,"HH:mm",{locale:ea})}`},components:{month:{dateHeader:({date:e,label:t})=>(0,i.jsx)("span",{className:"text-sm font-medium",children:t})},week:{header:({date:e,label:t})=>(0,i.jsx)("span",{className:"text-sm font-medium",children:t})},event:({event:e})=>(0,i.jsxs)("div",{className:"text-xs",children:[(0,i.jsx)("div",{className:"font-medium truncate",children:e.title}),(0,i.jsx)("div",{className:"opacity-75",children:"consultation"===e.appointmentType?"咨询":"治疗"})]})}})})})]})}var io=n(11035),ii=n(75922),is=n(2212),il=(0,y.A)("outline","calendar-check","IconCalendarCheck",[["path",{d:"M11.5 21h-5.5a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v6",key:"svg-0"}],["path",{d:"M16 3v4",key:"svg-1"}],["path",{d:"M8 3v4",key:"svg-2"}],["path",{d:"M4 11h16",key:"svg-3"}],["path",{d:"M15 19l2 2l4 -4",key:"svg-4"}]]),ic=n(79397),iu=n(22031);let id={scheduled:{label:"已预约",color:"bg-blue-100 text-blue-800 hover:bg-blue-200",icon:is.A,nextStates:["confirmed","cancelled"]},confirmed:{label:"已确认",color:"bg-green-100 text-green-800 hover:bg-green-200",icon:il,nextStates:["completed","no-show","cancelled"]},completed:{label:"已完成",color:"bg-gray-100 text-gray-800 hover:bg-gray-200",icon:f.A,nextStates:[]},cancelled:{label:"已取消",color:"bg-red-100 text-red-800 hover:bg-red-200",icon:p.A,nextStates:["scheduled"]},"no-show":{label:"未到诊",color:"bg-amber-100 text-amber-800 hover:bg-amber-200",icon:ic.A,nextStates:["scheduled","cancelled"]}},ip={scheduled:"预约",confirmed:"确认",completed:"完成",cancelled:"取消","no-show":"标记未到诊"};function im({appointment:e,onStatusChange:t,compact:n=!1}){let{hasPermission:r}=(0,d.It)(),[a,o]=(0,s.useState)(!1),[l,f]=(0,s.useState)({open:!1,newStatus:"",title:"",description:""}),p=e.status,m=id[p],h=m?.icon||is.A,v=r("canEditAllAppointments"),g=async t=>{if(!v)return void es.error.permissionDenied("更改预约状态");if("cancelled"===t||"no-show"===t||"completed"===t){let n=ip[t];f({open:!0,newStatus:t,title:`确认${n}预约`,description:`您确定要将 ${e.patient.fullName} 的预约状态更改为"${id[t].label}"吗？`});return}await y(t)},y=async n=>{o(!0);try{let r=await w.RG.update(e.id,{status:n});es.appointment.statusChanged(r,p,n),t?.(r,n)}catch(e){console.error("Failed to update appointment status:",e),es.error.saveFailed(!0)}finally{o(!1),f({...l,open:!1})}},b=()=>{y(l.newStatus)};return n?(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsxs)(c.E,{className:m?.color,children:[(0,i.jsx)(h,{className:"h-3 w-3 mr-1"}),m?.label]}),v&&m?.nextStates.length>0&&(0,i.jsxs)(io.rI,{children:[(0,i.jsx)(io.ty,{asChild:!0,children:(0,i.jsx)(u.$,{variant:"ghost",size:"sm",disabled:a,className:"h-6 w-6 p-0",children:(0,i.jsx)(iu.A,{className:"h-3 w-3"})})}),(0,i.jsx)(io.SQ,{align:"end",children:m.nextStates.map(e=>{let t=id[e].icon;return(0,i.jsxs)(io._2,{onClick:()=>g(e),children:[(0,i.jsx)(t,{className:"h-4 w-4 mr-2"}),ip[e]]},e)})})]}),(0,i.jsx)(ii.Lt,{open:l.open,onOpenChange:e=>f({...l,open:e}),children:(0,i.jsxs)(ii.EO,{children:[(0,i.jsxs)(ii.wd,{children:[(0,i.jsx)(ii.r7,{children:l.title}),(0,i.jsx)(ii.$v,{children:l.description})]}),(0,i.jsxs)(ii.ck,{children:[(0,i.jsx)(ii.Zr,{children:"取消"}),(0,i.jsx)(ii.Rx,{onClick:b,disabled:a,children:"确认"})]})]})})]}):(0,i.jsxs)("div",{className:"flex items-center gap-3","data-sentry-component":"AppointmentStatusManager","data-sentry-source-file":"appointment-status-manager.tsx",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsxs)(c.E,{className:m?.color,"data-sentry-element":"Badge","data-sentry-source-file":"appointment-status-manager.tsx",children:[(0,i.jsx)(h,{className:"h-4 w-4 mr-2","data-sentry-element":"StatusIcon","data-sentry-source-file":"appointment-status-manager.tsx"}),m?.label]}),(0,i.jsxs)("div",{className:"text-sm text-muted-foreground",children:["预约时间: ",new Date(e.appointmentDate).toLocaleString("zh-CN")]})]}),v&&m?.nextStates.length>0&&(0,i.jsxs)(io.rI,{children:[(0,i.jsx)(io.ty,{asChild:!0,children:(0,i.jsxs)(u.$,{variant:"outline",size:"sm",disabled:a,children:["更改状态",(0,i.jsx)(iu.A,{className:"h-4 w-4 ml-2"})]})}),(0,i.jsxs)(io.SQ,{align:"end",children:[(0,i.jsx)("div",{className:"px-2 py-1.5 text-sm font-medium text-muted-foreground",children:"可用操作"}),(0,i.jsx)(io.mB,{}),m.nextStates.map(e=>{let t=id[e].icon;return(0,i.jsxs)(io._2,{onClick:()=>g(e),children:[(0,i.jsx)(t,{className:"h-4 w-4 mr-2"}),ip[e]]},e)})]})]}),(0,i.jsx)(ii.Lt,{open:l.open,onOpenChange:e=>f({...l,open:e}),"data-sentry-element":"AlertDialog","data-sentry-source-file":"appointment-status-manager.tsx",children:(0,i.jsxs)(ii.EO,{"data-sentry-element":"AlertDialogContent","data-sentry-source-file":"appointment-status-manager.tsx",children:[(0,i.jsxs)(ii.wd,{"data-sentry-element":"AlertDialogHeader","data-sentry-source-file":"appointment-status-manager.tsx",children:[(0,i.jsx)(ii.r7,{"data-sentry-element":"AlertDialogTitle","data-sentry-source-file":"appointment-status-manager.tsx",children:l.title}),(0,i.jsx)(ii.$v,{"data-sentry-element":"AlertDialogDescription","data-sentry-source-file":"appointment-status-manager.tsx",children:l.description})]}),(0,i.jsxs)(ii.ck,{"data-sentry-element":"AlertDialogFooter","data-sentry-source-file":"appointment-status-manager.tsx",children:[(0,i.jsx)(ii.Zr,{"data-sentry-element":"AlertDialogCancel","data-sentry-source-file":"appointment-status-manager.tsx",children:"取消"}),(0,i.jsx)(ii.Rx,{onClick:b,disabled:a,"data-sentry-element":"AlertDialogAction","data-sentry-source-file":"appointment-status-manager.tsx",children:"确认"})]})]})})]})}var ih=n(12772),iv=n(24368),ig=n(4684),iy=n(36612),ib=n(87483),iw=n(42354),ix=n(24582),iD="Switch",[iS,ik]=(0,ig.A)(iD),[iE,ij]=iS(iD),iN=s.forwardRef((e,t)=>{let{__scopeSwitch:n,name:r,checked:a,defaultChecked:o,required:l,disabled:c,value:u="on",onCheckedChange:d,form:f,...p}=e,[m,h]=s.useState(null),v=(0,iv.s)(t,e=>h(e)),g=s.useRef(!1),y=!m||f||!!m.closest("form"),[b=!1,w]=(0,iy.i)({prop:a,defaultProp:o,onChange:d});return(0,i.jsxs)(iE,{scope:n,checked:b,disabled:c,children:[(0,i.jsx)(ix.sG.button,{type:"button",role:"switch","aria-checked":b,"aria-required":l,"data-state":iC(b),"data-disabled":c?"":void 0,disabled:c,value:u,...p,ref:v,onClick:(0,ih.m)(e.onClick,e=>{w(e=>!e),y&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})}),y&&(0,i.jsx)(iO,{control:m,bubbles:!g.current,name:r,value:u,checked:b,required:l,disabled:c,form:f,style:{transform:"translateX(-100%)"}})]})});iN.displayName=iD;var iM="SwitchThumb",iT=s.forwardRef((e,t)=>{let{__scopeSwitch:n,...r}=e,a=ij(iM,n);return(0,i.jsx)(ix.sG.span,{"data-state":iC(a.checked),"data-disabled":a.disabled?"":void 0,...r,ref:t})});iT.displayName=iM;var iO=e=>{let{control:t,checked:n,bubbles:r=!0,...a}=e,o=s.useRef(null),l=(0,ib.Z)(n),c=(0,iw.X)(t);return s.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(l!==n&&t){let a=new Event("click",{bubbles:r});t.call(e,n),e.dispatchEvent(a)}},[l,n,r]),(0,i.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n,...a,tabIndex:-1,ref:o,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function iC(e){return e?"checked":"unchecked"}function iA({className:e,...t}){return(0,i.jsx)(iN,{"data-slot":"switch",className:(0,_.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,"data-sentry-element":"SwitchPrimitive.Root","data-sentry-component":"Switch","data-sentry-source-file":"switch.tsx",children:(0,i.jsx)(iT,{"data-slot":"switch-thumb",className:(0,_.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0"),"data-sentry-element":"SwitchPrimitive.Thumb","data-sentry-source-file":"switch.tsx"})})}var iP=n(18984),iR=n(63184),i_=n(12305),iI=n(33005),iL=n(68343),iF=(0,y.A)("outline","message","IconMessage",[["path",{d:"M8 9h8",key:"svg-0"}],["path",{d:"M8 13h6",key:"svg-1"}],["path",{d:"M18 4a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-5l-5 3v-3h-2a3 3 0 0 1 -3 -3v-8a3 3 0 0 1 3 -3h12z",key:"svg-2"}]]);class iz{scheduleReminders(e,t){let n={...this.defaultSettings,...t};if(!n.enabled)return;this.clearReminders(e.id);let r=new Date(e.appointmentDate),a=new Date;n.reminderTimes.forEach(t=>{let o=new Date(r.getTime()-60*t*1e3);o>a&&n.methods.forEach(n=>{let r={id:`${e.id}-${t}-${n}`,appointmentId:e.id,reminderTime:o,method:n,status:"pending",createdAt:new Date};this.scheduleReminder(r,e)})})}scheduleReminder(e,t){let n=new Date,r=e.reminderTime.getTime()-n.getTime();if(r<=0)return void this.sendReminder(e,t);let a=setTimeout(()=>{this.sendReminder(e,t)},r);this.timers.set(e.id,a);let o=this.reminders.get(t.id)||[];o.push(e),this.reminders.set(t.id,o),console.log(`Scheduled reminder for appointment ${t.id} at ${e.reminderTime.toISOString()}`)}async sendReminder(e,t){try{let n=Math.floor((new Date(t.appointmentDate).getTime()-new Date().getTime())/6e4);switch(e.method){case"notification":this.sendNotificationReminder(t,n);break;case"email":await this.sendEmailReminder(t,n);break;case"sms":await this.sendSMSReminder(t,n)}e.status="sent",es.reminder.reminderSet(t,eo(e.reminderTime))}catch(t){console.error("Failed to send reminder:",t),e.status="failed"}finally{let t=this.timers.get(e.id);t&&(clearTimeout(t),this.timers.delete(e.id))}}sendNotificationReminder(e,t){if("Notification"in window)if("granted"===Notification.permission){let n=new Notification("预约提醒",{body:`${e.patient.fullName} 的预约将在 ${t} 分钟后开始`,icon:"/favicon.ico",tag:`appointment-${e.id}`});setTimeout(()=>n.close(),1e4)}else"denied"!==Notification.permission&&Notification.requestPermission().then(n=>{"granted"===n&&this.sendNotificationReminder(e,t)});es.reminder.upcoming(e,t)}async sendEmailReminder(e,t){console.log(`Email reminder would be sent for appointment ${e.id}`),console.log("Email data:",{to:e.patient.email,subject:"预约提醒",body:`您好 ${e.patient.fullName}，您的预约将在 ${t} 分钟后开始。`,appointmentDetails:{date:eo(new Date(e.appointmentDate)),treatment:e.treatment?.name||"咨询",practitioner:`${e.practitioner.firstName} ${e.practitioner.lastName}`}})}async sendSMSReminder(e,t){console.log(`SMS reminder would be sent for appointment ${e.id}`),console.log("SMS data:",{to:e.patient.phone,message:`预约提醒：${e.patient.fullName}，您的预约将在 ${t} 分钟后开始。时间：${eo(new Date(e.appointmentDate))}`})}clearReminders(e){(this.reminders.get(e)||[]).forEach(e=>{let t=this.timers.get(e.id);t&&(clearTimeout(t),this.timers.delete(e.id))}),this.reminders.delete(e),console.log(`Cleared reminders for appointment ${e}`)}getReminders(e){return this.reminders.get(e)||[]}checkOverdueAppointments(e){let t=new Date;e.forEach(e=>{new Date(e.appointmentDate)<t&&("scheduled"===e.status||"confirmed"===e.status)&&es.reminder.overdue(e)})}async requestNotificationPermission(){return"Notification"in window&&"granted"===await Notification.requestPermission()}isNotificationSupported(){return"Notification"in window&&"granted"===Notification.permission}updateSettings(e){this.defaultSettings={...this.defaultSettings,...e}}getSettings(){return{...this.defaultSettings}}constructor(){this.reminders=new Map,this.timers=new Map,this.defaultSettings={enabled:!0,reminderTimes:[60,15],methods:["notification"]}}}let iq=new iz;function iH({onSettingsChange:e}){let[t,n]=(0,s.useState)(!1),[r,a]=(0,s.useState)(iq.getSettings()),[o,l]=(0,s.useState)(""),[d,f]=(0,s.useState)(iq.isNotificationSupported()),p=t=>{let n={...r,...t};a(n),iq.updateSettings(n),e?.(n)},m=e=>{p({reminderTimes:r.reminderTimes.filter(t=>t!==e)}),I.toast.success("提醒时间删除成功")},v=(e,t)=>{let n=[...r.methods];t?n.includes(e)||n.push(e):n=n.filter(t=>t!==e),p({methods:n})},y=async()=>{let e=await iq.requestNotificationPermission();f(e),e?I.toast.success("通知权限已授予"):I.toast.error("通知权限被拒绝")},b=e=>{if(e<60)return`${e} 分钟前`;{let t=Math.floor(e/60),n=e%60;return 0===n?`${t} 小时前`:`${t} 小时 ${n} 分钟前`}};return(0,i.jsxs)(T.lG,{open:t,onOpenChange:n,"data-sentry-element":"Dialog","data-sentry-component":"AppointmentReminderSettings","data-sentry-source-file":"appointment-reminder-settings.tsx",children:[(0,i.jsx)(T.zM,{asChild:!0,"data-sentry-element":"DialogTrigger","data-sentry-source-file":"appointment-reminder-settings.tsx",children:(0,i.jsxs)(u.$,{variant:"outline",size:"sm","data-sentry-element":"Button","data-sentry-source-file":"appointment-reminder-settings.tsx",children:[(0,i.jsx)(i_.A,{className:"h-4 w-4 mr-2","data-sentry-element":"IconSettings","data-sentry-source-file":"appointment-reminder-settings.tsx"}),"提醒设置"]})}),(0,i.jsxs)(T.Cf,{className:"max-w-md","data-sentry-element":"DialogContent","data-sentry-source-file":"appointment-reminder-settings.tsx",children:[(0,i.jsxs)(T.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"appointment-reminder-settings.tsx",children:[(0,i.jsxs)(T.L3,{className:"flex items-center gap-2","data-sentry-element":"DialogTitle","data-sentry-source-file":"appointment-reminder-settings.tsx",children:[(0,i.jsx)(iI.A,{className:"h-5 w-5","data-sentry-element":"IconBell","data-sentry-source-file":"appointment-reminder-settings.tsx"}),"预约提醒设置"]}),(0,i.jsx)(T.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"appointment-reminder-settings.tsx",children:"配置预约提醒的时间和方式"})]}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"space-y-0.5",children:[(0,i.jsx)(iP.J,{htmlFor:"enable-reminders","data-sentry-element":"Label","data-sentry-source-file":"appointment-reminder-settings.tsx",children:"启用预约提醒"}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"自动发送预约提醒通知"})]}),(0,i.jsx)(iA,{id:"enable-reminders",checked:r.enabled,onCheckedChange:e=>p({enabled:e}),"data-sentry-element":"Switch","data-sentry-source-file":"appointment-reminder-settings.tsx"})]}),r.enabled&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)(iP.J,{children:"提醒时间"}),(0,i.jsx)("div",{className:"flex flex-wrap gap-2",children:r.reminderTimes.map(e=>(0,i.jsxs)(c.E,{variant:"secondary",className:"flex items-center gap-1",children:[b(e),(0,i.jsx)(u.$,{variant:"ghost",size:"sm",className:"h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground",onClick:()=>m(e),children:(0,i.jsx)(h.A,{className:"h-3 w-3"})})]},e))}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)(C.p,{type:"number",placeholder:"分钟",value:o,onChange:e=>l(e.target.value),className:"flex-1",min:"1",max:"1440"}),(0,i.jsx)(u.$,{variant:"outline",size:"sm",onClick:()=>{let e=parseInt(o);return isNaN(e)||e<=0?void I.toast.error("请输入有效的提醒时间（分钟）"):r.reminderTimes.includes(e)?void I.toast.error("该提醒时间已存在"):void(p({reminderTimes:[...r.reminderTimes,e].sort((e,t)=>t-e)}),l(""),I.toast.success("提醒时间添加成功"))},disabled:!o,children:(0,i.jsx)(g.A,{className:"h-4 w-4"})})]})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)(iP.J,{children:"提醒方式"}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(iI.A,{className:"h-4 w-4"}),(0,i.jsxs)("div",{children:[(0,i.jsx)(iP.J,{htmlFor:"method-notification",children:"浏览器通知"}),!d&&(0,i.jsx)("p",{className:"text-xs text-muted-foreground",children:"需要授权通知权限"})]})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[!d&&(0,i.jsx)(u.$,{variant:"outline",size:"sm",onClick:y,children:"授权"}),(0,i.jsx)(iR.S,{id:"method-notification",checked:r.methods.includes("notification"),onCheckedChange:e=>v("notification",e),disabled:!d})]})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(iL.A,{className:"h-4 w-4"}),(0,i.jsxs)("div",{children:[(0,i.jsx)(iP.J,{htmlFor:"method-email",children:"邮件提醒"}),(0,i.jsx)("p",{className:"text-xs text-muted-foreground",children:"即将推出"})]})]}),(0,i.jsx)(iR.S,{id:"method-email",checked:r.methods.includes("email"),onCheckedChange:e=>v("email",e),disabled:!0})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(iF,{className:"h-4 w-4"}),(0,i.jsxs)("div",{children:[(0,i.jsx)(iP.J,{htmlFor:"method-sms",children:"短信提醒"}),(0,i.jsx)("p",{className:"text-xs text-muted-foreground",children:"即将推出"})]})]}),(0,i.jsx)(iR.S,{id:"method-sms",checked:r.methods.includes("sms"),onCheckedChange:e=>v("sms",e),disabled:!0})]})]})]})]})]}),(0,i.jsx)(T.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"appointment-reminder-settings.tsx",children:(0,i.jsx)(u.$,{onClick:()=>n(!1),"data-sentry-element":"Button","data-sentry-source-file":"appointment-reminder-settings.tsx",children:"完成"})})]})]})}var i$=n(41634);function iW({filters:e,onFiltersChange:t}){let[n,r]=(0,s.useState)(!1),[a,o]=(0,s.useState)(!1),l=(n,r)=>{t({...e,[n]:r})},c=e.search||e.status&&"all"!==e.status||e.dateFrom||e.dateTo;return(0,i.jsxs)("div",{className:"space-y-4 p-4 border rounded-lg bg-muted/50","data-sentry-component":"AppointmentFiltersComponent","data-sentry-source-file":"appointment-filters.tsx",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("h3",{className:"text-sm font-medium",children:"筛选"}),c&&(0,i.jsx)(u.$,{variant:"ghost",size:"sm",onClick:()=>{t({search:"",status:"all",dateFrom:void 0,dateTo:void 0})},children:"清除全部"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(iP.J,{htmlFor:"search","data-sentry-element":"Label","data-sentry-source-file":"appointment-filters.tsx",children:"搜索"}),(0,i.jsx)(C.p,{id:"search",placeholder:"患者姓名、治疗项目...",value:e.search,onChange:e=>l("search",e.target.value),"data-sentry-element":"Input","data-sentry-source-file":"appointment-filters.tsx"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(iP.J,{"data-sentry-element":"Label","data-sentry-source-file":"appointment-filters.tsx",children:"状态"}),(0,i.jsxs)(A.l6,{value:e.status,onValueChange:e=>l("status",e),"data-sentry-element":"Select","data-sentry-source-file":"appointment-filters.tsx",children:[(0,i.jsx)(A.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"appointment-filters.tsx",children:(0,i.jsx)(A.yv,{placeholder:"所有状态","data-sentry-element":"SelectValue","data-sentry-source-file":"appointment-filters.tsx"})}),(0,i.jsxs)(A.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"appointment-filters.tsx",children:[(0,i.jsx)(A.eb,{value:"all","data-sentry-element":"SelectItem","data-sentry-source-file":"appointment-filters.tsx",children:"所有状态"}),(0,i.jsx)(A.eb,{value:"scheduled","data-sentry-element":"SelectItem","data-sentry-source-file":"appointment-filters.tsx",children:"已安排"}),(0,i.jsx)(A.eb,{value:"completed","data-sentry-element":"SelectItem","data-sentry-source-file":"appointment-filters.tsx",children:"已完成"}),(0,i.jsx)(A.eb,{value:"cancelled","data-sentry-element":"SelectItem","data-sentry-source-file":"appointment-filters.tsx",children:"已取消"})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(iP.J,{"data-sentry-element":"Label","data-sentry-source-file":"appointment-filters.tsx",children:"开始日期"}),(0,i.jsxs)(P.AM,{open:n,onOpenChange:r,"data-sentry-element":"Popover","data-sentry-source-file":"appointment-filters.tsx",children:[(0,i.jsx)(P.Wv,{asChild:!0,"data-sentry-element":"PopoverTrigger","data-sentry-source-file":"appointment-filters.tsx",children:(0,i.jsxs)(u.$,{variant:"outline",className:(0,_.cn)("w-full justify-start text-left font-normal",!e.dateFrom&&"text-muted-foreground"),"data-sentry-element":"Button","data-sentry-source-file":"appointment-filters.tsx",children:[(0,i.jsx)(M.A,{className:"mr-2 h-4 w-4","data-sentry-element":"CalendarIcon","data-sentry-source-file":"appointment-filters.tsx"}),e.dateFrom?(0,N.GP)(e.dateFrom,"PPP"):(0,i.jsx)("span",{children:"选择日期"})]})}),(0,i.jsx)(P.hl,{className:"w-auto p-0",align:"start","data-sentry-element":"PopoverContent","data-sentry-source-file":"appointment-filters.tsx",children:(0,i.jsx)(R.V,{mode:"single",selected:e.dateFrom,onSelect:e=>{l("dateFrom",e),r(!1)},initialFocus:!0,"data-sentry-element":"Calendar","data-sentry-source-file":"appointment-filters.tsx"})})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(iP.J,{"data-sentry-element":"Label","data-sentry-source-file":"appointment-filters.tsx",children:"结束日期"}),(0,i.jsxs)(P.AM,{open:a,onOpenChange:o,"data-sentry-element":"Popover","data-sentry-source-file":"appointment-filters.tsx",children:[(0,i.jsx)(P.Wv,{asChild:!0,"data-sentry-element":"PopoverTrigger","data-sentry-source-file":"appointment-filters.tsx",children:(0,i.jsxs)(u.$,{variant:"outline",className:(0,_.cn)("w-full justify-start text-left font-normal",!e.dateTo&&"text-muted-foreground"),"data-sentry-element":"Button","data-sentry-source-file":"appointment-filters.tsx",children:[(0,i.jsx)(M.A,{className:"mr-2 h-4 w-4","data-sentry-element":"CalendarIcon","data-sentry-source-file":"appointment-filters.tsx"}),e.dateTo?(0,N.GP)(e.dateTo,"PPP"):(0,i.jsx)("span",{children:"选择日期"})]})}),(0,i.jsx)(P.hl,{className:"w-auto p-0",align:"start","data-sentry-element":"PopoverContent","data-sentry-source-file":"appointment-filters.tsx",children:(0,i.jsx)(R.V,{mode:"single",selected:e.dateTo,onSelect:e=>{l("dateTo",e),o(!1)},disabled:t=>!!e.dateFrom&&t<e.dateFrom,initialFocus:!0,"data-sentry-element":"Calendar","data-sentry-source-file":"appointment-filters.tsx"})})]})]})]})]})}var iY=n(57416),iU=n(32300);let iV=(e,t,n)=>[{accessorKey:"patient",header:"患者",cell:({row:e})=>{let t=e.getValue("patient");return t?.fullName||"未知患者"}},{accessorKey:"treatment",header:"治疗项目",cell:({row:e})=>{let t=e.getValue("treatment");return t?.name||"未知治疗"}},{accessorKey:"appointmentDate",header:"日期时间",cell:({row:e})=>{let t=new Date(e.getValue("appointmentDate"));return(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{children:t.toLocaleDateString()}),(0,i.jsx)("div",{className:"text-sm text-muted-foreground",children:t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]})}},{accessorKey:"price",header:"价格",cell:({row:e})=>{let t=e.getValue("price");return`\xa5${t}`}},{accessorKey:"status",header:"状态",cell:({row:e})=>{let t=e.original;return(0,i.jsx)(im,{appointment:t,onStatusChange:e=>{setAppointments(t=>t.map(t=>t.id===e.id?e:t))},compact:!0})}},{id:"actions",header:"操作",cell:({row:r})=>{let a=r.original;return(0,i.jsxs)("div",{className:"flex items-center gap-2",children:["scheduled"===a.status&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(u.$,{variant:"outline",size:"sm",onClick:()=>n(a,"completed"),className:"text-green-600 hover:text-green-700",children:(0,i.jsx)(f.A,{className:"h-4 w-4"})}),(0,i.jsx)(u.$,{variant:"outline",size:"sm",onClick:()=>n(a,"cancelled"),className:"text-red-600 hover:text-red-700",children:(0,i.jsx)(p.A,{className:"h-4 w-4"})})]}),(0,i.jsxs)(io.rI,{children:[(0,i.jsx)(io.ty,{asChild:!0,children:(0,i.jsx)(u.$,{variant:"outline",size:"sm",children:(0,i.jsx)(m.A,{className:"h-4 w-4"})})}),(0,i.jsxs)(io.SQ,{children:[(0,i.jsxs)(io._2,{onClick:()=>e(a),children:[(0,i.jsx)(m.A,{className:"h-4 w-4 mr-2"}),(0,iU.t)("common.actions.edit")]}),(0,i.jsxs)(io._2,{onClick:()=>t(a),className:"text-red-600",children:[(0,i.jsx)(h.A,{className:"h-4 w-4 mr-2"}),(0,iU.t)("common.actions.delete")]})]})]})]})}}];function iB(){let{hasPermission:e,user:t}=(0,d.It)(),[n,r]=(0,s.useState)([]),[a,o]=(0,s.useState)([]),[l,c]=(0,s.useState)(!0),[f,p]=(0,s.useState)(null),[m,h]=(0,s.useState)(!1),[y,k]=(0,s.useState)(),[E,j]=(0,s.useState)(!1),[N,M]=(0,s.useState)(),[T,O]=(0,s.useState)(!1),[C,A]=(0,s.useState)({search:"",status:"all",dateFrom:void 0,dateTo:void 0}),[P,R]=(0,s.useState)("table"),_=async()=>{try{c(!0),p(null);let e=await w.RG.getAll({limit:100});r(e.docs)}catch(e){console.error("Failed to fetch appointments:",e),p("Failed to load appointments. Please try again later.")}finally{c(!1)}},L=async()=>{if(N){O(!0);try{await w.RG.delete(N.id),I.toast.success("Appointment deleted successfully"),j(!1),M(void 0),_()}catch(e){console.error("Failed to delete appointment:",e),I.toast.error("Failed to delete appointment")}finally{O(!1)}}},F=()=>{h(!1),k(void 0),_()},z=()=>{k(void 0),h(!0)},q=iV(e=>{k(e),h(!0)},e=>{M(e),j(!0)},async(e,t)=>{O(!0);try{await w.RG.update(e.id,{status:t}),I.toast.success(`Appointment ${t} successfully`),_()}catch(e){console.error("Failed to update appointment status:",e),I.toast.error("Failed to update appointment status")}finally{O(!1)}}),{table:H}=(0,S.u)({data:a,columns:q,pageCount:Math.ceil(a.length/10),shallow:!0,debounceMs:500});return l?(0,i.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,i.jsx)("p",{className:"text-muted-foreground",children:(0,iU.t)("appointments.loadingAppointments")})]})}):f?(0,i.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("p",{className:"text-red-600 mb-4",children:f}),(0,i.jsx)(u.$,{onClick:_,variant:"outline",children:"重试"})]})}):0===n.length?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)(v.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,i.jsx)("h3",{className:"text-lg font-medium mb-2",children:(0,iU.t)("appointments.noAppointments")}),(0,i.jsx)("p",{className:"text-muted-foreground mb-4",children:"开始安排您的第一个预约。"}),(0,i.jsx)(d.Bk,{permission:"canCreateAppointments",children:(0,i.jsxs)(u.$,{onClick:z,children:[(0,i.jsx)(g.A,{className:"h-4 w-4 mr-2"}),(0,iU.t)("appointments.newAppointment")]})})]})}),(0,i.jsx)(ec,{open:m,onOpenChange:h,appointment:y,onSuccess:F})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-4",children:[(0,i.jsxs)("h3",{className:"text-lg font-medium",children:[a.length," / ",n.length," ",(0,iU.t)("appointments.appointmentsCount")]}),(0,i.jsx)(iY.tU,{value:P,onValueChange:e=>R(e),"data-sentry-element":"Tabs","data-sentry-source-file":"appointments-list.tsx",children:(0,i.jsxs)(iY.j7,{className:"grid w-full grid-cols-2","data-sentry-element":"TabsList","data-sentry-source-file":"appointments-list.tsx",children:[(0,i.jsxs)(iY.Xi,{value:"table",className:"flex items-center gap-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"appointments-list.tsx",children:[(0,i.jsx)(b,{className:"h-4 w-4","data-sentry-element":"IconTable","data-sentry-source-file":"appointments-list.tsx"}),"表格视图"]}),(0,i.jsxs)(iY.Xi,{value:"calendar",className:"flex items-center gap-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"appointments-list.tsx",children:[(0,i.jsx)(v.A,{className:"h-4 w-4","data-sentry-element":"IconCalendar","data-sentry-source-file":"appointments-list.tsx"}),"日历视图"]})]})})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(iH,{"data-sentry-element":"AppointmentReminderSettings","data-sentry-source-file":"appointments-list.tsx"}),(0,i.jsx)(d.Bk,{permission:"canCreateAppointments","data-sentry-element":"PermissionGate","data-sentry-source-file":"appointments-list.tsx",children:(0,i.jsxs)(u.$,{onClick:z,"data-sentry-element":"Button","data-sentry-source-file":"appointments-list.tsx",children:[(0,i.jsx)(g.A,{className:"h-4 w-4 mr-2","data-sentry-element":"IconPlus","data-sentry-source-file":"appointments-list.tsx"}),(0,iU.t)("appointments.newAppointment")]})})]})]}),"table"===P&&(0,i.jsx)(iW,{filters:C,onFiltersChange:A}),"table"===P?(0,i.jsx)(i.Fragment,{children:(0,i.jsx)(x.b,{table:H,children:(0,i.jsx)(D.w,{table:H})})}):(0,i.jsx)(ia,{onNewAppointment:z,onEditAppointment:handleEditAppointment,onDeleteAppointment:handleDeleteAppointment}),0===a.length&&n.length>0&&(0,i.jsx)("div",{className:"text-center py-8",children:(0,i.jsx)("p",{className:"text-muted-foreground",children:"没有预约符合当前筛选条件。"})})]}),(0,i.jsx)(ec,{open:m,onOpenChange:h,appointment:y,onSuccess:F,"data-sentry-element":"AppointmentFormDialog","data-sentry-source-file":"appointments-list.tsx"}),(0,i.jsx)(i$.K,{open:E,onOpenChange:j,title:"删除预约",description:`您确定要删除 ${N?.patient.fullName} 的预约吗？此操作无法撤销。`,confirmText:"删除",variant:"destructive",onConfirm:L,loading:T,"data-sentry-element":"ConfirmationDialog","data-sentry-source-file":"appointments-list.tsx"})]})}},62889:e=>{"use strict";e.exports=function(e,t,n,r,a,o,i,s){if(!e){var l;if(void 0===t)l=Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[n,r,a,o,i,s],u=0;(l=Error(t.replace(/%s/g,function(){return c[u++]}))).name="Invariant Violation"}throw l.framesToPop=1,l}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63184:(e,t,n)=>{"use strict";n.d(t,{S:()=>j});var r=n(24443),a=n(60222),o=n(24368),i=n(4684),s=n(12772),l=n(36612),c=n(87483),u=n(42354),d=n(49258),f=n(24582),p="Checkbox",[m,h]=(0,i.A)(p),[v,g]=m(p),y=a.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:i,checked:c,defaultChecked:u,required:d,disabled:p,value:m="on",onCheckedChange:h,form:g,...y}=e,[b,w]=a.useState(null),k=(0,o.s)(t,e=>w(e)),E=a.useRef(!1),j=!b||g||!!b.closest("form"),[N=!1,M]=(0,l.i)({prop:c,defaultProp:u,onChange:h}),T=a.useRef(N);return a.useEffect(()=>{let e=b?.form;if(e){let t=()=>M(T.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[b,M]),(0,r.jsxs)(v,{scope:n,state:N,disabled:p,children:[(0,r.jsx)(f.sG.button,{type:"button",role:"checkbox","aria-checked":D(N)?"mixed":N,"aria-required":d,"data-state":S(N),"data-disabled":p?"":void 0,disabled:p,value:m,...y,ref:k,onKeyDown:(0,s.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,s.m)(e.onClick,e=>{M(e=>!!D(e)||!e),j&&(E.current=e.isPropagationStopped(),E.current||e.stopPropagation())})}),j&&(0,r.jsx)(x,{control:b,bubbles:!E.current,name:i,value:m,checked:N,required:d,disabled:p,form:g,style:{transform:"translateX(-100%)"},defaultChecked:!D(u)&&u})]})});y.displayName=p;var b="CheckboxIndicator",w=a.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:a,...o}=e,i=g(b,n);return(0,r.jsx)(d.C,{present:a||D(i.state)||!0===i.state,children:(0,r.jsx)(f.sG.span,{"data-state":S(i.state),"data-disabled":i.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});w.displayName=b;var x=e=>{let{control:t,checked:n,bubbles:o=!0,defaultChecked:i,...s}=e,l=a.useRef(null),d=(0,c.Z)(n),f=(0,u.X)(t);a.useEffect(()=>{let e=l.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(d!==n&&t){let r=new Event("click",{bubbles:o});e.indeterminate=D(n),t.call(e,!D(n)&&n),e.dispatchEvent(r)}},[d,n,o]);let p=a.useRef(!D(n)&&n);return(0,r.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:i??p.current,...s,tabIndex:-1,ref:l,style:{...e.style,...f,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function D(e){return"indeterminate"===e}function S(e){return D(e)?"indeterminate":e?"checked":"unchecked"}var k=n(84338),E=n(72595);function j({className:e,...t}){return(0,r.jsx)(y,{"data-slot":"checkbox",className:(0,E.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,"data-sentry-element":"CheckboxPrimitive.Root","data-sentry-component":"Checkbox","data-sentry-source-file":"checkbox.tsx",children:(0,r.jsx)(w,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none","data-sentry-element":"CheckboxPrimitive.Indicator","data-sentry-source-file":"checkbox.tsx",children:(0,r.jsx)(k.A,{className:"size-3.5","data-sentry-element":"CheckIcon","data-sentry-source-file":"checkbox.tsx"})})})}},63636:(e,t,n)=>{var r=n(67965),a=r?r.prototype:void 0,o=a?a.valueOf:void 0;e.exports=function(e){return o?Object(o.call(e)):{}}},65682:e=>{"use strict";e.exports=function(){}},68843:()=>{},70717:(e,t,n)=>{var r=n(17263),a=n(31415),o=n(82278);e.exports=function(e){return o(e)?r(e,!0):a(e)}},71879:(e,t,n)=>{var r=n(78171),a=n(13804);e.exports=function(e,t,n,o){var i=!n;n||(n={});for(var s=-1,l=t.length;++s<l;){var c=t[s],u=o?o(n[c],e[c],c,n,e):void 0;void 0===u&&(u=e[c]),i?a(n,c,u):r(n,c,u)}return n}},72617:(e,t,n)=>{var r=n(81191),a=n(36887),o=n(70717);e.exports=function(e){return r(e,o,a)}},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75145:(e,t,n)=>{var r=n(38868),a=n(7348),o=n(11541),i=Math.ceil,s=Math.max;e.exports=function(e,t,n){t=(n?a(e,t,n):void 0===t)?1:s(o(t),0);var l=null==e?0:e.length;if(!l||t<1)return[];for(var c=0,u=0,d=Array(i(l/t));c<l;)d[u++]=r(e,c,c+=t);return d}},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76070:e=>{var t=Object.prototype.hasOwnProperty;e.exports=function(e){var n=e.length,r=new e.constructor(n);return n&&"string"==typeof e[0]&&t.call(e,"index")&&(r.index=e.index,r.input=e.input),r}},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78171:(e,t,n)=>{var r=n(13804),a=n(13228),o=Object.prototype.hasOwnProperty;e.exports=function(e,t,n){var i=e[t];o.call(e,t)&&a(i,n)&&(void 0!==n||t in e)||r(e,t,n)}},78588:(e,t,n)=>{var r=n(52326),a=n(14001),o=n(52645),i=o&&o.isSet;e.exports=i?a(i):r},79243:(e,t,n)=>{var r=n(65936),a=n(32247),o=n(89707),i=n(87197),s=n(71879),l=n(6138),c=n(46932),u=n(72617);e.exports=c(function(e,t){var n={};if(null==e)return n;var c=!1;t=r(t,function(t){return t=i(t,e),c||(c=t.length>1),t}),s(e,u(e),n),c&&(n=a(n,7,l));for(var d=t.length;d--;)o(n,t[d]);return n})},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},82772:(e,t,n)=>{var r=n(57413),a=n(8140),o=n(8709),i=n(97657),s=n(47559),l=n(5517),c=n(58148),u=n(2802),d=n(56097),f=n(66991);e.exports=function(e,t,n){var p=l(e),m=p||c(e)||f(e);if(t=i(t,4),null==n){var h=e&&e.constructor;n=m?p?new h:[]:d(e)&&u(h)?a(s(e)):{}}return(m?r:o)(e,function(e,r,a){return t(n,e,r,a)}),n}},82966:(e,t,n)=>{Promise.resolve().then(n.bind(n,17403)),Promise.resolve().then(n.bind(n,89371))},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},85586:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>o.default,__next_app__:()=>u,pages:()=>c,routeModule:()=>d,tree:()=>l});var r=n(29703),a=n(85544),o=n(62458),i=n(77821),s={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>i[e]);n.d(t,s);let l={children:["",{children:["dashboard",{children:["appointments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,27279)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\appointments\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,56164)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,3259))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,69549)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(n.bind(n,62458)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(n.bind(n,8036)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,11103,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,13780,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,3259))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\appointments\\page.tsx"],u={require:n,loadChunk:()=>Promise.resolve()},d=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/appointments/page",pathname:"/dashboard/appointments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},85917:function(e){e.exports=function(e,t){t.prototype.isSameOrAfter=function(e,t){return this.isSame(e,t)||this.isAfter(e,t)}}},86592:e=>{"use strict";e.exports=require("node:inspector")},89707:(e,t,n)=>{var r=n(87197),a=n(84946),o=n(93125),i=n(20513);e.exports=function(e,t){return t=r(t,e),null==(e=o(e,t))||delete e[i(a(t))]}},89733:e=>{var t=/\w*$/;e.exports=function(e){var n=new e.constructor(e.source,t.exec(e));return n.lastIndex=e.lastIndex,n}},93125:(e,t,n)=>{var r=n(43046),a=n(38868);e.exports=function(e,t){return t.length<2?e:r(e,a(t,0,-1))}},94735:e=>{"use strict";e.exports=require("events")},96854:function(e){e.exports=function(e,t,n){var r=function(e,t){if(!t||!t.length||1===t.length&&!t[0]||1===t.length&&Array.isArray(t[0])&&!t[0].length)return null;1===t.length&&t[0].length>0&&(t=t[0]),n=(t=t.filter(function(e){return e}))[0];for(var n,r=1;r<t.length;r+=1)t[r].isValid()&&!t[r][e](n)||(n=t[r]);return n};n.max=function(){var e=[].slice.call(arguments,0);return r("isAfter",e)},n.min=function(){var e=[].slice.call(arguments,0);return r("isBefore",e)}}},99124:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[55,3738,1950,5886,9615,7927,6451,5618,2584,9616,4144,4889,3875,395,5215,9255,5903,4298,8774,7494,6273,490,5392,7331],()=>n(85586));module.exports=r})();
//# sourceMappingURL=page.js.map