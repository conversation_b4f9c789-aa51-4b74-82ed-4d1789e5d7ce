try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="11928984-b329-4f11-90cb-2c196fe3f913",e._sentryDebugIdIdentifier="sentry-dbid-11928984-b329-4f11-90cb-2c196fe3f913")}catch(e){}(()=>{var e={};e.id=3613,e.ids=[3613],e.modules={822:(e,t,s)=>{"use strict";let a;s.r(t),s.d(t,{default:()=>g,generateImageMetadata:()=>f,generateMetadata:()=>h,generateViewport:()=>y});var r=s(63033),n=s(78869),l=s(79615),i=s(44508),o=s(83829),d=s(38461),c=(0,s(19e3).A)("outline","receipt","IconReceipt",[["path",{d:"M5 21v-16a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v16l-3 -2l-2 2l-2 -2l-2 2l-2 -2l-3 2m4 -14h6m-6 4h6m-2 4h2",key:"svg-0"}]]),m=s(19761);async function u(){let{userId:e}=await (0,l.j)();return e?(0,n.jsx)(o.A,{"data-sentry-element":"PageContainer","data-sentry-component":"BillingPage","data-sentry-source-file":"page.tsx",children:(0,n.jsxs)("div",{className:"flex flex-1 flex-col space-y-4",children:[(0,n.jsx)("div",{className:"flex items-center justify-between",children:(0,n.jsxs)("div",{children:[(0,n.jsxs)("h2",{className:"text-2xl font-bold tracking-tight flex items-center gap-2",children:[(0,n.jsx)(c,{className:"size-6","data-sentry-element":"IconReceipt","data-sentry-source-file":"page.tsx"}),"财务管理"]}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"管理账单、支付记录、收据和财务报表"})]})}),(0,n.jsx)(d.BillingTabs,{"data-sentry-element":"BillingTabs","data-sentry-source-file":"page.tsx"})]})}):(0,i.redirect)("/auth/sign-in")}let x={...r},p="workUnitAsyncStorage"in x?x.workUnitAsyncStorage:"requestAsyncStorage"in x?x.requestAsyncStorage:void 0;a=new Proxy(u,{apply:(e,t,s)=>{let a,r,n;try{let e=p?.getStore();a=e?.headers.get("sentry-trace")??void 0,r=e?.headers.get("baggage")??void 0,n=e?.headers}catch(e){}return m.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/billing",componentType:"Page",sentryTraceHeader:a,baggageHeader:r,headers:n}).apply(t,s)}});let h=void 0,f=void 0,y=void 0,g=a},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19422:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>m,tree:()=>o});var a=s(29703),r=s(85544),n=s(62458),l=s(77821),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);s.d(t,i);let o={children:["",{children:["dashboard",{children:["billing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,822)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\billing\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,56164)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,3259))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,69549)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,62458)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,8036)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,11103,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,13780,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,3259))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\billing\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/billing/page",pathname:"/dashboard/billing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},26403:(e,t,s)=>{Promise.resolve().then(s.bind(s,38461)),Promise.resolve().then(s.bind(s,89371))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38461:(e,t,s)=>{"use strict";s.d(t,{BillingTabs:()=>a});let a=(0,s(91611).registerClientReference)(function(){throw Error("Attempted to call BillingTabs() from the server but BillingTabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\billing-tabs.tsx","BillingTabs")},38522:e=>{"use strict";e.exports=require("node:zlib")},39979:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var a=(0,s(46244).A)("outline","refresh","IconRefresh",[["path",{d:"M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4",key:"svg-0"}],["path",{d:"M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4",key:"svg-1"}]])},40475:(e,t,s)=>{Promise.resolve().then(s.bind(s,74619)),Promise.resolve().then(s.bind(s,67529))},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},51092:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var a=(0,s(46244).A)("outline","alert-circle","IconAlertCircle",[["path",{d:"M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0",key:"svg-0"}],["path",{d:"M12 8v4",key:"svg-1"}],["path",{d:"M12 16h.01",key:"svg-2"}]])},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},61780:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>m,Es:()=>x,L3:()=>p,c7:()=>u,lG:()=>i,rr:()=>h,zM:()=>o});var a=s(24443);s(60222);var r=s(99873),n=s(20422),l=s(72595);function i({...e}){return(0,a.jsx)(r.bL,{"data-slot":"dialog",...e,"data-sentry-element":"DialogPrimitive.Root","data-sentry-component":"Dialog","data-sentry-source-file":"dialog.tsx"})}function o({...e}){return(0,a.jsx)(r.l9,{"data-slot":"dialog-trigger",...e,"data-sentry-element":"DialogPrimitive.Trigger","data-sentry-component":"DialogTrigger","data-sentry-source-file":"dialog.tsx"})}function d({...e}){return(0,a.jsx)(r.ZL,{"data-slot":"dialog-portal",...e,"data-sentry-element":"DialogPrimitive.Portal","data-sentry-component":"DialogPortal","data-sentry-source-file":"dialog.tsx"})}function c({className:e,...t}){return(0,a.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t,"data-sentry-element":"DialogPrimitive.Overlay","data-sentry-component":"DialogOverlay","data-sentry-source-file":"dialog.tsx"})}function m({className:e,children:t,...s}){return(0,a.jsxs)(d,{"data-slot":"dialog-portal","data-sentry-element":"DialogPortal","data-sentry-component":"DialogContent","data-sentry-source-file":"dialog.tsx",children:[(0,a.jsx)(c,{"data-sentry-element":"DialogOverlay","data-sentry-source-file":"dialog.tsx"}),(0,a.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...s,"data-sentry-element":"DialogPrimitive.Content","data-sentry-source-file":"dialog.tsx",children:[t,(0,a.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4","data-sentry-element":"DialogPrimitive.Close","data-sentry-source-file":"dialog.tsx",children:[(0,a.jsx)(n.A,{"data-sentry-element":"XIcon","data-sentry-source-file":"dialog.tsx"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t,"data-sentry-component":"DialogHeader","data-sentry-source-file":"dialog.tsx"})}function x({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t,"data-sentry-component":"DialogFooter","data-sentry-source-file":"dialog.tsx"})}function p({className:e,...t}){return(0,a.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",e),...t,"data-sentry-element":"DialogPrimitive.Title","data-sentry-component":"DialogTitle","data-sentry-source-file":"dialog.tsx"})}function h({className:e,...t}){return(0,a.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-element":"DialogPrimitive.Description","data-sentry-component":"DialogDescription","data-sentry-source-file":"dialog.tsx"})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67529:(e,t,s)=>{"use strict";s.d(t,{$:()=>i,ScrollArea:()=>l});var a=s(24443);s(60222);var r=s(54889),n=s(72595);function l({className:e,children:t,...s}){return(0,a.jsxs)(r.bL,{"data-slot":"scroll-area",className:(0,n.cn)("relative",e),...s,"data-sentry-element":"ScrollAreaPrimitive.Root","data-sentry-component":"ScrollArea","data-sentry-source-file":"scroll-area.tsx",children:[(0,a.jsx)(r.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1","data-sentry-element":"ScrollAreaPrimitive.Viewport","data-sentry-source-file":"scroll-area.tsx",children:t}),(0,a.jsx)(i,{"data-sentry-element":"ScrollBar","data-sentry-source-file":"scroll-area.tsx"}),(0,a.jsx)(r.OK,{"data-sentry-element":"ScrollAreaPrimitive.Corner","data-sentry-source-file":"scroll-area.tsx"})]})}function i({className:e,orientation:t="vertical",...s}){return(0,a.jsx)(r.VM,{"data-slot":"scroll-area-scrollbar",orientation:t,className:(0,n.cn)("flex touch-none p-px transition-colors select-none","vertical"===t&&"h-full w-2.5 border-l border-l-transparent","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent",e),...s,"data-sentry-element":"ScrollAreaPrimitive.ScrollAreaScrollbar","data-sentry-component":"ScrollBar","data-sentry-source-file":"scroll-area.tsx",children:(0,a.jsx)(r.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full","data-sentry-element":"ScrollAreaPrimitive.ScrollAreaThumb","data-sentry-source-file":"scroll-area.tsx"})})}},68829:(e,t,s)=>{"use strict";s.r(t),s.d(t,{"7f22efd92a3b59d43d3d12fe480e87910640e1db9e":()=>r.y,"7f7b45347fd50452ee6e2850ded1018991a7b086f0":()=>a.at,"7f909588461cb83e855875f4939d6f26e4ae81b49e":()=>a.ot,"7fe4197e906c8b4cd0ec15b4e0f6272dfe50de9261":()=>a.ai});var a=s(27235),r=s(41372)},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74619:(e,t,s)=>{"use strict";s.d(t,{BillingTabs:()=>eV});var a=s(24443),r=s(60222),n=s(57416),l=s(10531),i=s(33284),o=s(74482),d=s(51092),c=s(39979),m=s(46244),u=(0,m.A)("outline","receipt","IconReceipt",[["path",{d:"M5 21v-16a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v16l-3 -2l-2 2l-2 -2l-2 2l-2 -2l-3 2m4 -14h6m-6 4h6m-2 4h2",key:"svg-0"}]]),x=s(20866),p=s(47145),h=s(49958),f=s(53503),y=s(85001);let g={AUTH_REQUIRED:{message:"请先登录以继续操作",severity:"error"},AUTH_SERVICE_ERROR:{message:"认证服务暂时不可用，请稍后重试",severity:"error"},CLERK_USER_FETCH_ERROR:{message:"获取用户信息失败，请重新登录",severity:"error"},USER_EMAIL_MISSING:{message:"用户邮箱信息缺失，请联系管理员",severity:"error"},VALIDATION_ERROR:{message:"输入数据验证失败，请检查表单内容",severity:"error"},INVALID_JSON:{message:"数据格式错误，请刷新页面重试",severity:"error"},INVALID_LIMIT:{message:"分页参数错误",severity:"warning"},INVALID_PAGE:{message:"页码参数错误",severity:"warning"},SUBTOTAL_MISMATCH:{message:"账单金额计算错误，请重新检查",severity:"error"},INVALID_PAYMENT_AMOUNT:{message:"支付金额无效",severity:"error"},BILL_NOT_FOUND:{message:"账单不存在或已被删除",severity:"error"},PAYMENT_METHOD_ERROR:{message:"支付方式验证失败",severity:"error"},INSUFFICIENT_PERMISSIONS:{message:"权限不足，无法执行此操作",severity:"error"},BACKEND_ERROR:{message:"后端服务错误",severity:"error"},BACKEND_SERVICE_ERROR:{message:"后端服务暂时不可用，请稍后重试",severity:"error"},DATABASE_ERROR:{message:"数据库操作失败，请稍后重试",severity:"error"},NETWORK_ERROR:{message:"网络连接错误，请检查网络连接",severity:"error"},RATE_LIMIT_EXCEEDED:{message:"操作过于频繁，请稍后再试",severity:"error"},SUSPICIOUS_ACTIVITY:{message:"检测到可疑活动，操作已被阻止",severity:"error"},SESSION_EXPIRED:{message:"会话已过期，请重新登录",severity:"error"},INVALID_AUTHENTICATION:{message:"身份验证失败",severity:"error"},UNAUTHORIZED_ACCESS:{message:"未授权访问，请先登录",severity:"error"},PAYMENT_GATEWAY_ERROR:{message:"支付网关错误，请稍后重试",severity:"error"},CARD_DECLINED:{message:"银行卡被拒绝，请检查卡片状态",severity:"error"},CARD_EXPIRED:{message:"银行卡已过期，请使用其他卡片",severity:"error"},INVALID_CARD_NUMBER:{message:"银行卡号无效",severity:"error"},TRANSACTION_TIMEOUT:{message:"交易超时，请重新尝试",severity:"error"},DUPLICATE_TRANSACTION:{message:"重复交易，请勿重复提交",severity:"error"},INSUFFICIENT_FUNDS:{message:"余额不足，无法完成支付",severity:"error"},PAYMENT_ALREADY_PROCESSED:{message:"支付已处理，请勿重复操作",severity:"error"},DEPOSIT_EXPIRED:{message:"押金已过期",severity:"error"},DEPOSIT_INSUFFICIENT_BALANCE:{message:"押金余额不足",severity:"error"},REFUND_AMOUNT_EXCEEDS_PAYMENT:{message:"退款金额超过支付金额",severity:"error"},DEPOSIT_NOT_FOUND:{message:"未找到指定押金记录",severity:"error"},CONFIGURATION_ERROR:{message:"系统配置错误，请联系管理员",severity:"error"},EXTERNAL_SERVICE_ERROR:{message:"外部服务错误，请稍后重试",severity:"error"},INTERNAL_ERROR:{message:"系统内部错误，请稍后重试",severity:"error"},UNKNOWN_ERROR:{message:"发生未知错误，请联系技术支持",severity:"error"}};class j{constructor(){this.errorLog=[]}static getInstance(){return j.instance||(j.instance=new j),j.instance}handleAPIError(e,t={}){let s,{showToast:a=!0,logError:r=!0,context:n="API",fallbackMessage:l="操作失败，请稍后重试"}=t;if(e?.code&&g[e.code]){let t=g[e.code];s={code:e.code,message:e.message||t.message,userMessage:e.message||t.message,severity:t.severity,details:e.details,timestamp:new Date,context:n}}else s={code:"UNKNOWN_ERROR",message:e?.message||"Unknown error occurred",userMessage:l,severity:"error",details:e,timestamp:new Date,context:n};return r&&this.logError(s),a&&this.showErrorToast(s),s}handleNetworkError(e,t={}){let s={code:"NETWORK_ERROR",message:e?.message||"Network request failed",userMessage:"网络连接错误，请检查网络连接后重试",severity:"error",details:e,timestamp:new Date,context:t.context||"Network"};return!1!==t.logError&&this.logError(s),!1!==t.showToast&&this.showErrorToast(s),s}handleValidationError(e,t={}){let s=e[0],a={code:"VALIDATION_ERROR",message:"Validation failed",userMessage:s?.message||"表单验证失败，请检查输入内容",severity:"error",details:e,timestamp:new Date,context:t.context||"Validation"};return!1!==t.logError&&this.logError(a),!1!==t.showToast&&this.showErrorToast(a),a}showSuccess(e,t){y.toast.success(e,{description:t,duration:3e3})}showWarning(e,t){y.toast.warning(e,{description:t,duration:4e3})}showInfo(e,t){y.toast.info(e,{description:t,duration:3e3})}logError(e){console.error(`[${e.context}] ${e.code}: ${e.message}`,{userMessage:e.userMessage,details:e.details,timestamp:e.timestamp}),this.errorLog.push(e),this.errorLog.length>100&&this.errorLog.shift()}showErrorToast(e){let t={duration:"error"===e.severity?5e3:4e3};switch(e.severity){case"error":y.toast.error(e.userMessage,t);break;case"warning":y.toast.warning(e.userMessage,t);break;case"info":y.toast.info(e.userMessage,t)}}getErrorLog(){return[...this.errorLog]}clearErrorLog(){this.errorLog=[]}}let b=j.getInstance(),v=(e,t)=>b.handleAPIError(e,t),N=(e,t)=>b.handleNetworkError(e,t),w=async(e,t=3,s=1e3,a)=>{let r;for(let n=1;n<=t;n++)try{return await e()}catch(l){if(r=l,n===t)throw v(l,{context:a||"Retry",fallbackMessage:`操作失败，已重试${t}次`}),l;let e=s*Math.pow(2,n-1);await new Promise(t=>setTimeout(t,e))}throw r};class C extends Error{constructor(e,t,s,a){super(e),this.status=t,this.code=s,this.details=a,this.name="BillingAPIError"}}async function A(e,t={},s){let a=`/api${e}`,r={...t,headers:{"Content-Type":"application/json",...t.headers}},n=async()=>{try{let e=await fetch(a,r);if(!e.ok){let t;try{t=await e.json()}catch{t={error:`HTTP ${e.status}: ${e.statusText}`,code:`HTTP_${e.status}`,message:e.statusText}}let a=new C(t.error||t.message||`HTTP ${e.status}: ${e.statusText}`,e.status,t.code||`HTTP_${e.status}`,t.details);throw v(t,{context:s?.context||"API Request",showToast:!1}),a}return await e.json()}catch(t){if(t instanceof C)throw t;let e=new C(t instanceof Error?t.message:"Network error occurred",0,"NETWORK_ERROR",t);throw N(t,{context:s?.context||"API Request",showToast:!1}),e}};return(!t.method||["GET","HEAD","OPTIONS"].includes(t.method.toUpperCase()))&&s?.maxRetries?w(n,s.maxRetries,1e3,s.context):n()}let I={async fetchBills(e){let t=new URLSearchParams;e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.search&&t.append("search",e.search),e?.status&&t.append("status",e.status),e?.patientId&&t.append("patient",e.patientId),e?.dateFrom&&t.append("dateFrom",e.dateFrom),e?.dateTo&&t.append("dateTo",e.dateTo);let s=t.toString();return A(`/bills${s?`?${s}`:""}`,{},{maxRetries:3,context:"Fetch Bills"})},fetchBill:async e=>A(`/bills/${e}`),createBill:async e=>A("/bills",{method:"POST",body:JSON.stringify(e)}),updateBill:async(e,t)=>A(`/bills/${e}`,{method:"PATCH",body:JSON.stringify(t)}),deleteBill:async e=>A(`/bills/${e}`,{method:"DELETE"}),generateFromAppointment:async(e,t="treatment")=>A("/bills/generate-from-appointment",{method:"POST",body:JSON.stringify({appointmentId:e,billType:t})}),async checkAppointmentBill(e){try{let t=(await I.fetchBills({limit:1})).docs.find(t=>"object"==typeof t.appointment&&t.appointment?.id===e);return{hasBill:!!t,bill:t||void 0}}catch(e){return console.error("Failed to check appointment bill:",e),{hasBill:!1}}}},S={async fetchPayments(e){let t=new URLSearchParams;e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.billId&&t.append("bill",e.billId),e?.patientId&&t.append("patient",e.patientId),e?.paymentMethod&&t.append("paymentMethod",e.paymentMethod),e?.status&&t.append("paymentStatus",e.status),e?.dateFrom&&t.append("dateFrom",e.dateFrom),e?.dateTo&&t.append("dateTo",e.dateTo);let s=t.toString();return A(`/payments${s?`?${s}`:""}`)},fetchPayment:async e=>A(`/payments/${e}`),processPayment:async e=>A("/payments",{method:"POST",body:JSON.stringify(e)}),updatePayment:async(e,t)=>A(`/payments/${e}`,{method:"PATCH",body:JSON.stringify(t)}),processRefund:async(e,t)=>A(`/payments/${e}/refund`,{method:"POST",body:JSON.stringify(t)})},R={getDailyRevenue:async e=>A(`/reports/daily-revenue?date=${e}`),getMonthlyRevenue:async(e,t)=>A(`/reports/monthly-revenue?year=${e}&month=${t}`),getOutstandingBalances:async()=>A("/reports/outstanding-balances"),async getFinancialReport(e){let t=new URLSearchParams;return e?.startDate&&t.set("startDate",e.startDate),e?.endDate&&t.set("endDate",e.endDate),e?.type&&t.set("type",e.type),A(`/reports/financial?${t.toString()}`)}},T={formatCurrency:e=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:"USD"}).format(e),getPaymentMethodName:e=>({cash:"现金",card:"银行卡",wechat:"微信支付",alipay:"支付宝",transfer:"银行转账",deposit:"押金抵扣",installment:"分期付款"})[e]||e,getBillStatusName:e=>({draft:"草稿",sent:"已发送",confirmed:"已确认",paid:"已支付",cancelled:"已取消"})[e]||e,getPaymentStatusName:e=>({pending:"待处理",completed:"已完成",failed:"失败",refunded:"已退款"})[e]||e};var D=s(61780),k=s(95550),E=s(80395),P=s(13875);P.ai().min(0,"金额不能为负数");let M=P.Yj().min(1,"此字段为必填项"),B=P.Yj(),F=e=>!(e<0)&&(e.toString().split(".")[1]||"").length<=2,$=P.Ik({itemType:P.k5(["treatment","consultation","material","service"],{required_error:"请选择项目类型",invalid_type_error:"无效的项目类型"}),itemName:M.max(100,"项目名称不能超过100个字符"),description:B.max(500,"描述不能超过500个字符").optional(),quantity:P.ai().min(.01,"数量必须大于0").max(9999,"数量不能超过9999").refine(e=>(e.toString().split(".")[1]||"").length<=3,"数量最多支持3位小数"),unitPrice:P.ai().min(0,"单价不能为负数").max(999999.99,"单价不能超过999,999.99").refine(F,"单价格式无效，最多支持2位小数"),discountRate:P.ai().min(0,"折扣率不能为负数").max(100,"折扣率不能超过100%").optional()}).refine(e=>!e.discountRate||!(e.discountRate>0)||0!==e.unitPrice,{message:"单价为0时不能设置折扣",path:["discountRate"]}),_=P.Ik({patient:M.uuid("请选择有效的患者"),appointment:B.uuid("请选择有效的预约").optional().or(P.eu("")),treatment:B.uuid("请选择有效的治疗项目").optional().or(P.eu("")),billType:P.k5(["treatment","consultation","deposit","additional"],{required_error:"请选择账单类型",invalid_type_error:"无效的账单类型"}),description:M.min(2,"账单描述至少需要2个字符").max(200,"账单描述不能超过200个字符"),notes:B.max(1e3,"备注不能超过1000个字符").optional(),dueDate:P.Yj().min(1,"请选择到期日期").refine(e=>{try{return!0}catch{return!1}},"请输入有效的日期").refine(e=>{let t=new Date(e),s=new Date;return s.setHours(0,0,0,0),t>=s},"到期日期不能是过去的日期").refine(e=>{let t=new Date(e),s=new Date;return s.setFullYear(s.getFullYear()+2),t<=s},"到期日期不能超过2年"),discountAmount:P.ai().min(0,"折扣金额不能为负数").max(999999.99,"折扣金额不能超过999,999.99").refine(F,"折扣金额格式无效").optional(),taxAmount:P.ai().min(0,"税费金额不能为负数").max(999999.99,"税费金额不能超过999,999.99").refine(F,"税费金额格式无效").optional(),items:P.YO($).min(1,"至少需要一个账单项目").max(50,"账单项目不能超过50个")}).refine(e=>{let t=e.items.reduce((e,t)=>{let s=t.quantity*t.unitPrice,a=s*((t.discountRate||0)/100);return e+(s-a)},0),s=e.discountAmount||0;return t+(e.taxAmount||0)-s>=0},{message:"账单总金额不能为负数",path:["discountAmount"]}).refine(e=>{let t=e.items.reduce((e,t)=>{let s=t.quantity*t.unitPrice,a=s*((t.discountRate||0)/100);return e+(s-a)},0);return(e.discountAmount||0)<=t},{message:"折扣金额不能超过项目小计",path:["discountAmount"]}),O=P.Ik({amount:P.ai().min(.01,"支付金额必须大于0").max(999999.99,"支付金额不能超过999,999.99").refine(F,"支付金额格式无效，最多支持2位小数"),paymentMethod:P.k5(["cash","card","wechat","alipay","transfer","installment"],{required_error:"请选择支付方式",invalid_type_error:"无效的支付方式"}),transactionId:P.Yj().max(100,"交易ID不能超过100个字符").optional().or(P.eu("")),notes:B.max(500,"备注不能超过500个字符").optional()}).refine(e=>!["card","wechat","alipay","transfer"].includes(e.paymentMethod)||e.transactionId&&e.transactionId.trim().length>0,{message:"此支付方式需要提供交易ID",path:["transactionId"]});P.Ik({fullName:M.min(2,"姓名至少需要2个字符").max(50,"姓名不能超过50个字符").regex(/^[\u4e00-\u9fa5a-zA-Z\s]+$/,"姓名只能包含中文、英文和空格"),phone:M.regex(/^1[3-9]\d{9}$/,"请输入有效的手机号码"),email:P.Yj().email("请输入有效的邮箱地址").max(100,"邮箱地址不能超过100个字符").optional().or(P.eu("")),medicalNotes:B.max(2e3,"医疗备注不能超过2000个字符").optional()}),P.Ik({status:P.k5(["draft","sent","confirmed","paid","cancelled"],{required_error:"请选择账单状态",invalid_type_error:"无效的账单状态"}),notes:B.max(500,"状态更新备注不能超过500个字符").optional()}),P.Ik({search:B.max(100,"搜索关键词不能超过100个字符").optional(),status:P.k5(["draft","sent","confirmed","paid","cancelled"]).optional(),billType:P.k5(["treatment","consultation","deposit","additional"]).optional(),patientId:B.uuid("请选择有效的患者").optional().or(P.eu("")),dateFrom:P.Yj().optional().refine(e=>{if(!e)return!0;try{return!0}catch{return!1}},"请输入有效的开始日期"),dateTo:P.Yj().optional().refine(e=>{if(!e)return!0;try{return!0}catch{return!1}},"请输入有效的结束日期"),amountMin:P.ai().min(0,"最小金额不能为负数").max(999999.99,"最小金额不能超过999,999.99").optional(),amountMax:P.ai().min(0,"最大金额不能为负数").max(999999.99,"最大金额不能超过999,999.99").optional()}).refine(e=>!e.dateFrom||!e.dateTo||new Date(e.dateFrom)<=new Date(e.dateTo),{message:"开始日期不能晚于结束日期",path:["dateTo"]}).refine(e=>void 0===e.amountMin||void 0===e.amountMax||e.amountMin<=e.amountMax,{message:"最小金额不能大于最大金额",path:["amountMax"]});let z={bill:{created:e=>{y.toast.success(`账单创建成功！`,{description:`账单编号: ${e.billNumber}`,duration:4e3})},updated:e=>{y.toast.success(`账单更新成功！`,{description:`账单编号: ${e.billNumber}`,duration:4e3})},deleted:e=>{y.toast.success(`账单删除成功！`,{description:`账单编号: ${e}`,duration:4e3})},statusUpdated:(e,t,s)=>{let a={draft:"草稿",sent:"已发送",confirmed:"已确认",paid:"已支付",cancelled:"已取消"};y.toast.success(`账单状态已更新！`,{description:`${e.billNumber}: ${a[t]} → ${a[s]}`,duration:5e3})},generateFromAppointment:(e,t)=>{y.toast.success(`从预约生成账单成功！`,{description:`预约日期: ${t}，账单编号: ${e.billNumber}`,duration:4e3})},loadError:e=>{y.toast.error(`加载账单失败`,{description:e||"请检查网络连接后重试",duration:5e3})},createError:e=>{y.toast.error(`创建账单失败`,{description:e||"请检查输入信息后重试",duration:5e3})},updateError:e=>{y.toast.error(`更新账单失败`,{description:e||"请稍后重试",duration:5e3})},deleteError:e=>{y.toast.error(`删除账单失败`,{description:e||"请稍后重试",duration:5e3})},validationError:e=>{y.toast.error(`账单验证失败`,{description:e,duration:5e3})}},payment:{processed:e=>{y.toast.success(`支付处理成功！`,{description:`支付金额: ${T.formatCurrency(e.amount)}，支付编号: ${e.paymentNumber}`,duration:5e3})},receiptGenerated:e=>{y.toast.success(`收据生成成功！`,{description:`收据编号: ${e.receiptNumber||"待生成"}`,duration:4e3})},refunded:(e,t)=>{y.toast.success(`退款处理成功！`,{description:`退款金额: ${T.formatCurrency(t)}，支付编号: ${e.paymentNumber}`,duration:5e3})},statusUpdated:(e,t,s)=>{let a={pending:"待处理",completed:"已完成",failed:"失败",refunded:"已退款"};y.toast.success(`支付状态已更新！`,{description:`${e.paymentNumber}: ${a[t]} → ${a[s]}`,duration:4e3})},processError:e=>{y.toast.error(`支付处理失败`,{description:e||"请检查支付信息后重试",duration:5e3})},refundError:e=>{y.toast.error(`退款处理失败`,{description:e||"请联系管理员处理",duration:5e3})},validationError:e=>{y.toast.error(`支付验证失败`,{description:e,duration:5e3})},amountExceeded:e=>{y.toast.error(`支付金额超限`,{description:`最大支付金额: ${T.formatCurrency(e)}`,duration:5e3})}},receipt:{printed:e=>{y.toast.success(`收据打印成功！`,{description:`收据编号: ${e}`,duration:3e3})},downloaded:e=>{y.toast.success(`收据下载成功！`,{description:`收据编号: ${e}`,duration:3e3})},printError:()=>{y.toast.error(`收据打印失败`,{description:"请检查打印机设置",duration:4e3})},downloadError:()=>{y.toast.error(`收据下载失败`,{description:"请稍后重试",duration:4e3})},notFound:e=>{y.toast.error(`收据未找到`,{description:`收据编号: ${e}`,duration:4e3})}},system:{loading:e=>{y.toast.loading(`${e}中...`,{duration:1/0})},networkError:()=>{y.toast.error(`网络连接失败`,{description:"请检查网络连接后重试",duration:5e3})},permissionDenied:e=>{y.toast.error(`权限不足`,{description:`您没有权限执行: ${e}`,duration:5e3})},dataRefreshed:()=>{y.toast.success(`数据刷新成功`,{duration:2e3})},dataRefreshError:()=>{y.toast.error(`数据刷新失败`,{description:"请稍后重试",duration:4e3})},operationCancelled:e=>{y.toast.info(`${e}已取消`,{duration:2e3})},featureNotImplemented:e=>{y.toast.info(`${e}功能开发中...`,{description:"敬请期待",duration:3e3})}},validation:{requiredField:e=>{y.toast.error(`字段验证失败`,{description:`${e}为必填项`,duration:4e3})},invalidFormat:(e,t)=>{y.toast.error(`格式验证失败`,{description:`${e}格式应为: ${t}`,duration:4e3})},duplicateEntry:(e,t)=>{y.toast.error(`重复条目`,{description:`${e} "${t}" 已存在`,duration:4e3})},unsavedChanges:()=>{y.toast.warning(`有未保存的更改`,{description:"请保存后再继续",duration:4e3})},confirmAction:e=>{y.toast.warning(`请确认操作`,{description:`即将执行: ${e}`,duration:5e3})}}};class q{constructor(e=500){this.timeouts=new Map,this.delay=e}debounce(e,t,...s){let a=this.timeouts.get(e);a&&clearTimeout(a);let r=setTimeout(()=>{t(...s),this.timeouts.delete(e)},this.delay);this.timeouts.set(e,r)}clear(e){if(e){let t=this.timeouts.get(e);t&&(clearTimeout(t),this.timeouts.delete(e))}else this.timeouts.forEach(e=>clearTimeout(e)),this.timeouts.clear()}}class L{constructor(e,t=300){this.schema=e,this.debouncer=new q(t)}validateField(e,t,s,a){this.debouncer.debounce(e,this.performFieldValidation.bind(this),e,t,s,a)}performFieldValidation(e,t,s,a){try{let r=this.setNestedValue({},e,t),n={...s,...r},l=this.schema.safeParse(n),i=l.success?[]:l.error.errors.filter(t=>t.path.join(".")===e).map(t=>({field:e,message:t.message,code:t.code,severity:"error"})),o=this.generateWarnings(e,t,s),d={isValid:0===i.length,errors:i,warnings:o};a&&a(d)}catch(t){console.error("Field validation error:",t),a&&a({isValid:!1,errors:[{field:e,message:"验证过程中发生错误",code:"VALIDATION_ERROR",severity:"error"}],warnings:[]})}}setNestedValue(e,t,s){let a=t.split("."),r=e;for(let e=0;e<a.length-1;e++){let t=a[e];t in r||(r[t]={}),r=r[t]}return r[a[a.length-1]]=s,e}generateWarnings(e,t,s){let a=[];switch(e){case"amount":"number"==typeof t&&t>1e4&&a.push({field:e,message:"金额较大，请确认是否正确",suggestion:"检查金额是否输入正确"});break;case"dueDate":if(t){let s=new Date(t),r=new Date,n=Math.ceil((s.getTime()-r.getTime())/864e5);n>365?a.push({field:e,message:"到期日期距离现在超过一年",suggestion:"考虑设置更近的到期日期"}):n<7&&a.push({field:e,message:"到期日期较近",suggestion:"确保有足够时间处理账单"})}break;case"discountAmount":"number"==typeof t&&t>0&&s.items&&t>.5*s.items.reduce((e,t)=>e+(t.quantity||0)*(t.unitPrice||0),0)&&a.push({field:e,message:"折扣金额超过小计的50%",suggestion:"确认折扣金额是否正确"});break;case"unitPrice":"number"==typeof t&&0===t&&a.push({field:e,message:"单价为0，确认是否为免费项目",suggestion:"如果不是免费项目，请输入正确单价"})}return a}cleanup(){this.debouncer.clear()}}class V{constructor(e){this.fieldValidators=new Map,this.schema=e}validateForm(e){try{let t=this.schema.safeParse(e);if(t.success)return{isValid:!0,errors:[],warnings:this.generateFormWarnings(e)};let s=t.error.errors.map(e=>({field:e.path.join("."),message:e.message,code:e.code,severity:"error"}));return{isValid:!1,errors:s,warnings:this.generateFormWarnings(e)}}catch(e){return console.error("Form validation error:",e),{isValid:!1,errors:[{field:"form",message:"表单验证过程中发生错误",code:"FORM_VALIDATION_ERROR",severity:"error"}],warnings:[]}}}generateFormWarnings(e){let t=[];if(e.items&&Array.isArray(e.items)){let s=e.items.length;s>20&&t.push({field:"items",message:`账单包含${s}个项目，较多`,suggestion:"考虑合并相似项目或分拆为多个账单"}),e.items.reduce((e,t)=>e+(t.quantity||0)*(t.unitPrice||0),0)>5e4&&t.push({field:"form",message:"账单总金额较大",suggestion:"确认金额计算是否正确"})}return t}getFieldValidator(e){return this.fieldValidators.has(e)||this.fieldValidators.set(e,new L(this.schema)),this.fieldValidators.get(e)}cleanup(){this.fieldValidators.forEach(e=>e.cleanup()),this.fieldValidators.clear()}}let U={isValidPaymentAmount:(e,t)=>{let s=t.remainingAmount||0;return e>s?{valid:!1,reason:`支付金额不能超过待付金额 $${s.toFixed(2)}`}:e<=0?{valid:!1,reason:"支付金额必须大于0"}:{valid:!0}}};var J=s(19342),G=s(81646),W=s(23032),H=s(32218),Z=s(26882),Y=s(75895),X=(0,m.A)("outline","cash","IconCash",[["path",{d:"M7 15h-3a1 1 0 0 1 -1 -1v-8a1 1 0 0 1 1 -1h12a1 1 0 0 1 1 1v3",key:"svg-0"}],["path",{d:"M7 9m0 1a1 1 0 0 1 1 -1h12a1 1 0 0 1 1 1v8a1 1 0 0 1 -1 1h-12a1 1 0 0 1 -1 -1z",key:"svg-1"}],["path",{d:"M12 14a2 2 0 1 0 4 0a2 2 0 0 0 -4 0",key:"svg-2"}]]),K=(0,m.A)("outline","device-mobile","IconDeviceMobile",[["path",{d:"M6 5a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-8a2 2 0 0 1 -2 -2v-14z",key:"svg-0"}],["path",{d:"M11 4h2",key:"svg-1"}],["path",{d:"M12 17v.01",key:"svg-2"}]]),Q=(0,m.A)("outline","brand-alipay","IconBrandAlipay",[["path",{d:"M19 3h-14a2 2 0 0 0 -2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2 -2v-14a2 2 0 0 0 -2 -2z",key:"svg-0"}],["path",{d:"M7 7h10",key:"svg-1"}],["path",{d:"M12 3v7",key:"svg-2"}],["path",{d:"M21 17.314c-2.971 -1.923 -15 -8.779 -15 -1.864c0 1.716 1.52 2.55 2.985 2.55c3.512 0 6.814 -5.425 6.814 -8h-6.604",key:"svg-3"}]]),ee=(0,m.A)("outline","building-bank","IconBuildingBank",[["path",{d:"M3 21l18 0",key:"svg-0"}],["path",{d:"M3 10l18 0",key:"svg-1"}],["path",{d:"M5 6l7 -3l7 3",key:"svg-2"}],["path",{d:"M4 10l0 11",key:"svg-3"}],["path",{d:"M20 10l0 11",key:"svg-4"}],["path",{d:"M8 14l0 3",key:"svg-5"}],["path",{d:"M12 14l0 3",key:"svg-6"}],["path",{d:"M16 14l0 3",key:"svg-7"}]]),et=s(95748),es=s(4826);let ea={name:"美丽诊所",address:"北京市朝阳区美丽街123号",phone:"010-********",email:"<EMAIL>",taxId:"91110000000000000X"},er=(0,r.forwardRef)(({payment:e,bill:t,clinicInfo:s=ea},r)=>{let n="object"==typeof e.patient?e.patient:null,i=t||("object"==typeof e.bill?e.bill:null);return(0,a.jsx)("div",{ref:r,className:"max-w-md mx-auto bg-white",children:(0,a.jsxs)(H.Zp,{className:"shadow-none border-none",children:[(0,a.jsxs)(H.aR,{className:"text-center pb-4",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("h1",{className:"text-xl font-bold",children:s.name}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:s.address}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["电话: ",s.phone,s.email&&` | 邮箱: ${s.email}`]}),s.taxId&&(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["税号: ",s.taxId]})]}),(0,a.jsx)(Y.Separator,{className:"my-4"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"收款收据"}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"收据编号:"}),(0,a.jsx)("span",{className:"font-mono",children:e.receiptNumber||"待生成"})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"支付编号:"}),(0,a.jsx)("span",{className:"font-mono",children:e.paymentNumber})]})]})]}),(0,a.jsxs)(H.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h3",{className:"font-medium text-sm border-b pb-1",children:"支付信息"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"患者姓名:"}),(0,a.jsx)("span",{children:n?.fullName||"未知患者"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"支付日期:"}),(0,a.jsx)("span",{children:new Date(e.paymentDate).toLocaleDateString("zh-CN")})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"支付方式:"}),(0,a.jsx)("span",{children:T.getPaymentMethodName(e.paymentMethod)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"支付状态:"}),(0,a.jsx)(l.E,{variant:"completed"===e.paymentStatus?"default":"secondary",children:T.getPaymentStatusName(e.paymentStatus)})]})]}),e.transactionId&&(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"交易ID:"}),(0,a.jsx)("span",{className:"font-mono text-xs",children:e.transactionId})]})]}),(0,a.jsx)(Y.Separator,{}),i&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h3",{className:"font-medium text-sm border-b pb-1",children:"账单信息"}),(0,a.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"账单编号:"}),(0,a.jsx)("span",{className:"font-mono",children:i.billNumber})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"账单类型:"}),(0,a.jsx)("span",{children:T.getBillStatusName(i.billType)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"服务描述:"}),(0,a.jsx)("span",{className:"text-right max-w-32 truncate",children:i.description})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"账单总额:"}),(0,a.jsx)("span",{children:T.formatCurrency(i.totalAmount)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"已支付:"}),(0,a.jsx)("span",{className:"text-green-600",children:T.formatCurrency(i.paidAmount||0)})]}),(i.remainingAmount||0)>0&&(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"待支付:"}),(0,a.jsx)("span",{className:"text-red-600",children:T.formatCurrency(i.remainingAmount||0)})]})]})]}),(0,a.jsx)(Y.Separator,{}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h3",{className:"font-medium text-sm border-b pb-1",children:"本次支付"}),(0,a.jsx)("div",{className:"bg-muted/30 rounded-lg p-3",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-lg font-medium",children:"支付金额:"}),(0,a.jsx)("span",{className:"text-xl font-bold text-green-600",children:T.formatCurrency(e.amount)})]})})]}),e.notes&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(Y.Separator,{}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h3",{className:"font-medium text-sm border-b pb-1",children:"备注"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.notes})]})]}),(0,a.jsx)(Y.Separator,{}),(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["感谢您选择",s.name]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["如有疑问，请联系我们: ",s.phone]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["打印时间: ",new Date().toLocaleString("zh-CN")]})]})]})]})})});er.displayName="Receipt";var en=(0,m.A)("outline","printer","IconPrinter",[["path",{d:"M17 17h2a2 2 0 0 0 2 -2v-4a2 2 0 0 0 -2 -2h-14a2 2 0 0 0 -2 2v4a2 2 0 0 0 2 2h2",key:"svg-0"}],["path",{d:"M17 9v-4a2 2 0 0 0 -2 -2h-6a2 2 0 0 0 -2 2v4",key:"svg-1"}],["path",{d:"M7 13m0 2a2 2 0 0 1 2 -2h6a2 2 0 0 1 2 2v4a2 2 0 0 1 -2 2h-6a2 2 0 0 1 -2 -2z",key:"svg-2"}]]),el=(0,m.A)("outline","download","IconDownload",[["path",{d:"M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2",key:"svg-0"}],["path",{d:"M7 11l5 5l5 -5",key:"svg-1"}],["path",{d:"M12 4l0 12",key:"svg-2"}]]);function ei({payment:e,bill:t,isOpen:s,onClose:n}){let l=(0,r.useRef)(null),o=async()=>{try{z.system.featureNotImplemented("PDF下载")}catch(e){console.error("PDF generation failed:",e),z.receipt.downloadError()}};return e?(0,a.jsx)(D.lG,{open:s,onOpenChange:n,"data-sentry-element":"Dialog","data-sentry-component":"ReceiptDialog","data-sentry-source-file":"receipt-dialog.tsx",children:(0,a.jsxs)(D.Cf,{className:"max-w-lg max-h-[90vh] overflow-y-auto","data-sentry-element":"DialogContent","data-sentry-source-file":"receipt-dialog.tsx",children:[(0,a.jsx)(D.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"receipt-dialog.tsx",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(D.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"receipt-dialog.tsx",children:["收据 - ",e.receiptNumber||e.paymentNumber]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>{if(!l.current)return;let t=window.open("","_blank");if(!t)return void z.receipt.printError();let s=l.current.innerHTML;t.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>收据 - ${e?.receiptNumber||e?.paymentNumber}</title>
          <meta charset="utf-8">
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              margin: 0;
              padding: 20px;
              background: white;
            }
            .max-w-md {
              max-width: 28rem;
            }
            .mx-auto {
              margin-left: auto;
              margin-right: auto;
            }
            .bg-white {
              background-color: white;
            }
            .shadow-none {
              box-shadow: none;
            }
            .border-none {
              border: none;
            }
            .text-center {
              text-align: center;
            }
            .text-xl {
              font-size: 1.25rem;
            }
            .text-lg {
              font-size: 1.125rem;
            }
            .text-sm {
              font-size: 0.875rem;
            }
            .text-xs {
              font-size: 0.75rem;
            }
            .font-bold {
              font-weight: 700;
            }
            .font-semibold {
              font-weight: 600;
            }
            .font-medium {
              font-weight: 500;
            }
            .font-mono {
              font-family: ui-monospace, SFMono-Regular, monospace;
            }
            .space-y-1 > * + * {
              margin-top: 0.25rem;
            }
            .space-y-2 > * + * {
              margin-top: 0.5rem;
            }
            .space-y-4 > * + * {
              margin-top: 1rem;
            }
            .pb-4 {
              padding-bottom: 1rem;
            }
            .pb-1 {
              padding-bottom: 0.25rem;
            }
            .p-3 {
              padding: 0.75rem;
            }
            .my-4 {
              margin-top: 1rem;
              margin-bottom: 1rem;
            }
            .border-b {
              border-bottom: 1px solid #e5e7eb;
            }
            .grid {
              display: grid;
            }
            .grid-cols-2 {
              grid-template-columns: repeat(2, minmax(0, 1fr));
            }
            .gap-2 {
              gap: 0.5rem;
            }
            .flex {
              display: flex;
            }
            .justify-between {
              justify-content: space-between;
            }
            .items-center {
              align-items: center;
            }
            .text-right {
              text-align: right;
            }
            .max-w-32 {
              max-width: 8rem;
            }
            .truncate {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .text-muted-foreground {
              color: #6b7280;
            }
            .text-green-600 {
              color: #059669;
            }
            .text-red-600 {
              color: #dc2626;
            }
            .bg-muted\\/30 {
              background-color: rgba(243, 244, 246, 0.3);
            }
            .rounded-lg {
              border-radius: 0.5rem;
            }
            hr {
              border: none;
              border-top: 1px solid #e5e7eb;
              margin: 1rem 0;
            }
            @media print {
              body {
                padding: 0;
              }
              .no-print {
                display: none;
              }
            }
          </style>
        </head>
        <body>
          ${s}
        </body>
      </html>
    `),t.document.close(),t.focus(),setTimeout(()=>{t.print(),t.close()},250),z.receipt.printed(e?.receiptNumber||e?.paymentNumber||"")},"data-sentry-element":"Button","data-sentry-source-file":"receipt-dialog.tsx",children:[(0,a.jsx)(en,{className:"h-4 w-4 mr-2","data-sentry-element":"IconPrinter","data-sentry-source-file":"receipt-dialog.tsx"}),"打印"]}),(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:o,"data-sentry-element":"Button","data-sentry-source-file":"receipt-dialog.tsx",children:[(0,a.jsx)(el,{className:"h-4 w-4 mr-2","data-sentry-element":"IconDownload","data-sentry-source-file":"receipt-dialog.tsx"}),"下载PDF"]}),(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:n,"data-sentry-element":"Button","data-sentry-source-file":"receipt-dialog.tsx",children:(0,a.jsx)(es.A,{className:"h-4 w-4","data-sentry-element":"IconX","data-sentry-source-file":"receipt-dialog.tsx"})})]})]})}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)(er,{ref:l,payment:e,bill:t,"data-sentry-element":"Receipt","data-sentry-source-file":"receipt-dialog.tsx"})})]})}):null}let eo=[{value:"cash",label:"现金",icon:X,description:"现金支付",requiresTransactionId:!1},{value:"card",label:"银行卡",icon:f.A,description:"银行卡刷卡支付",requiresTransactionId:!0},{value:"wechat",label:"微信支付",icon:K,description:"微信扫码支付",requiresTransactionId:!0},{value:"alipay",label:"支付宝",icon:Q,description:"支付宝扫码支付",requiresTransactionId:!0},{value:"transfer",label:"银行转账",icon:ee,description:"银行转账支付",requiresTransactionId:!0},{value:"installment",label:"分期付款",icon:et.A,description:"分期付款",requiresTransactionId:!1}];function ed({bill:e,onSuccess:t,onCancel:s,isOpen:n=!0}){let[l,o]=(0,r.useState)(!1),[d,c]=(0,r.useState)(""),[m,x]=(0,r.useState)(null),[p,h]=(0,r.useState)(!1),f=(0,k.mN)({resolver:(0,E.u)(O),defaultValues:{amount:e.remainingAmount||0,paymentMethod:"cash",transactionId:"",notes:""}}),y=eo.find(e=>e.value===d),g=e.remainingAmount||0,j=async s=>{try{o(!0);let a=U.isValidPaymentAmount(s.amount,e);if(!a.valid)return void z.payment.validationError(a.reason||"支付金额无效");let r=await S.processPayment({bill:e.id,patient:"object"==typeof e.patient?e.patient.id:e.patientId,amount:s.amount,paymentMethod:s.paymentMethod,transactionId:s.transactionId||void 0,notes:s.notes||void 0});z.payment.processed(r),x(r),h(!0),t&&t(r),f.reset()}catch(t){console.error("Payment processing failed:",t);let e=t instanceof C?t.message:void 0;z.payment.processError(e)}finally{o(!1)}};return n?(0,a.jsxs)(H.Zp,{className:"w-full max-w-2xl mx-auto","data-sentry-element":"Card","data-sentry-component":"PaymentForm","data-sentry-source-file":"payment-form.tsx",children:[(0,a.jsx)(H.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"payment-form.tsx",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(H.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"payment-form.tsx",children:[(0,a.jsx)(u,{className:"h-5 w-5","data-sentry-element":"IconReceipt","data-sentry-source-file":"payment-form.tsx"}),"处理支付"]}),(0,a.jsxs)(H.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"payment-form.tsx",children:["为账单 ",e.billNumber," 处理支付"]})]}),s&&(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:s,children:(0,a.jsx)(es.A,{className:"h-4 w-4"})})]})}),(0,a.jsxs)(H.Wu,{className:"space-y-6","data-sentry-element":"CardContent","data-sentry-source-file":"payment-form.tsx",children:[(0,a.jsxs)("div",{className:"bg-muted/50 rounded-lg p-4 space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"患者:"}),(0,a.jsx)("span",{className:"text-sm",children:"object"==typeof e.patient?e.patient.fullName:"未知患者"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"账单总额:"}),(0,a.jsx)("span",{className:"text-sm font-semibold",children:T.formatCurrency(e.totalAmount)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"已支付:"}),(0,a.jsx)("span",{className:"text-sm text-green-600",children:T.formatCurrency(e.paidAmount||0)})]}),(0,a.jsx)(Y.Separator,{"data-sentry-element":"Separator","data-sentry-source-file":"payment-form.tsx"}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"待支付:"}),(0,a.jsx)("span",{className:"text-lg font-bold text-red-600",children:T.formatCurrency(g)})]})]}),(0,a.jsx)(Z.lV,{...f,"data-sentry-element":"Form","data-sentry-source-file":"payment-form.tsx",children:(0,a.jsxs)("form",{onSubmit:f.handleSubmit(j),className:"space-y-6",children:[(0,a.jsx)(Z.zB,{control:f.control,name:"amount",render:({field:e})=>(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"支付金额"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("span",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground",children:"$"}),(0,a.jsx)(J.p,{type:"number",step:"0.01",min:"0.01",max:g,placeholder:"0.00",className:"pl-8",...e,onChange:t=>e.onChange(parseFloat(t.target.value)||0)})]})}),(0,a.jsxs)(Z.Rr,{children:["最大支付金额: ",T.formatCurrency(g)]}),(0,a.jsx)(Z.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"payment-form.tsx"}),(0,a.jsx)(Z.zB,{control:f.control,name:"paymentMethod",render:({field:e})=>(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"支付方式"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsxs)(W.l6,{value:e.value,onValueChange:t=>{e.onChange(t),c(t)},children:[(0,a.jsx)(W.bq,{children:(0,a.jsx)(W.yv,{placeholder:"选择支付方式"})}),(0,a.jsx)(W.gC,{children:eo.map(e=>{let t=e.icon;return(0,a.jsx)(W.eb,{value:e.value,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(t,{className:"h-4 w-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.label}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]})]})},e.value)})})]})}),(0,a.jsx)(Z.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"payment-form.tsx"}),y?.requiresTransactionId&&(0,a.jsx)(Z.zB,{control:f.control,name:"transactionId",render:({field:e})=>(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"交易ID"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsx)(J.p,{placeholder:"输入第三方支付平台的交易ID",...e})}),(0,a.jsxs)(Z.Rr,{children:["请输入",y.label,"的交易ID或流水号"]}),(0,a.jsx)(Z.C5,{})]})}),(0,a.jsx)(Z.zB,{control:f.control,name:"notes",render:({field:e})=>(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"备注 (可选)"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsx)(G.T,{placeholder:"支付相关备注信息...",className:"resize-none",rows:3,...e})}),(0,a.jsx)(Z.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"payment-form.tsx"}),(0,a.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,a.jsx)(i.$,{type:"submit",disabled:l,className:"flex-1","data-sentry-element":"Button","data-sentry-source-file":"payment-form.tsx",children:l?"处理中...":"确认支付"}),s&&(0,a.jsx)(i.$,{type:"button",variant:"outline",onClick:s,disabled:l,children:"取消"})]})]})})]}),(0,a.jsx)(ei,{payment:m,bill:e,isOpen:p,onClose:()=>{h(!1),x(null)},"data-sentry-element":"ReceiptDialog","data-sentry-source-file":"payment-form.tsx"})]}):null}function ec({bill:e,isOpen:t,onClose:s,onSuccess:r}){return e?(0,a.jsx)(D.lG,{open:t,onOpenChange:s,"data-sentry-element":"Dialog","data-sentry-component":"PaymentDialog","data-sentry-source-file":"payment-dialog.tsx",children:(0,a.jsxs)(D.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto","data-sentry-element":"DialogContent","data-sentry-source-file":"payment-dialog.tsx",children:[(0,a.jsx)(D.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"payment-dialog.tsx",children:(0,a.jsxs)(D.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"payment-dialog.tsx",children:["处理支付 - ",e.billNumber]})}),(0,a.jsx)(ed,{bill:e,onSuccess:e=>{r&&r(e),s()},onCancel:s,isOpen:!0,"data-sentry-element":"PaymentForm","data-sentry-source-file":"payment-dialog.tsx"})]})}):null}var em=s(18984),eu=s(46643),ex=s(9752);let ep=[{value:"treatment",label:"治疗账单"},{value:"consultation",label:"咨询账单"},{value:"deposit",label:"押金账单"},{value:"additional",label:"补充账单"}],eh=[{value:"treatment",label:"治疗项目"},{value:"consultation",label:"咨询服务"},{value:"material",label:"材料费用"},{value:"service",label:"其他服务"}];function ef({bill:e,patients:t=[],appointments:s=[],treatments:n=[],onSuccess:l,onCancel:o,isOpen:d=!0}){let[c,m]=(0,r.useState)(!1),[p,h]=(0,r.useState)({subtotal:0,totalAmount:0}),[f]=(0,r.useState)(()=>new V(_)),[y,g]=(0,r.useState)({}),j=!!e,b=(0,k.mN)({resolver:(0,E.u)(_),defaultValues:{patient:e?.patientId?String(e.patientId):"",appointment:e?.appointmentId?String(e.appointmentId):"",treatment:e?.treatmentId?String(e.treatmentId):"",billType:e?.billType||"treatment",description:e?.description||"",notes:e?.notes||"",dueDate:e?.dueDate?new Date(e.dueDate).toISOString().split("T")[0]:"",discountAmount:e?.discountAmount||0,taxAmount:e?.taxAmount||0,items:e?.items?.map(e=>({itemType:e.itemType,itemName:e.itemName,description:e.description||"",quantity:e.quantity,unitPrice:e.unitPrice,discountRate:e.discountRate||0}))||[{itemType:"treatment",itemName:"",description:"",quantity:1,unitPrice:0,discountRate:0}]}}),{fields:v,append:N,remove:w}=(0,k.jz)({control:b.control,name:"items"}),A=b.watch("items"),S=b.watch("discountAmount"),R=b.watch("taxAmount"),D=async t=>{try{let s;m(!0);let a={patient:parseInt(t.patient),appointment:t.appointment&&"none"!==t.appointment?parseInt(t.appointment):void 0,treatment:t.treatment&&"none"!==t.treatment?parseInt(t.treatment):void 0,billType:t.billType,subtotal:p.subtotal,discountAmount:t.discountAmount||0,taxAmount:t.taxAmount||0,totalAmount:p.totalAmount,description:t.description,notes:t.notes||void 0,dueDate:t.dueDate,items:t.items.map(e=>({itemType:e.itemType,itemName:e.itemName,description:e.description||void 0,quantity:e.quantity,unitPrice:e.unitPrice,discountRate:e.discountRate||0}))};j&&e?(s=await I.updateBill(e.id,a),z.bill.updated(s)):(s=await I.createBill(a),z.bill.created(s)),l&&l(s),j||b.reset()}catch(t){console.error("Bill operation failed:",t);let e=t instanceof C?t.message:void 0;j?z.bill.updateError(e):z.bill.createError(e)}finally{m(!1)}},P=e=>{v.length>1?w(e):z.validation.confirmAction("至少需要保留一个账单项目")};return d?(0,a.jsxs)(H.Zp,{className:"w-full max-w-4xl mx-auto","data-sentry-element":"Card","data-sentry-component":"BillForm","data-sentry-source-file":"bill-form.tsx",children:[(0,a.jsx)(H.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"bill-form.tsx",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(H.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"bill-form.tsx",children:[(0,a.jsx)(u,{className:"h-5 w-5","data-sentry-element":"IconReceipt","data-sentry-source-file":"bill-form.tsx"}),j?"编辑账单":"创建账单"]}),(0,a.jsx)(H.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"bill-form.tsx",children:j?`编辑账单 ${e?.billNumber}`:"创建新的账单"})]}),o&&(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:o,children:(0,a.jsx)(es.A,{className:"h-4 w-4"})})]})}),(0,a.jsx)(H.Wu,{className:"space-y-6","data-sentry-element":"CardContent","data-sentry-source-file":"bill-form.tsx",children:(0,a.jsx)(Z.lV,{...b,"data-sentry-element":"Form","data-sentry-source-file":"bill-form.tsx",children:(0,a.jsxs)("form",{onSubmit:b.handleSubmit(D),className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)(Z.zB,{control:b.control,name:"patient",render:({field:e})=>(0,a.jsxs)(Z.eI,{children:[(0,a.jsxs)(Z.lR,{className:"flex items-center gap-2",children:[(0,a.jsx)(eu.A,{className:"h-4 w-4"}),"患者"]}),(0,a.jsx)(Z.MJ,{children:(0,a.jsxs)(W.l6,{value:e.value,onValueChange:e.onChange,children:[(0,a.jsx)(W.bq,{children:(0,a.jsx)(W.yv,{placeholder:"选择患者"})}),(0,a.jsx)(W.gC,{children:t.map(e=>(0,a.jsxs)(W.eb,{value:String(e.id),children:[e.fullName," - ",e.phone]},e.id))})]})}),(0,a.jsx)(Z.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"bill-form.tsx"}),(0,a.jsx)(Z.zB,{control:b.control,name:"billType",render:({field:e})=>(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"账单类型"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsxs)(W.l6,{value:e.value,onValueChange:e.onChange,children:[(0,a.jsx)(W.bq,{children:(0,a.jsx)(W.yv,{placeholder:"选择账单类型"})}),(0,a.jsx)(W.gC,{children:ep.map(e=>(0,a.jsx)(W.eb,{value:e.value,children:e.label},e.value))})]})}),(0,a.jsx)(Z.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"bill-form.tsx"}),(0,a.jsx)(Z.zB,{control:b.control,name:"appointment",render:({field:e})=>(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"关联预约 (可选)"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsxs)(W.l6,{value:e.value||"",onValueChange:e.onChange,children:[(0,a.jsx)(W.bq,{children:(0,a.jsx)(W.yv,{placeholder:"选择预约"})}),(0,a.jsxs)(W.gC,{children:[(0,a.jsx)(W.eb,{value:"none",children:"无关联预约"}),s.map(e=>(0,a.jsxs)(W.eb,{value:e.id,children:[new Date(e.appointmentDate).toLocaleDateString("zh-CN")," -","object"==typeof e.treatment?e.treatment.name:"未知治疗"]},e.id))]})]})}),(0,a.jsx)(Z.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"bill-form.tsx"}),(0,a.jsx)(Z.zB,{control:b.control,name:"dueDate",render:({field:e})=>(0,a.jsxs)(Z.eI,{children:[(0,a.jsxs)(Z.lR,{className:"flex items-center gap-2",children:[(0,a.jsx)(et.A,{className:"h-4 w-4"}),"到期日期"]}),(0,a.jsx)(Z.MJ,{children:(0,a.jsx)(J.p,{type:"date",...e})}),(0,a.jsx)(Z.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"bill-form.tsx"})]}),(0,a.jsx)(Z.zB,{control:b.control,name:"description",render:({field:e})=>(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"账单描述"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsx)(J.p,{placeholder:"输入账单描述...",...e})}),(0,a.jsx)(Z.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"bill-form.tsx"}),(0,a.jsx)(Z.zB,{control:b.control,name:"notes",render:({field:e})=>(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"备注 (可选)"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsx)(G.T,{placeholder:"账单相关备注...",className:"resize-none",rows:3,...e})}),(0,a.jsx)(Z.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"bill-form.tsx"}),(0,a.jsx)(Y.Separator,{"data-sentry-element":"Separator","data-sentry-source-file":"bill-form.tsx"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"账单项目"}),(0,a.jsxs)(i.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{N({itemType:"treatment",itemName:"",description:"",quantity:1,unitPrice:0,discountRate:0})},"data-sentry-element":"Button","data-sentry-source-file":"bill-form.tsx",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2","data-sentry-element":"IconPlus","data-sentry-source-file":"bill-form.tsx"}),"添加项目"]})]}),(0,a.jsx)("div",{className:"space-y-4",children:v.map((e,t)=>(0,a.jsxs)(H.Zp,{className:"p-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-6 gap-4 items-end",children:[(0,a.jsx)(Z.zB,{control:b.control,name:`items.${t}.itemType`,render:({field:e})=>(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"类型"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsxs)(W.l6,{value:e.value,onValueChange:e.onChange,children:[(0,a.jsx)(W.bq,{children:(0,a.jsx)(W.yv,{})}),(0,a.jsx)(W.gC,{children:eh.map(e=>(0,a.jsx)(W.eb,{value:e.value,children:e.label},e.value))})]})}),(0,a.jsx)(Z.C5,{})]})}),(0,a.jsx)(Z.zB,{control:b.control,name:`items.${t}.itemName`,render:({field:e})=>(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"项目名称"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsx)(J.p,{placeholder:"项目名称",...e})}),(0,a.jsx)(Z.C5,{})]})}),(0,a.jsx)(Z.zB,{control:b.control,name:`items.${t}.quantity`,render:({field:e})=>(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"数量"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsx)(J.p,{type:"number",step:"0.01",min:"0.01",placeholder:"1",...e,onChange:t=>e.onChange(parseFloat(t.target.value)||0)})}),(0,a.jsx)(Z.C5,{})]})}),(0,a.jsx)(Z.zB,{control:b.control,name:`items.${t}.unitPrice`,render:({field:e})=>(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"单价"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsx)(J.p,{type:"number",step:"0.01",min:"0",placeholder:"0.00",...e,onChange:t=>e.onChange(parseFloat(t.target.value)||0)})}),(0,a.jsx)(Z.C5,{})]})}),(0,a.jsx)(Z.zB,{control:b.control,name:`items.${t}.discountRate`,render:({field:e})=>(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"折扣率 (%)"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsx)(J.p,{type:"number",step:"0.1",min:"0",max:"100",placeholder:"0",...e,onChange:t=>e.onChange(parseFloat(t.target.value)||0)})}),(0,a.jsx)(Z.C5,{})]})}),(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)(i.$,{type:"button",variant:"outline",size:"sm",onClick:()=>P(t),disabled:v.length<=1,children:(0,a.jsx)(ex.A,{className:"h-4 w-4"})})})]}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)(Z.zB,{control:b.control,name:`items.${t}.description`,render:({field:e})=>(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"项目描述 (可选)"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsx)(G.T,{placeholder:"项目详细描述...",className:"resize-none",rows:2,...e})}),(0,a.jsx)(Z.C5,{})]})})}),(0,a.jsx)("div",{className:"mt-2 text-right",children:(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["小计: ",T.formatCurrency((()=>{let e=A[t];if(!e)return 0;let s=(e.quantity||0)*(e.unitPrice||0),a=s*((e.discountRate||0)/100);return s-a})())]})})]},e.id))})]}),(0,a.jsx)(Y.Separator,{"data-sentry-element":"Separator","data-sentry-source-file":"bill-form.tsx"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsx)(Z.zB,{control:b.control,name:"discountAmount",render:({field:e})=>(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"额外折扣金额"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsx)(J.p,{type:"number",step:"0.01",min:"0",placeholder:"0.00",...e,onChange:t=>e.onChange(parseFloat(t.target.value)||0)})}),(0,a.jsx)(Z.Rr,{children:"在项目折扣基础上的额外折扣"}),(0,a.jsx)(Z.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"bill-form.tsx"}),(0,a.jsx)(Z.zB,{control:b.control,name:"taxAmount",render:({field:e})=>(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"税费金额"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsx)(J.p,{type:"number",step:"0.01",min:"0",placeholder:"0.00",...e,onChange:t=>e.onChange(parseFloat(t.target.value)||0)})}),(0,a.jsx)(Z.Rr,{children:"需要添加的税费金额"}),(0,a.jsx)(Z.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"bill-form.tsx"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(em.J,{"data-sentry-element":"Label","data-sentry-source-file":"bill-form.tsx",children:"账单总计"}),(0,a.jsxs)("div",{className:"bg-muted/50 rounded-lg p-3 space-y-1",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"项目小计:"}),(0,a.jsx)("span",{children:T.formatCurrency(p.subtotal)})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"额外折扣:"}),(0,a.jsxs)("span",{children:["-",T.formatCurrency(S||0)]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"税费:"}),(0,a.jsxs)("span",{children:["+",T.formatCurrency(R||0)]})]}),(0,a.jsx)(Y.Separator,{"data-sentry-element":"Separator","data-sentry-source-file":"bill-form.tsx"}),(0,a.jsxs)("div",{className:"flex justify-between font-semibold",children:[(0,a.jsx)("span",{children:"总金额:"}),(0,a.jsx)("span",{className:"text-lg",children:T.formatCurrency(p.totalAmount)})]})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,a.jsx)(i.$,{type:"submit",disabled:c,className:"flex-1","data-sentry-element":"Button","data-sentry-source-file":"bill-form.tsx",children:c?j?"更新中...":"创建中...":j?"更新账单":"创建账单"}),o&&(0,a.jsx)(i.$,{type:"button",variant:"outline",onClick:o,disabled:c,children:"取消"})]})]})})})]}):null}function ey({bill:e,isOpen:t,onClose:s,onSuccess:n}){let[l,i]=(0,r.useState)([]),[o,d]=(0,r.useState)([]),[c,m]=(0,r.useState)([]),[u,x]=(0,r.useState)(!1),p=!!e;return(0,a.jsx)(D.lG,{open:t,onOpenChange:s,"data-sentry-element":"Dialog","data-sentry-component":"BillDialog","data-sentry-source-file":"bill-dialog.tsx",children:(0,a.jsxs)(D.Cf,{className:"max-w-5xl max-h-[90vh] overflow-y-auto","data-sentry-element":"DialogContent","data-sentry-source-file":"bill-dialog.tsx",children:[(0,a.jsx)(D.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"bill-dialog.tsx",children:(0,a.jsx)(D.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"bill-dialog.tsx",children:p?`编辑账单 - ${e?.billNumber}`:"创建新账单"})}),u?(0,a.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"}),(0,a.jsx)("span",{className:"ml-2 text-muted-foreground",children:"加载数据中..."})]}):(0,a.jsx)(ef,{bill:e||void 0,patients:l,appointments:o,treatments:c,onSuccess:e=>{n&&n(e),s()},onCancel:s,isOpen:!0})]})})}var eg=s(91476),ej=s(44537),eb=s(68343),ev=s(69322),eN=s(92700);let ew={draft:["sent","cancelled"],sent:["confirmed","cancelled"],confirmed:["paid","cancelled"],paid:[],cancelled:[]},eC={draft:{label:"草稿",color:"bg-gray-100 text-gray-800",icon:ej.A,description:"账单正在编辑中"},sent:{label:"已发送",color:"bg-blue-100 text-blue-800",icon:eb.A,description:"账单已发送给患者"},confirmed:{label:"已确认",color:"bg-yellow-100 text-yellow-800",icon:ev.A,description:"患者已确认账单"},paid:{label:"已支付",color:"bg-green-100 text-green-800",icon:f.A,description:"账单已完全支付"},cancelled:{label:"已取消",color:"bg-red-100 text-red-800",icon:es.A,description:"账单已取消"}};function eA({bill:e,onStatusUpdate:t,trigger:s}){let{hasPermission:n}=(0,o.It)(),[d,c]=(0,r.useState)(!1),[m,u]=(0,r.useState)(e.status),[x,p]=(0,r.useState)(""),[f,g]=(0,r.useState)(!1),j=eC[e.status],b=ew[e.status]||[],v=n("canEditBills")&&b.length>0,N=async()=>{if(m===e.status)return void y.toast.warning("请选择不同的状态");try{if(g(!0),!b.includes(m))return void y.toast.error("无效的状态转换");if("paid"===m&&(e.remainingAmount||0)>0)return void y.toast.error("账单还有未支付金额，无法标记为已支付");let s={status:m};x.trim()&&(s.notes=e.notes?`${e.notes}

[状态更新] ${x.trim()}`:`[状态更新] ${x.trim()}`);let a=await I.updateBill(e.id,s);y.toast.success(`账单状态已更新为: ${eC[m].label}`),t&&t(a),c(!1),p("")}catch(t){console.error("Failed to update bill status:",t);let e=t instanceof C?t.message:"状态更新失败，请稍后重试";y.toast.error(e)}finally{g(!1)}},w=e=>{switch(e){case"sent":return"发送账单后，患者将收到账单通知";case"confirmed":return"确认账单表示患者已同意账单内容";case"paid":return"标记为已支付前，请确保所有款项已收到";case"cancelled":return"取消账单后将无法恢复，请谨慎操作";default:return null}};return v?(0,a.jsxs)(D.lG,{open:d,onOpenChange:c,"data-sentry-element":"Dialog","data-sentry-component":"BillStatusManager","data-sentry-source-file":"bill-status-manager.tsx",children:[(0,a.jsx)(D.zM,{asChild:!0,"data-sentry-element":"DialogTrigger","data-sentry-source-file":"bill-status-manager.tsx",children:s||(0,a.jsxs)(i.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"更新状态"]})}),(0,a.jsxs)(D.Cf,{className:"max-w-md","data-sentry-element":"DialogContent","data-sentry-source-file":"bill-status-manager.tsx",children:[(0,a.jsxs)(D.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"bill-status-manager.tsx",children:[(0,a.jsx)(D.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"bill-status-manager.tsx",children:"更新账单状态"}),(0,a.jsxs)(D.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"bill-status-manager.tsx",children:["账单编号: ",e.billNumber]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(em.J,{className:"text-sm font-medium","data-sentry-element":"Label","data-sentry-source-file":"bill-status-manager.tsx",children:"当前状态"}),(0,a.jsxs)("div",{className:"mt-1",children:[(0,a.jsxs)(l.E,{className:j.color,"data-sentry-element":"Badge","data-sentry-source-file":"bill-status-manager.tsx",children:[(0,a.jsx)(j.icon,{className:"h-3 w-3 mr-1","data-sentry-element":"currentStatusConfig.icon","data-sentry-source-file":"bill-status-manager.tsx"}),j.label]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:j.description})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(em.J,{htmlFor:"status-select",className:"text-sm font-medium","data-sentry-element":"Label","data-sentry-source-file":"bill-status-manager.tsx",children:"新状态"}),(0,a.jsxs)(W.l6,{value:m,onValueChange:u,"data-sentry-element":"Select","data-sentry-source-file":"bill-status-manager.tsx",children:[(0,a.jsx)(W.bq,{className:"mt-1","data-sentry-element":"SelectTrigger","data-sentry-source-file":"bill-status-manager.tsx",children:(0,a.jsx)(W.yv,{placeholder:"选择新状态","data-sentry-element":"SelectValue","data-sentry-source-file":"bill-status-manager.tsx"})}),(0,a.jsx)(W.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"bill-status-manager.tsx",children:b.map(e=>{let t=eC[e],s=t.icon;return(0,a.jsx)(W.eb,{value:e,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(s,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:t.label})]})},e)})})]})]}),m!==e.status&&w(m)&&(0,a.jsxs)(eg.Fc,{children:[(0,a.jsx)(eN.A,{className:"h-4 w-4"}),(0,a.jsx)(eg.TN,{children:w(m)})]}),"paid"===m&&(e.remainingAmount||0)>0&&(0,a.jsxs)(eg.Fc,{variant:"destructive",children:[(0,a.jsx)(eN.A,{className:"h-4 w-4"}),(0,a.jsxs)(eg.TN,{children:["账单还有 ",T.formatCurrency(e.remainingAmount||0)," 未支付， 无法标记为已支付状态。"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(em.J,{htmlFor:"notes",className:"text-sm font-medium","data-sentry-element":"Label","data-sentry-source-file":"bill-status-manager.tsx",children:"备注 (可选)"}),(0,a.jsx)(G.T,{id:"notes",placeholder:"添加状态更新的备注...",value:x,onChange:e=>p(e.target.value),className:"mt-1 resize-none",rows:3,"data-sentry-element":"Textarea","data-sentry-source-file":"bill-status-manager.tsx"})]}),(0,a.jsxs)("div",{className:"flex gap-2 pt-2",children:[(0,a.jsx)(i.$,{onClick:N,disabled:f||m===e.status||"paid"===m&&(e.remainingAmount||0)>0,className:"flex-1","data-sentry-element":"Button","data-sentry-source-file":"bill-status-manager.tsx",children:f?"更新中...":"确认更新"}),(0,a.jsx)(i.$,{variant:"outline",onClick:()=>{c(!1),u(e.status),p("")},disabled:f,"data-sentry-element":"Button","data-sentry-source-file":"bill-status-manager.tsx",children:"取消"})]})]})]})]}):(0,a.jsxs)(l.E,{className:j.color,children:[(0,a.jsx)(j.icon,{className:"h-3 w-3 mr-1"}),j.label]})}var eI=s(5149),eS=s(17838),eR=s(33520),eT=(0,m.A)("outline","currency-yuan","IconCurrencyYuan",[["path",{d:"M12 19v-7l-5 -7",key:"svg-0"}],["path",{d:"M17 5l-5 7",key:"svg-1"}],["path",{d:"M8 13h8",key:"svg-2"}]]);let eD=[{value:"draft",label:"草稿"},{value:"sent",label:"已发送"},{value:"confirmed",label:"已确认"},{value:"paid",label:"已支付"},{value:"cancelled",label:"已取消"}],ek=[{value:"treatment",label:"治疗账单"},{value:"consultation",label:"咨询账单"},{value:"deposit",label:"押金账单"},{value:"additional",label:"补充账单"}];function eE({filters:e,onFiltersChange:t,patients:s=[],className:n}){let[o,d]=(0,r.useState)(!1),[c,m]=(0,r.useState)(e),u=Object.values(e).some(e=>void 0!==e&&""!==e&&null!==e),x=Object.values(e).filter(e=>void 0!==e&&""!==e&&null!==e).length,p=(e,t)=>{m({...c,[e]:t})},h=()=>{let e={};m(e),t(e),d(!1)},f=s=>{let a={...e};delete a[s],t(a)};return(0,a.jsxs)("div",{className:`space-y-3 ${n}`,"data-sentry-component":"BillFilters","data-sentry-source-file":"bill-filters.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(eS.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4","data-sentry-element":"IconSearch","data-sentry-source-file":"bill-filters.tsx"}),(0,a.jsx)(J.p,{placeholder:"搜索账单编号、患者姓名或描述...",value:e.search||"",onChange:s=>t({...e,search:s.target.value}),className:"pl-10","data-sentry-element":"Input","data-sentry-source-file":"bill-filters.tsx"})]}),(0,a.jsxs)(eI.AM,{open:o,onOpenChange:d,"data-sentry-element":"Popover","data-sentry-source-file":"bill-filters.tsx",children:[(0,a.jsx)(eI.Wv,{asChild:!0,"data-sentry-element":"PopoverTrigger","data-sentry-source-file":"bill-filters.tsx",children:(0,a.jsxs)(i.$,{variant:"outline",className:"relative","data-sentry-element":"Button","data-sentry-source-file":"bill-filters.tsx",children:[(0,a.jsx)(eR.A,{className:"h-4 w-4 mr-2","data-sentry-element":"IconFilter","data-sentry-source-file":"bill-filters.tsx"}),"高级筛选",x>0&&(0,a.jsx)(l.E,{variant:"secondary",className:"ml-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs",children:x})]})}),(0,a.jsx)(eI.hl,{className:"w-80 p-4",align:"end","data-sentry-element":"PopoverContent","data-sentry-source-file":"bill-filters.tsx",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"font-medium",children:"高级筛选"}),(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>d(!1),"data-sentry-element":"Button","data-sentry-source-file":"bill-filters.tsx",children:(0,a.jsx)(es.A,{className:"h-4 w-4","data-sentry-element":"IconX","data-sentry-source-file":"bill-filters.tsx"})})]}),(0,a.jsx)(Y.Separator,{"data-sentry-element":"Separator","data-sentry-source-file":"bill-filters.tsx"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(em.J,{className:"flex items-center gap-2","data-sentry-element":"Label","data-sentry-source-file":"bill-filters.tsx",children:[(0,a.jsx)(ej.A,{className:"h-4 w-4","data-sentry-element":"IconFileText","data-sentry-source-file":"bill-filters.tsx"}),"账单状态"]}),(0,a.jsxs)(W.l6,{value:c.status||"",onValueChange:e=>p("status",e||void 0),"data-sentry-element":"Select","data-sentry-source-file":"bill-filters.tsx",children:[(0,a.jsx)(W.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"bill-filters.tsx",children:(0,a.jsx)(W.yv,{placeholder:"选择状态","data-sentry-element":"SelectValue","data-sentry-source-file":"bill-filters.tsx"})}),(0,a.jsxs)(W.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"bill-filters.tsx",children:[(0,a.jsx)(W.eb,{value:"","data-sentry-element":"SelectItem","data-sentry-source-file":"bill-filters.tsx",children:"全部状态"}),eD.map(e=>(0,a.jsx)(W.eb,{value:e.value,children:e.label},e.value))]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(em.J,{"data-sentry-element":"Label","data-sentry-source-file":"bill-filters.tsx",children:"账单类型"}),(0,a.jsxs)(W.l6,{value:c.billType||"",onValueChange:e=>p("billType",e||void 0),"data-sentry-element":"Select","data-sentry-source-file":"bill-filters.tsx",children:[(0,a.jsx)(W.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"bill-filters.tsx",children:(0,a.jsx)(W.yv,{placeholder:"选择类型","data-sentry-element":"SelectValue","data-sentry-source-file":"bill-filters.tsx"})}),(0,a.jsxs)(W.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"bill-filters.tsx",children:[(0,a.jsx)(W.eb,{value:"","data-sentry-element":"SelectItem","data-sentry-source-file":"bill-filters.tsx",children:"全部类型"}),ek.map(e=>(0,a.jsx)(W.eb,{value:e.value,children:e.label},e.value))]})]})]}),s.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(em.J,{className:"flex items-center gap-2",children:[(0,a.jsx)(eu.A,{className:"h-4 w-4"}),"患者"]}),(0,a.jsxs)(W.l6,{value:c.patientId||"",onValueChange:e=>p("patientId",e||void 0),children:[(0,a.jsx)(W.bq,{children:(0,a.jsx)(W.yv,{placeholder:"选择患者"})}),(0,a.jsxs)(W.gC,{children:[(0,a.jsx)(W.eb,{value:"",children:"全部患者"}),s.map(e=>(0,a.jsxs)(W.eb,{value:e.id,children:[e.fullName," - ",e.phone]},e.id))]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(em.J,{className:"flex items-center gap-2","data-sentry-element":"Label","data-sentry-source-file":"bill-filters.tsx",children:[(0,a.jsx)(et.A,{className:"h-4 w-4","data-sentry-element":"IconCalendar","data-sentry-source-file":"bill-filters.tsx"}),"日期范围"]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(em.J,{className:"text-xs text-muted-foreground","data-sentry-element":"Label","data-sentry-source-file":"bill-filters.tsx",children:"开始日期"}),(0,a.jsx)(J.p,{type:"date",value:c.dateFrom||"",onChange:e=>p("dateFrom",e.target.value||void 0),className:"text-sm","data-sentry-element":"Input","data-sentry-source-file":"bill-filters.tsx"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(em.J,{className:"text-xs text-muted-foreground","data-sentry-element":"Label","data-sentry-source-file":"bill-filters.tsx",children:"结束日期"}),(0,a.jsx)(J.p,{type:"date",value:c.dateTo||"",onChange:e=>p("dateTo",e.target.value||void 0),className:"text-sm","data-sentry-element":"Input","data-sentry-source-file":"bill-filters.tsx"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(em.J,{className:"flex items-center gap-2","data-sentry-element":"Label","data-sentry-source-file":"bill-filters.tsx",children:[(0,a.jsx)(eT,{className:"h-4 w-4","data-sentry-element":"IconCurrencyYuan","data-sentry-source-file":"bill-filters.tsx"}),"金额范围"]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(em.J,{className:"text-xs text-muted-foreground","data-sentry-element":"Label","data-sentry-source-file":"bill-filters.tsx",children:"最小金额"}),(0,a.jsx)(J.p,{type:"number",step:"0.01",min:"0",placeholder:"0.00",value:c.amountMin||"",onChange:e=>p("amountMin",parseFloat(e.target.value)||void 0),className:"text-sm","data-sentry-element":"Input","data-sentry-source-file":"bill-filters.tsx"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(em.J,{className:"text-xs text-muted-foreground","data-sentry-element":"Label","data-sentry-source-file":"bill-filters.tsx",children:"最大金额"}),(0,a.jsx)(J.p,{type:"number",step:"0.01",min:"0",placeholder:"无限制",value:c.amountMax||"",onChange:e=>p("amountMax",parseFloat(e.target.value)||void 0),className:"text-sm","data-sentry-element":"Input","data-sentry-source-file":"bill-filters.tsx"})]})]})]}),(0,a.jsx)(Y.Separator,{"data-sentry-element":"Separator","data-sentry-source-file":"bill-filters.tsx"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(i.$,{onClick:()=>{t(c),d(!1)},className:"flex-1","data-sentry-element":"Button","data-sentry-source-file":"bill-filters.tsx",children:"应用筛选"}),(0,a.jsx)(i.$,{variant:"outline",onClick:h,"data-sentry-element":"Button","data-sentry-source-file":"bill-filters.tsx",children:"清除"})]})]})})]})]}),u&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[e.status&&(0,a.jsxs)(l.E,{variant:"secondary",className:"flex items-center gap-1",children:["状态: ",eD.find(t=>t.value===e.status)?.label,(0,a.jsx)("button",{onClick:()=>f("status"),className:"ml-1 hover:bg-muted rounded-full p-0.5",children:(0,a.jsx)(es.A,{className:"h-3 w-3"})})]}),e.billType&&(0,a.jsxs)(l.E,{variant:"secondary",className:"flex items-center gap-1",children:["类型: ",ek.find(t=>t.value===e.billType)?.label,(0,a.jsx)("button",{onClick:()=>f("billType"),className:"ml-1 hover:bg-muted rounded-full p-0.5",children:(0,a.jsx)(es.A,{className:"h-3 w-3"})})]}),e.patientId&&(0,a.jsxs)(l.E,{variant:"secondary",className:"flex items-center gap-1",children:["患者: ",s.find(t=>t.id===e.patientId)?.fullName,(0,a.jsx)("button",{onClick:()=>f("patientId"),className:"ml-1 hover:bg-muted rounded-full p-0.5",children:(0,a.jsx)(es.A,{className:"h-3 w-3"})})]}),(e.dateFrom||e.dateTo)&&(0,a.jsxs)(l.E,{variant:"secondary",className:"flex items-center gap-1",children:["日期: ",e.dateFrom||"开始"," ~ ",e.dateTo||"结束",(0,a.jsx)("button",{onClick:()=>{f("dateFrom"),f("dateTo")},className:"ml-1 hover:bg-muted rounded-full p-0.5",children:(0,a.jsx)(es.A,{className:"h-3 w-3"})})]}),(void 0!==e.amountMin||void 0!==e.amountMax)&&(0,a.jsxs)(l.E,{variant:"secondary",className:"flex items-center gap-1",children:["金额: ",e.amountMin?T.formatCurrency(e.amountMin):"0"," ~ ",e.amountMax?T.formatCurrency(e.amountMax):"∞",(0,a.jsx)("button",{onClick:()=>{f("amountMin"),f("amountMax")},className:"ml-1 hover:bg-muted rounded-full p-0.5",children:(0,a.jsx)(es.A,{className:"h-3 w-3"})})]}),u&&(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:h,className:"h-6 px-2",children:"清除全部"})]})]})}let eP=({status:e})=>(0,a.jsx)(l.E,{className:(e=>{switch(e.toLowerCase()){case"paid":return"bg-green-100 text-green-800 hover:bg-green-200";case"pending":return"bg-yellow-100 text-yellow-800 hover:bg-yellow-200";case"partial":return"bg-blue-100 text-blue-800 hover:bg-blue-200";case"overdue":return"bg-red-100 text-red-800 hover:bg-red-200";default:return"bg-gray-100 text-gray-800 hover:bg-gray-200"}})(e),"data-sentry-element":"Badge","data-sentry-component":"StatusBadge","data-sentry-source-file":"billing-list.tsx",children:(e=>{switch(e.toLowerCase()){case"paid":return"已支付";case"pending":return"待支付";case"partial":return"部分支付";case"overdue":return"逾期";case"cancelled":return"已取消";default:return e}})(e)}),eM=({type:e})=>(0,a.jsx)(l.E,{variant:"outline",className:(e=>{switch(e.toLowerCase()){case"treatment":return"bg-purple-100 text-purple-800 hover:bg-purple-200";case"consultation":return"bg-orange-100 text-orange-800 hover:bg-orange-200";case"deposit":return"bg-cyan-100 text-cyan-800 hover:bg-cyan-200";case"additional":return"bg-pink-100 text-pink-800 hover:bg-pink-200";default:return"bg-gray-100 text-gray-800 hover:bg-gray-200"}})(e),"data-sentry-element":"Badge","data-sentry-component":"BillTypeBadge","data-sentry-source-file":"billing-list.tsx",children:(e=>{switch(e.toLowerCase()){case"treatment":return"治疗";case"consultation":return"咨询";case"deposit":return"押金";case"additional":return"附加";default:return e}})(e)});function eB(){let{hasPermission:e}=(0,o.It)(),[t,s]=(0,r.useState)([]),[n,l]=(0,r.useState)(!0),[m,g]=(0,r.useState)(null),[j,b]=(0,r.useState)({}),[v,N]=(0,r.useState)(1),[w,A]=(0,r.useState)(1),[S,R]=(0,r.useState)(!1),[D,k]=(0,r.useState)([]),[E,P]=(0,r.useState)(null),[M,B]=(0,r.useState)(!1),[F,$]=(0,r.useState)(null),[_,O]=(0,r.useState)(!1),[z,q]=(0,r.useState)(!1),L=async(e=1,t={})=>{try{l(1===e),g(null);let a=await I.fetchBills({page:e,limit:10,search:t.search||void 0,status:t.status||void 0,patientId:t.patientId||void 0,dateFrom:t.dateFrom||void 0,dateTo:t.dateTo||void 0});s(a.docs),N(a.page),A(a.totalPages)}catch(t){console.error("Failed to fetch bills:",t);let e=t instanceof C?t.message:"加载账单失败，请稍后重试。";g(e),y.toast.error(e)}finally{l(!1),R(!1)}},V=async()=>{R(!0),await L(v,j)},U=()=>{$(null),q(!0),O(!0)},J=e=>{y.toast.info(`查看账单: ${e.billNumber}`)},G=e=>{$(e),q(!1),O(!0)},W=e=>{P(e),B(!0)},H=e=>{s(t=>t.map(t=>t.id===e.id?e:t)),y.toast.success("账单状态已更新")},Z=e=>{L(e,j)};return n?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"加载账单中..."})]})}):m&&0===t.length?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(d.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-red-600 mb-2",children:"加载失败"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:m}),(0,a.jsxs)(i.$,{onClick:V,variant:"outline",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"重试"]})]})}):0===t.length?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(u,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-2",children:"暂无账单"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:"开始创建您的第一个账单。"}),(0,a.jsx)(o.Bk,{permission:"canCreateBills",children:(0,a.jsxs)(i.$,{onClick:U,children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"新建账单"]})})]})}):(0,a.jsxs)("div",{className:"space-y-4","data-sentry-component":"BillingList","data-sentry-source-file":"billing-list.tsx",children:[(0,a.jsx)(eE,{filters:j,onFiltersChange:e=>{b(e),N(1)},patients:D,"data-sentry-element":"BillFilters","data-sentry-source-file":"billing-list.tsx"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:V,disabled:S,"data-sentry-element":"Button","data-sentry-source-file":"billing-list.tsx",children:[(0,a.jsx)(c.A,{className:`h-4 w-4 mr-2 ${S?"animate-spin":""}`,"data-sentry-element":"IconRefresh","data-sentry-source-file":"billing-list.tsx"}),"刷新"]}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:["共 ",t.length," 个账单"]})]}),(0,a.jsx)(o.Bk,{permission:"canCreateBills","data-sentry-element":"PermissionGate","data-sentry-source-file":"billing-list.tsx",children:(0,a.jsxs)(i.$,{onClick:U,"data-sentry-element":"Button","data-sentry-source-file":"billing-list.tsx",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2","data-sentry-element":"IconPlus","data-sentry-source-file":"billing-list.tsx"}),"新建账单"]})})]}),m&&t.length>0&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 text-red-500 mr-2"}),(0,a.jsx)("span",{className:"text-red-700 text-sm",children:m})]})}),(0,a.jsx)("div",{className:"grid gap-4",children:t.map(e=>(0,a.jsxs)("div",{className:"border rounded-lg p-4 space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("h4",{className:"font-medium text-lg",children:e.billNumber}),(0,a.jsx)(eM,{type:e.billType}),(0,a.jsx)(eA,{bill:e,onStatusUpdate:H,trigger:(0,a.jsx)(eP,{status:e.status})})]}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:[(0,a.jsxs)("p",{children:["患者: ","object"==typeof e.patient?e.patient.fullName:"未知患者"]}),(0,a.jsxs)("p",{children:["描述: ",e.description]}),(0,a.jsxs)("p",{children:["开票日期: ",new Date(e.issueDate).toLocaleDateString("zh-CN")]})]})]}),(0,a.jsxs)("div",{className:"text-right space-y-1",children:[(0,a.jsx)("div",{className:"text-lg font-semibold",children:T.formatCurrency(e.totalAmount)}),(e.remainingAmount||0)>0&&(0,a.jsxs)("div",{className:"text-sm text-red-600",children:["待收: ",T.formatCurrency(e.remainingAmount||0)]}),(e.paidAmount||0)>0&&(0,a.jsxs)("div",{className:"text-sm text-green-600",children:["已收: ",T.formatCurrency(e.paidAmount||0)]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-2 border-t",children:[(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:["到期日期: ",new Date(e.dueDate).toLocaleDateString("zh-CN")]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>J(e),children:(0,a.jsx)(p.A,{className:"h-4 w-4"})}),(0,a.jsx)(o.Bk,{permission:"canEditBills",children:(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>G(e),children:(0,a.jsx)(h.A,{className:"h-4 w-4"})})}),(e.remainingAmount||0)>0&&(0,a.jsx)(o.Bk,{permission:"canProcessPayments",children:(0,a.jsxs)(i.$,{variant:"default",size:"sm",onClick:()=>W(e),children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-1"}),"收款"]})})]})]})]},e.id))}),w>1&&(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 pt-4",children:[(0,a.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>Z(v-1),disabled:v<=1,children:"上一页"}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["第 ",v," 页，共 ",w," 页"]}),(0,a.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>Z(v+1),disabled:v>=w,children:"下一页"})]}),(0,a.jsx)(ec,{bill:E,isOpen:M,onClose:()=>{B(!1),P(null)},onSuccess:e=>{y.toast.success(`支付处理成功！收据编号: ${e.receiptNumber||"待生成"}`),L(v,j)},"data-sentry-element":"PaymentDialog","data-sentry-source-file":"billing-list.tsx"}),(0,a.jsx)(ey,{bill:F,isOpen:_,onClose:()=>{O(!1),$(null),q(!1)},onSuccess:e=>{y.toast.success(`账单${z?"创建":"更新"}成功！账单编号: ${e.billNumber}`),L(v,j)},"data-sentry-element":"BillDialog","data-sentry-source-file":"billing-list.tsx"})]})}var eF=s(92113);function e$({onBillGenerated:e,className:t}){let{hasPermission:s}=(0,o.It)(),[n,d]=(0,r.useState)([]),[m,x]=(0,r.useState)(!0),[p,h]=(0,r.useState)(null),[f,y]=(0,r.useState)("treatment");if(!s("canCreateBills"))return(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(eN.A,{className:"h-12 w-12 text-yellow-500 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"权限不足"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"您没有权限从预约生成账单"})]})});let g=async()=>{try{x(!0);let e=await fetch("/api/appointments?status=completed&limit=50"),t=await e.json();if(!e.ok)throw Error(t.error||"Failed to fetch appointments");let s=(await I.fetchBills({limit:100})).docs,a=t.docs.map(e=>{let t=s.find(t=>"object"==typeof t.appointment&&t.appointment?.id===e.id);return{...e,hasBill:!!t,billId:t?.id,billNumber:t?.billNumber}});d(a)}catch(e){console.error("Failed to fetch appointments:",e),z.system.dataRefreshError()}finally{x(!1)}},j=async t=>{try{h(t.id);let s=await I.generateFromAppointment(t.id,f);z.bill.generateFromAppointment(s,new Date(t.appointmentDate).toLocaleDateString("zh-CN")),d(e=>e.map(e=>e.id===t.id?{...e,hasBill:!0,billId:s.id,billNumber:s.billNumber}:e)),e&&e(s)}catch(t){console.error("Failed to generate bill:",t);let e=t instanceof C?t.message:void 0;z.bill.createError(e)}finally{h(null)}},b=e=>{switch(e){case"completed":return"bg-green-100 text-green-800";case"confirmed":return"bg-blue-100 text-blue-800";case"scheduled":return"bg-yellow-100 text-yellow-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},v=e=>({scheduled:"已预约",confirmed:"已确认",completed:"已完成",cancelled:"已取消","no-show":"未到诊"})[e]||e;if(m)return(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"加载已完成预约中..."})]})});let N=n.filter(e=>!e.hasBill);return(0,a.jsxs)("div",{className:`space-y-6 ${t}`,"data-sentry-component":"AppointmentToBill","data-sentry-source-file":"appointment-to-bill.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold tracking-tight flex items-center gap-2",children:[(0,a.jsx)(u,{className:"size-6","data-sentry-element":"IconReceipt","data-sentry-source-file":"appointment-to-bill.tsx"}),"预约生成账单"]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"从已完成的预约自动生成账单"})]}),(0,a.jsxs)(i.$,{variant:"outline",onClick:g,disabled:m,"data-sentry-element":"Button","data-sentry-source-file":"appointment-to-bill.tsx",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2","data-sentry-element":"IconRefresh","data-sentry-source-file":"appointment-to-bill.tsx"}),"刷新"]})]}),(0,a.jsxs)(H.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"appointment-to-bill.tsx",children:[(0,a.jsxs)(H.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"appointment-to-bill.tsx",children:[(0,a.jsx)(H.ZB,{className:"text-lg","data-sentry-element":"CardTitle","data-sentry-source-file":"appointment-to-bill.tsx",children:"账单类型设置"}),(0,a.jsx)(H.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"appointment-to-bill.tsx",children:"选择生成账单的默认类型"})]}),(0,a.jsx)(H.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"appointment-to-bill.tsx",children:(0,a.jsxs)(W.l6,{value:f,onValueChange:y,"data-sentry-element":"Select","data-sentry-source-file":"appointment-to-bill.tsx",children:[(0,a.jsx)(W.bq,{className:"w-64","data-sentry-element":"SelectTrigger","data-sentry-source-file":"appointment-to-bill.tsx",children:(0,a.jsx)(W.yv,{"data-sentry-element":"SelectValue","data-sentry-source-file":"appointment-to-bill.tsx"})}),(0,a.jsxs)(W.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"appointment-to-bill.tsx",children:[(0,a.jsx)(W.eb,{value:"treatment","data-sentry-element":"SelectItem","data-sentry-source-file":"appointment-to-bill.tsx",children:"治疗账单"}),(0,a.jsx)(W.eb,{value:"consultation","data-sentry-element":"SelectItem","data-sentry-source-file":"appointment-to-bill.tsx",children:"咨询账单"}),(0,a.jsx)(W.eb,{value:"additional","data-sentry-element":"SelectItem","data-sentry-source-file":"appointment-to-bill.tsx",children:"补充账单"})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsx)(H.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"appointment-to-bill.tsx",children:(0,a.jsx)(H.Wu,{className:"pt-6","data-sentry-element":"CardContent","data-sentry-source-file":"appointment-to-bill.tsx",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:n.length}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"已完成预约"})]})})}),(0,a.jsx)(H.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"appointment-to-bill.tsx",children:(0,a.jsx)(H.Wu,{className:"pt-6","data-sentry-element":"CardContent","data-sentry-source-file":"appointment-to-bill.tsx",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:n.filter(e=>e.hasBill).length}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"已生成账单"})]})})}),(0,a.jsx)(H.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"appointment-to-bill.tsx",children:(0,a.jsx)(H.Wu,{className:"pt-6","data-sentry-element":"CardContent","data-sentry-source-file":"appointment-to-bill.tsx",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:N.length}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"待生成账单"})]})})})]}),0===N.length?(0,a.jsx)(H.Zp,{children:(0,a.jsx)(H.Wu,{className:"py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(ev.A,{className:"h-12 w-12 text-green-500 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-2",children:"全部预约已生成账单"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"所有已完成的预约都已生成对应的账单"})]})})}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"待生成账单的预约"}),(0,a.jsx)("div",{className:"grid gap-4",children:N.map(e=>(0,a.jsx)(H.Zp,{className:"hover:shadow-md transition-shadow",children:(0,a.jsx)(H.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-3 flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(l.E,{className:b(e.status),children:v(e.status)}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["预约ID: ",e.id]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(et.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{children:new Date(e.appointmentDate).toLocaleDateString("zh-CN")})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(eu.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{children:"object"==typeof e.patient?e.patient.fullName:"未知患者"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(eF.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{children:"object"==typeof e.treatment?e.treatment.name:"未知治疗"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(eT,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{children:T.formatCurrency(e.price||0)})]})]})]}),(0,a.jsx)("div",{className:"ml-4",children:(0,a.jsx)(i.$,{onClick:()=>j(e),disabled:p===e.id,className:"min-w-24",children:p===e.id?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"生成中..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u,{className:"h-4 w-4 mr-2"}),"生成账单"]})})})]})})},e.id))})]}),(0,a.jsxs)(eg.Fc,{"data-sentry-element":"Alert","data-sentry-source-file":"appointment-to-bill.tsx",children:[(0,a.jsx)(eN.A,{className:"h-4 w-4","data-sentry-element":"IconAlertTriangle","data-sentry-source-file":"appointment-to-bill.tsx"}),(0,a.jsx)(eg.TN,{"data-sentry-element":"AlertDescription","data-sentry-source-file":"appointment-to-bill.tsx",children:"生成的账单将包含预约的治疗项目、价格和患者信息。您可以在账单列表中进一步编辑账单详情。"})]})]})}function e_({className:e}){let{hasPermission:t}=(0,o.It)(),[s,n]=(0,r.useState)([]),[m,x]=(0,r.useState)(!0),[h,f]=(0,r.useState)(null),[g,j]=(0,r.useState)(""),[b,v]=(0,r.useState)(null),[N,w]=(0,r.useState)(!1),[A,I]=(0,r.useState)(!1),R=async e=>{try{x(!0),f(null);let t=(await S.fetchPayments({limit:50,status:"completed",search:e||void 0})).docs.filter(e=>e.receiptNumber);n(t)}catch(t){console.error("Failed to fetch payments:",t);let e=t instanceof C?t.message:"加载收据失败，请稍后重试。";f(e),y.toast.error(e)}finally{x(!1),I(!1)}},D=async()=>{I(!0),await R(g)},k=e=>{v(e),w(!0)},E=e=>{v(e),w(!0)},P=s.filter(e=>e.receiptNumber?.toLowerCase().includes(g.toLowerCase())||e.paymentNumber.toLowerCase().includes(g.toLowerCase())||"object"==typeof e.patient&&e.patient.fullName.toLowerCase().includes(g.toLowerCase()));return m?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"加载收据中..."})]})}):h&&0===s.length?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(d.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-red-600 mb-2",children:"加载失败"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:h}),(0,a.jsxs)(i.$,{onClick:D,variant:"outline",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"重试"]})]})}):(0,a.jsxs)("div",{className:`space-y-4 ${e}`,"data-sentry-component":"ReceiptManager","data-sentry-source-file":"receipt-manager.tsx",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold tracking-tight flex items-center gap-2",children:[(0,a.jsx)(u,{className:"size-6","data-sentry-element":"IconReceipt","data-sentry-source-file":"receipt-manager.tsx"}),"收据管理"]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"查看、打印和管理支付收据"})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 flex-1 max-w-md",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(eS.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4","data-sentry-element":"IconSearch","data-sentry-source-file":"receipt-manager.tsx"}),(0,a.jsx)(J.p,{placeholder:"搜索收据编号、支付编号或患者姓名...",value:g,onChange:e=>j(e.target.value),className:"pl-10","data-sentry-element":"Input","data-sentry-source-file":"receipt-manager.tsx"})]}),(0,a.jsx)(i.$,{variant:"outline",size:"sm",onClick:D,disabled:A,"data-sentry-element":"Button","data-sentry-source-file":"receipt-manager.tsx",children:(0,a.jsx)(c.A,{className:`h-4 w-4 ${A?"animate-spin":""}`,"data-sentry-element":"IconRefresh","data-sentry-source-file":"receipt-manager.tsx"})})]}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:["共 ",P.length," 张收据"]})]}),h&&s.length>0&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 text-red-500 mr-2"}),(0,a.jsx)("span",{className:"text-red-700 text-sm",children:h})]})}),0===P.length?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(u,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-2",children:"暂无收据"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:g?"没有找到匹配的收据":"还没有生成任何收据"})]})}):(0,a.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:P.map(e=>(0,a.jsxs)(H.Zp,{className:"hover:shadow-md transition-shadow",children:[(0,a.jsx)(H.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(H.ZB,{className:"text-sm font-medium",children:e.receiptNumber}),(0,a.jsxs)(H.BT,{className:"text-xs",children:["支付编号: ",e.paymentNumber]})]}),(0,a.jsx)(l.E,{variant:"completed"===e.paymentStatus?"default":"secondary",children:T.getPaymentStatusName(e.paymentStatus)})]})}),(0,a.jsxs)(H.Wu,{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"患者:"}),(0,a.jsx)("span",{children:"object"==typeof e.patient?e.patient.fullName:"未知患者"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"支付方式:"}),(0,a.jsx)("span",{children:T.getPaymentMethodName(e.paymentMethod)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"支付日期:"}),(0,a.jsx)("span",{children:new Date(e.paymentDate).toLocaleDateString("zh-CN")})]}),(0,a.jsxs)("div",{className:"flex justify-between font-medium",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"金额:"}),(0,a.jsx)("span",{className:"text-green-600",children:T.formatCurrency(e.amount)})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 pt-2 border-t",children:[(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>k(e),className:"flex-1",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-1"}),"查看"]}),(0,a.jsx)(o.Bk,{permission:"canGenerateReceipts",children:(0,a.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>E(e),children:(0,a.jsx)(en,{className:"h-4 w-4"})})})]})]})]},e.id))}),(0,a.jsx)(ei,{payment:b,isOpen:N,onClose:()=>{w(!1),v(null)},"data-sentry-element":"ReceiptDialog","data-sentry-source-file":"receipt-manager.tsx"})]})}var eO=(0,m.A)("outline","chart-bar","IconChartBar",[["path",{d:"M3 13a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v6a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z",key:"svg-0"}],["path",{d:"M15 9a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v10a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z",key:"svg-1"}],["path",{d:"M9 5a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v14a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z",key:"svg-2"}],["path",{d:"M4 20h14",key:"svg-3"}]]),ez=s(58246);function eq(){let{hasPermission:e}=(0,o.It)(),[t,s]=(0,r.useState)({dailyRevenue:null,monthlyRevenue:null,outstandingBalances:null}),[n,d]=(0,r.useState)(!0),[m,u]=(0,r.useState)(null),[x,p]=(0,r.useState)(new Date().toISOString().split("T")[0]),[h,g]=(0,r.useState)({year:new Date().getFullYear(),month:new Date().getMonth()+1}),[j,b]=(0,r.useState)(!1);if(!e("canViewDetailedFinancials"))return(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(eN.A,{className:"h-12 w-12 text-yellow-500 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"权限不足"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"您没有权限查看详细的财务报表"})]})});let v=async()=>{try{d(!0),u(null);let[e,t,a]=await Promise.all([R.getDailyRevenue(x).catch(()=>null),R.getMonthlyRevenue(h.year,h.month).catch(()=>null),R.getOutstandingBalances().catch(()=>null)]);s({dailyRevenue:e,monthlyRevenue:t,outstandingBalances:a})}catch(t){console.error("Failed to fetch financial metrics:",t);let e=t instanceof C?t.message:"加载财务数据失败，请稍后重试";u(e),y.toast.error(e)}finally{d(!1),b(!1)}},N=e=>{switch(e){case"cash":default:return X;case"card":return f.A;case"wechat":case"alipay":return K;case"transfer":return ee}};return n?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"加载财务数据中..."})]})}):(0,a.jsxs)("div",{className:"space-y-6","data-sentry-component":"FinancialDashboard","data-sentry-source-file":"financial-dashboard.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold tracking-tight flex items-center gap-2",children:[(0,a.jsx)(eO,{className:"size-6","data-sentry-element":"IconChartBar","data-sentry-source-file":"financial-dashboard.tsx"}),"财务报表"]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"查看收入统计、支付分析和应收账款"})]}),(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>{b(!0),v()},disabled:j,"data-sentry-element":"Button","data-sentry-source-file":"financial-dashboard.tsx",children:[(0,a.jsx)(c.A,{className:`h-4 w-4 mr-2 ${j?"animate-spin":""}`,"data-sentry-element":"IconRefresh","data-sentry-source-file":"financial-dashboard.tsx"}),"刷新数据"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(et.A,{className:"h-4 w-4","data-sentry-element":"IconCalendar","data-sentry-source-file":"financial-dashboard.tsx"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"日期选择:"}),(0,a.jsx)("input",{type:"date",value:x,onChange:e=>p(e.target.value),className:"px-3 py-1 border rounded-md text-sm"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"月份选择:"}),(0,a.jsxs)(W.l6,{value:`${h.year}-${h.month}`,onValueChange:e=>{let[t,s]=e.split("-").map(Number);g({year:t,month:s})},"data-sentry-element":"Select","data-sentry-source-file":"financial-dashboard.tsx",children:[(0,a.jsx)(W.bq,{className:"w-40","data-sentry-element":"SelectTrigger","data-sentry-source-file":"financial-dashboard.tsx",children:(0,a.jsx)(W.yv,{"data-sentry-element":"SelectValue","data-sentry-source-file":"financial-dashboard.tsx"})}),(0,a.jsx)(W.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"financial-dashboard.tsx",children:Array.from({length:12},(e,t)=>{let s=t+1,r=new Date().getFullYear();return(0,a.jsxs)(W.eb,{value:`${r}-${s}`,children:[r,"年",s,"月"]},`${r}-${s}`)})})]})]})]}),m&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(eN.A,{className:"h-4 w-4 text-red-500 mr-2"}),(0,a.jsx)("span",{className:"text-red-700 text-sm",children:m})]})}),(0,a.jsxs)(H.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"financial-dashboard.tsx",children:[(0,a.jsxs)(H.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"financial-dashboard.tsx",children:[(0,a.jsxs)(H.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"financial-dashboard.tsx",children:[(0,a.jsx)(ez.A,{className:"h-5 w-5","data-sentry-element":"IconTrendingUp","data-sentry-source-file":"financial-dashboard.tsx"}),"日收入统计"]}),(0,a.jsxs)(H.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"financial-dashboard.tsx",children:[new Date(x).toLocaleDateString("zh-CN")," 的收入详情"]})]}),(0,a.jsx)(H.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"financial-dashboard.tsx",children:t.dailyRevenue?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:T.formatCurrency(t.dailyRevenue.totalRevenue)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"总收入"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:t.dailyRevenue.paymentCount}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"支付笔数"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:t.dailyRevenue.paymentCount>0?T.formatCurrency(t.dailyRevenue.totalRevenue/t.dailyRevenue.paymentCount):T.formatCurrency(0)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"平均金额"})]})]}),(0,a.jsx)(Y.Separator,{}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"支付方式分布"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:Object.entries(t.dailyRevenue.paymentMethods).map(([e,t])=>{let s=N(e);return(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-muted/50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(s,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:T.getPaymentMethodName(e)})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-sm font-semibold",children:T.formatCurrency(t.amount)}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:[t.count," 笔"]})]})]},e)})})]})]}):(0,a.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:"暂无当日收入数据"})})]}),(0,a.jsxs)(H.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"financial-dashboard.tsx",children:[(0,a.jsxs)(H.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"financial-dashboard.tsx",children:[(0,a.jsxs)(H.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"financial-dashboard.tsx",children:[(0,a.jsx)(eO,{className:"h-5 w-5","data-sentry-element":"IconChartBar","data-sentry-source-file":"financial-dashboard.tsx"}),"月度收入统计"]}),(0,a.jsxs)(H.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"financial-dashboard.tsx",children:[h.year,"年",h.month,"月的收入趋势"]})]}),(0,a.jsx)(H.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"financial-dashboard.tsx",children:t.monthlyRevenue?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-green-600",children:T.formatCurrency(t.monthlyRevenue.totalRevenue)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"月度总收入"})]}),(0,a.jsx)(Y.Separator,{}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"每日收入明细"}),(0,a.jsx)("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:t.monthlyRevenue.dailyBreakdown.map(e=>(0,a.jsxs)("div",{className:"flex justify-between items-center p-2 hover:bg-muted/50 rounded",children:[(0,a.jsx)("span",{className:"text-sm",children:new Date(e.date).toLocaleDateString("zh-CN")}),(0,a.jsx)("span",{className:"font-medium",children:T.formatCurrency(e.revenue)})]},e.date))})]})]}):(0,a.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:"暂无月度收入数据"})})]}),(0,a.jsxs)(H.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"financial-dashboard.tsx",children:[(0,a.jsxs)(H.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"financial-dashboard.tsx",children:[(0,a.jsxs)(H.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"financial-dashboard.tsx",children:[(0,a.jsx)(eN.A,{className:"h-5 w-5","data-sentry-element":"IconAlertTriangle","data-sentry-source-file":"financial-dashboard.tsx"}),"应收账款"]}),(0,a.jsx)(H.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"financial-dashboard.tsx",children:"待收款项和逾期账单统计"})]}),(0,a.jsx)(H.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"financial-dashboard.tsx",children:t.outstandingBalances?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:T.formatCurrency(t.outstandingBalances.totalOutstanding)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"总待收金额"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:T.formatCurrency(t.outstandingBalances.overdueAmount)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"逾期金额"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:t.outstandingBalances.billsCount}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"待收账单"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:t.outstandingBalances.overdueBillsCount}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"逾期账单"})]})]}),t.outstandingBalances.bills.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(Y.Separator,{}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"逾期账单详情"}),(0,a.jsx)("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:t.outstandingBalances.bills.filter(e=>e.daysOverdue>0).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-sm",children:e.billNumber}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.patient})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"font-semibold text-red-600",children:T.formatCurrency(e.amount)}),(0,a.jsxs)(l.E,{variant:"destructive",className:"text-xs",children:["逾期 ",e.daysOverdue," 天"]})]})]},e.id))})]})]})]}):(0,a.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:"暂无应收账款数据"})})]})]})}var eL=(0,m.A)("outline","calendar-event","IconCalendarEvent",[["path",{d:"M4 5m0 2a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2z",key:"svg-0"}],["path",{d:"M16 3l0 4",key:"svg-1"}],["path",{d:"M8 3l0 4",key:"svg-2"}],["path",{d:"M4 11l16 0",key:"svg-3"}],["path",{d:"M8 15h2v2h-2z",key:"svg-4"}]]);function eV({defaultTab:e="bills",className:t}){let{hasPermission:s}=(0,o.It)(),[l,i]=(0,r.useState)(e);return(0,a.jsx)("div",{className:t,"data-sentry-component":"BillingTabs","data-sentry-source-file":"billing-tabs.tsx",children:(0,a.jsxs)(n.tU,{value:l,onValueChange:i,className:"space-y-6","data-sentry-element":"Tabs","data-sentry-source-file":"billing-tabs.tsx",children:[(0,a.jsxs)(n.j7,{className:"grid w-full grid-cols-2 lg:grid-cols-4","data-sentry-element":"TabsList","data-sentry-source-file":"billing-tabs.tsx",children:[(0,a.jsxs)(n.Xi,{value:"bills",className:"flex items-center gap-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"billing-tabs.tsx",children:[(0,a.jsx)(u,{className:"h-4 w-4","data-sentry-element":"IconReceipt","data-sentry-source-file":"billing-tabs.tsx"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"账单管理"}),(0,a.jsx)("span",{className:"sm:hidden",children:"账单"})]}),(0,a.jsx)(o.Bk,{permission:"canCreateBills","data-sentry-element":"PermissionGate","data-sentry-source-file":"billing-tabs.tsx",children:(0,a.jsxs)(n.Xi,{value:"generate",className:"flex items-center gap-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"billing-tabs.tsx",children:[(0,a.jsx)(eL,{className:"h-4 w-4","data-sentry-element":"IconCalendarEvent","data-sentry-source-file":"billing-tabs.tsx"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"预约生成"}),(0,a.jsx)("span",{className:"sm:hidden",children:"生成"})]})}),(0,a.jsx)(o.Bk,{permission:"canGenerateReceipts","data-sentry-element":"PermissionGate","data-sentry-source-file":"billing-tabs.tsx",children:(0,a.jsxs)(n.Xi,{value:"receipts",className:"flex items-center gap-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"billing-tabs.tsx",children:[(0,a.jsx)(ej.A,{className:"h-4 w-4","data-sentry-element":"IconFileText","data-sentry-source-file":"billing-tabs.tsx"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"收据管理"}),(0,a.jsx)("span",{className:"sm:hidden",children:"收据"})]})}),(0,a.jsx)(o.Bk,{permission:"canViewDetailedFinancials","data-sentry-element":"PermissionGate","data-sentry-source-file":"billing-tabs.tsx",children:(0,a.jsxs)(n.Xi,{value:"reports",className:"flex items-center gap-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"billing-tabs.tsx",children:[(0,a.jsx)(eO,{className:"h-4 w-4","data-sentry-element":"IconChartBar","data-sentry-source-file":"billing-tabs.tsx"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"财务报表"}),(0,a.jsx)("span",{className:"sm:hidden",children:"报表"})]})})]}),(0,a.jsxs)(n.av,{value:"bills",className:"space-y-6","data-sentry-element":"TabsContent","data-sentry-source-file":"billing-tabs.tsx",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold tracking-tight mb-2",children:"账单管理"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-6",children:"管理所有账单，处理支付和查看账单状态"})]}),(0,a.jsx)(eB,{"data-sentry-element":"BillingList","data-sentry-source-file":"billing-tabs.tsx"})]}),(0,a.jsx)(o.Bk,{permission:"canCreateBills","data-sentry-element":"PermissionGate","data-sentry-source-file":"billing-tabs.tsx",children:(0,a.jsx)(n.av,{value:"generate",className:"space-y-6","data-sentry-element":"TabsContent","data-sentry-source-file":"billing-tabs.tsx",children:(0,a.jsx)(e$,{onBillGenerated:e=>{y.toast.success(`账单生成成功！账单编号: ${e.billNumber}`),i("bills")},"data-sentry-element":"AppointmentToBill","data-sentry-source-file":"billing-tabs.tsx"})})}),(0,a.jsx)(o.Bk,{permission:"canGenerateReceipts","data-sentry-element":"PermissionGate","data-sentry-source-file":"billing-tabs.tsx",children:(0,a.jsx)(n.av,{value:"receipts",className:"space-y-6","data-sentry-element":"TabsContent","data-sentry-source-file":"billing-tabs.tsx",children:(0,a.jsx)(e_,{"data-sentry-element":"ReceiptManager","data-sentry-source-file":"billing-tabs.tsx"})})}),(0,a.jsx)(o.Bk,{permission:"canViewDetailedFinancials","data-sentry-element":"PermissionGate","data-sentry-source-file":"billing-tabs.tsx",children:(0,a.jsx)(n.av,{value:"reports",className:"space-y-6","data-sentry-element":"TabsContent","data-sentry-source-file":"billing-tabs.tsx",children:(0,a.jsx)(eq,{"data-sentry-element":"FinancialDashboard","data-sentry-source-file":"billing-tabs.tsx"})})})]})})}},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},81646:(e,t,s)=>{"use strict";s.d(t,{T:()=>n});var a=s(24443);s(60222);var r=s(72595);function n({className:e,...t}){return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t,"data-sentry-component":"Textarea","data-sentry-source-file":"textarea.tsx"})}},83829:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var a=s(78869);s(22576);var r=s(89371);function n({children:e,scrollable:t=!0}){return(0,a.jsx)(a.Fragment,{children:t?(0,a.jsx)(r.ScrollArea,{className:"h-[calc(100dvh-52px)]",children:(0,a.jsx)("div",{className:"flex flex-1 p-4 md:px-6",children:e})}):(0,a.jsx)("div",{className:"flex flex-1 p-4 md:px-6",children:e})})}},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},89371:(e,t,s)=>{"use strict";s.d(t,{ScrollArea:()=>r});var a=s(91611);let r=(0,a.registerClientReference)(function(){throw Error("Attempted to call ScrollArea() from the server but ScrollArea is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\scroll-area.tsx","ScrollArea");(0,a.registerClientReference)(function(){throw Error("Attempted to call ScrollBar() from the server but ScrollBar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\scroll-area.tsx","ScrollBar")},91476:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>i,TN:()=>d,XL:()=>o});var a=s(24443);s(60222);var r=s(29693),n=s(72595);let l=(0,r.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,...s}){return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(l({variant:t}),e),...s,"data-sentry-component":"Alert","data-sentry-source-file":"alert.tsx"})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"alert-title",className:(0,n.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",e),...t,"data-sentry-component":"AlertTitle","data-sentry-source-file":"alert.tsx"})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t,"data-sentry-component":"AlertDescription","data-sentry-source-file":"alert.tsx"})}},94735:e=>{"use strict";e.exports=require("events")},98487:(e,t,s)=>{"use strict";s.d(t,{UC:()=>U,ZL:()=>V,bL:()=>q,l9:()=>L});var a=s(60222),r=s(12772),n=s(24368),l=s(4684),i=s(12795),o=s(88860),d=s(71663),c=s(31354),m=s(22207),u=s(84629),x=s(49258),p=s(24582),h=s(16586),f=s(36612),y=s(2064),g=s(30992),j=s(24443),b="Popover",[v,N]=(0,l.A)(b,[m.Bk]),w=(0,m.Bk)(),[C,A]=v(b),I=e=>{let{__scopePopover:t,children:s,open:r,defaultOpen:n,onOpenChange:l,modal:i=!1}=e,o=w(t),d=a.useRef(null),[u,x]=a.useState(!1),[p=!1,h]=(0,f.i)({prop:r,defaultProp:n,onChange:l});return(0,j.jsx)(m.bL,{...o,children:(0,j.jsx)(C,{scope:t,contentId:(0,c.B)(),triggerRef:d,open:p,onOpenChange:h,onOpenToggle:a.useCallback(()=>h(e=>!e),[h]),hasCustomAnchor:u,onCustomAnchorAdd:a.useCallback(()=>x(!0),[]),onCustomAnchorRemove:a.useCallback(()=>x(!1),[]),modal:i,children:s})})};I.displayName=b;var S="PopoverAnchor";a.forwardRef((e,t)=>{let{__scopePopover:s,...r}=e,n=A(S,s),l=w(s),{onCustomAnchorAdd:i,onCustomAnchorRemove:o}=n;return a.useEffect(()=>(i(),()=>o()),[i,o]),(0,j.jsx)(m.Mz,{...l,...r,ref:t})}).displayName=S;var R="PopoverTrigger",T=a.forwardRef((e,t)=>{let{__scopePopover:s,...a}=e,l=A(R,s),i=w(s),o=(0,n.s)(t,l.triggerRef),d=(0,j.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":z(l.open),...a,ref:o,onClick:(0,r.m)(e.onClick,l.onOpenToggle)});return l.hasCustomAnchor?d:(0,j.jsx)(m.Mz,{asChild:!0,...i,children:d})});T.displayName=R;var D="PopoverPortal",[k,E]=v(D,{forceMount:void 0}),P=e=>{let{__scopePopover:t,forceMount:s,children:a,container:r}=e,n=A(D,t);return(0,j.jsx)(k,{scope:t,forceMount:s,children:(0,j.jsx)(x.C,{present:s||n.open,children:(0,j.jsx)(u.Z,{asChild:!0,container:r,children:a})})})};P.displayName=D;var M="PopoverContent",B=a.forwardRef((e,t)=>{let s=E(M,e.__scopePopover),{forceMount:a=s.forceMount,...r}=e,n=A(M,e.__scopePopover);return(0,j.jsx)(x.C,{present:a||n.open,children:n.modal?(0,j.jsx)(F,{...r,ref:t}):(0,j.jsx)($,{...r,ref:t})})});B.displayName=M;var F=a.forwardRef((e,t)=>{let s=A(M,e.__scopePopover),l=a.useRef(null),i=(0,n.s)(t,l),o=a.useRef(!1);return a.useEffect(()=>{let e=l.current;if(e)return(0,y.Eq)(e)},[]),(0,j.jsx)(g.A,{as:h.DX,allowPinchZoom:!0,children:(0,j.jsx)(_,{...e,ref:i,trapFocus:s.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),o.current||s.triggerRef.current?.focus()}),onPointerDownOutside:(0,r.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,s=0===t.button&&!0===t.ctrlKey;o.current=2===t.button||s},{checkForDefaultPrevented:!1}),onFocusOutside:(0,r.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),$=a.forwardRef((e,t)=>{let s=A(M,e.__scopePopover),r=a.useRef(!1),n=a.useRef(!1);return(0,j.jsx)(_,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(r.current||s.triggerRef.current?.focus(),t.preventDefault()),r.current=!1,n.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(n.current=!0));let a=t.target;s.triggerRef.current?.contains(a)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&n.current&&t.preventDefault()}})}),_=a.forwardRef((e,t)=>{let{__scopePopover:s,trapFocus:a,onOpenAutoFocus:r,onCloseAutoFocus:n,disableOutsidePointerEvents:l,onEscapeKeyDown:c,onPointerDownOutside:u,onFocusOutside:x,onInteractOutside:p,...h}=e,f=A(M,s),y=w(s);return(0,o.Oh)(),(0,j.jsx)(d.n,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:r,onUnmountAutoFocus:n,children:(0,j.jsx)(i.qW,{asChild:!0,disableOutsidePointerEvents:l,onInteractOutside:p,onEscapeKeyDown:c,onPointerDownOutside:u,onFocusOutside:x,onDismiss:()=>f.onOpenChange(!1),children:(0,j.jsx)(m.UC,{"data-state":z(f.open),role:"dialog",id:f.contentId,...y,...h,ref:t,style:{...h.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),O="PopoverClose";function z(e){return e?"open":"closed"}a.forwardRef((e,t)=>{let{__scopePopover:s,...a}=e,n=A(O,s);return(0,j.jsx)(p.sG.button,{type:"button",...a,ref:t,onClick:(0,r.m)(e.onClick,()=>n.onOpenChange(!1))})}).displayName=O,a.forwardRef((e,t)=>{let{__scopePopover:s,...a}=e,r=w(s);return(0,j.jsx)(m.i3,{...r,...a,ref:t})}).displayName="PopoverArrow";var q=I,L=T,V=P,U=B}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[55,3738,1950,5886,9615,7927,6451,5618,2584,9616,4144,4889,3875,395,8774,7494,5392],()=>s(19422));module.exports=a})();
//# sourceMappingURL=page.js.map