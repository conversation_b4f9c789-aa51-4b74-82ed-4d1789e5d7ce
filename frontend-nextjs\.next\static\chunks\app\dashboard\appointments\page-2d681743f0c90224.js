try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="d6f3c412-52ee-445f-88c5-84e9b63c4733",e._sentryDebugIdIdentifier="sentry-dbid-d6f3c412-52ee-445f-88c5-84e9b63c4733")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8876],{13946:(e,t,n)=>{Promise.resolve().then(n.bind(n,41968)),Promise.resolve().then(n.bind(n,90917))},40153:(e,t,n)=>{"use strict";n.d(t,{S:()=>l});var a=n(52880);n(99004);var s=n(14218),i=n(90502),r=n(54651);function l(e){let{className:t,...n}=e;return(0,a.jsx)(s.bL,{"data-slot":"checkbox",className:(0,r.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...n,"data-sentry-element":"CheckboxPrimitive.Root","data-sentry-component":"Checkbox","data-sentry-source-file":"checkbox.tsx",children:(0,a.jsx)(s.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none","data-sentry-element":"CheckboxPrimitive.Indicator","data-sentry-source-file":"checkbox.tsx",children:(0,a.jsx)(i.A,{className:"size-3.5","data-sentry-element":"CheckIcon","data-sentry-source-file":"checkbox.tsx"})})})}},41968:(e,t,n)=>{"use strict";n.d(t,{AppointmentsList:()=>eC});var a=n(52880),s=n(99004),i=n(88151),r=n(62054),l=n(29980),o=n(60382),d=n(68046),c=n(26368),m=n(93900),u=n(72486),p=n(66444),x=n(57638),h=n(87378),f=n(4056),g=n(76143),y=n(29648),j=n(38406),v=n(90290),b=n(73259),w=n(13533),N=n(78912),C=n(77362),D=n(56420),S=n(42094),T=n(45450),k=n(34901),A=n(39372),I=n(54651),F=n(4629);function M(e,t){return e.start<t.end&&t.start<e.end}function P(e,t){let n=new Date(e),a=new Date(n.getTime()+60*t*1e3);return{start:n,end:a}}function R(e){return e.toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0})}function z(e,t,n,a){let s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{start:9,end:17},i=[],r=a.filter(n=>{if(n.practitioner.id!==t||"cancelled"===n.status)return!1;let a=new Date(n.appointmentDate);return a.getFullYear()===e.getFullYear()&&a.getMonth()===e.getMonth()&&a.getDate()===e.getDate()});r.sort((e,t)=>new Date(e.appointmentDate).getTime()-new Date(t.appointmentDate).getTime());let l=new Date(e);l.setHours(s.start,0,0,0);let o=new Date(e);o.setHours(s.end,0,0,0);let d=new Date(l);for(;d.getTime()+60*n*1e3<=o.getTime();){let e=new Date(d.getTime()+60*n*1e3),t={start:new Date(d),end:e};r.some(e=>M(t,P(new Date(e.appointmentDate),e.durationInMinutes)))||i.push(t),d.setMinutes(d.getMinutes()+15)}return i}var E=n(99544),V=n(19892),$=n(30821);function B(e){return(0,w.GP)(e,"yyyy年MM月dd日 HH:mm",{locale:$.g})}let L={month:"月视图",week:"周视图",work_week:"工作周",day:"日视图",agenda:"议程",today:"今天",previous:"上一个",next:"下一个",showMore:e=>"+".concat(e," 更多")},_={appointment:{created:e=>{let t="consultation"===e.appointmentType?"咨询预约":"治疗预约";F.toast.success("".concat(t,"创建成功！"),{description:"患者: ".concat(e.patient.fullName," | 时间: ").concat(B(new Date(e.appointmentDate))),duration:4e3})},updated:e=>{let t="consultation"===e.appointmentType?"咨询预约":"治疗预约";F.toast.success("".concat(t,"更新成功！"),{description:"患者: ".concat(e.patient.fullName," | 时间: ").concat(B(new Date(e.appointmentDate))),duration:4e3})},deleted:e=>{F.toast.success("预约删除成功！",{description:"患者: ".concat(e),duration:4e3})},statusChanged:(e,t,n)=>{let a={scheduled:"已预约",confirmed:"已确认",completed:"已完成",cancelled:"已取消","no-show":"未到诊"},s=a[t]||t,i=a[n]||n;F.toast.success("预约状态更新成功！",{description:"".concat(e.patient.fullName,": ").concat(s," → ").concat(i),duration:4e3})},batchStatusChanged:(e,t)=>{F.toast.success("批量状态更新成功！",{description:"".concat(e," 个预约已更新为 ").concat({confirmed:"已确认",completed:"已完成",cancelled:"已取消"}[t]||t),duration:4e3})}},reminder:{upcoming:(e,t)=>{let n=t<60?"".concat(t," 分钟后"):"".concat(Math.floor(t/60)," 小时后");F.toast.info("预约提醒",{description:"".concat(e.patient.fullName," 的预约将在 ").concat(n," 开始"),duration:8e3})},overdue:e=>{F.toast.error("预约超时",{description:"".concat(e.patient.fullName," 的预约时间已过，状态为: ").concat(e.status),duration:1e4})},reminderSet:(e,t)=>{F.toast.success("预约提醒已设置",{description:"将在 ".concat(t," 提醒 ").concat(e.patient.fullName," 的预约"),duration:4e3})}},error:{fetchFailed:()=>{F.toast.error("获取预约数据失败",{description:"请检查网络连接后重试",duration:5e3})},saveFailed:e=>{F.toast.error("".concat(e?"更新":"创建","预约失败"),{description:"请稍后重试或联系管理员",duration:5e3})},deleteFailed:()=>{F.toast.error("删除预约失败",{description:"请稍后重试或联系管理员",duration:5e3})},permissionDenied:e=>{F.toast.error("权限不足",{description:"您没有权限执行: ".concat(e),duration:5e3})},networkError:()=>{F.toast.error("网络连接失败",{description:"请检查网络连接后重试",duration:5e3})}}},H=b.Ik({appointmentType:b.k5(["consultation","treatment"],{required_error:"Please select appointment type"}),appointmentDate:b.p6({required_error:"Please select an appointment date and time"}).refine(e=>e>=new Date(new Date().getTime()-864e5),{message:"Appointment date cannot be more than 24 hours in the past"}),patientId:b.Yj().min(1,"Please select a patient from the dropdown"),treatmentId:b.Yj().optional(),practitionerId:b.Yj().min(1,"Please select a practitioner from the dropdown"),price:b.ai().min(0,"Price must be $0 or greater").max(1e4,"Price cannot exceed $10,000").multipleOf(.01,"Price must be a valid currency amount").optional(),durationInMinutes:b.ai().min(5,"Duration must be at least 5 minutes").max(480,"Duration cannot exceed 8 hours (480 minutes)").int("Duration must be a whole number of minutes"),status:b.k5(["scheduled","confirmed","completed","cancelled","no-show"],{required_error:"Please select an appointment status"}),consultationType:b.k5(["initial","follow-up","price-inquiry"]).optional(),interestedTreatments:b.YO(b.Yj()).optional()}).refine(e=>"treatment"!==e.appointmentType||!!e.treatmentId,{message:"Treatment appointments must have a treatment selected",path:["treatmentId"]});function G(e){var t,n;let{open:i,onOpenChange:l,appointment:o,onSuccess:d}=e,[c,m]=(0,s.useState)(!1),[u,p]=(0,s.useState)([]),[x,f]=(0,s.useState)([]),[g,y]=(0,s.useState)([]),[b,E]=(0,s.useState)([]),[V,$]=(0,s.useState)(null),[B,L]=(0,s.useState)([]),G=!!o,O=(0,j.mN)({resolver:(0,v.u)(H),defaultValues:{appointmentType:(null==o?void 0:o.appointmentType)||"consultation",appointmentDate:o?new Date(o.appointmentDate):new Date,patientId:(null==o?void 0:o.patient.id)||"",treatmentId:(null==o||null==(t=o.treatment)?void 0:t.id)||"",practitionerId:(null==o?void 0:o.practitioner.id)||"",price:(null==o?void 0:o.price)||0,durationInMinutes:(null==o?void 0:o.durationInMinutes)||30,status:(null==o?void 0:o.status)||"scheduled",consultationType:(null==o?void 0:o.consultationType)||"initial",interestedTreatments:(null==o||null==(n=o.interestedTreatments)?void 0:n.map(e=>e.id))||[]}});(0,s.useEffect)(()=>{if(i&&(J(),o)){var e;O.reset({appointmentDate:new Date(o.appointmentDate),patientId:o.patient.id,treatmentId:(null==(e=o.treatment)?void 0:e.id)||"",practitionerId:o.practitioner.id,price:o.price,durationInMinutes:o.durationInMinutes,status:o.status})}},[i,o,O]);let J=async()=>{try{let[e,t,n]=await Promise.all([h.fJ.getAll({limit:100}),h._M.getAll({limit:100}),h.RG.getAll({limit:1e3})]);p(e.docs),f(t.docs),E(n.docs),y([{id:"user-1",email:"<EMAIL>",role:"doctor",clerkId:"clerk-1",firstName:"Dr. Jane",lastName:"Smith",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}])}catch(e){console.error("Failed to load form data:",e),F.toast.error("Failed to load form data")}},q=O.watch("treatmentId");(0,s.useEffect)(()=>{if(q&&!G){let e=x.find(e=>e.id===q);e&&(O.setValue("price",e.defaultPrice),O.setValue("durationInMinutes",e.defaultDurationInMinutes))}},[q,x,O,G]);let Y=O.watch("appointmentDate"),W=O.watch("durationInMinutes"),K=O.watch("practitionerId");(0,s.useEffect)(()=>{if(Y&&W&&K&&b.length>0){let e=function(e){let t=new Date,n=new Date(e);if(n<new Date(t.getTime()-36e5))return{isValid:!1,message:"Appointments cannot be scheduled more than 1 hour in the past."};if(n>new Date(t.getTime()+31536e6))return{isValid:!1,message:"Appointments cannot be scheduled more than 1 year in advance."};let a=n.getHours();if(a<9||a>=17)return{isValid:!1,message:"Appointments must be scheduled during business hours (9 AM to 5 PM)."};let s=n.getDay();return 0===s||6===s?{isValid:!1,message:"Appointments cannot be scheduled on weekends."}:{isValid:!0}}(Y);if(!e.isValid){$(e.message||"Invalid appointment time"),L([]);return}let t=function(e,t){let n=P(e.appointmentDate,e.durationInMinutes);for(let a of t.filter(t=>{if(e.id&&t.id===e.id||t.practitioner.id!==e.practitionerId||"cancelled"===t.status)return!1;let n=new Date(t.appointmentDate),a=e.appointmentDate;return n.getFullYear()===a.getFullYear()&&n.getMonth()===a.getMonth()&&n.getDate()===a.getDate()})){let e=P(new Date(a.appointmentDate),a.durationInMinutes);if(M(n,e))return{hasConflict:!0,conflictingAppointment:a,message:"This time slot conflicts with an existing appointment for ".concat(a.patient.fullName," from ").concat(R(e.start)," to ").concat(R(e.end),".")}}return{hasConflict:!1}}({appointmentDate:Y,durationInMinutes:W,practitionerId:K,id:null==o?void 0:o.id},b);t.hasConflict?($(t.message||"检测到时间段冲突"),L(function(e,t,n,a){let s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:5,i=[],r=z(e,t,n,a);if(i.push(...r.slice(0,s)),i.length<s)for(let r=1;r<=7&&i.length<s;r++){let l=new Date(e);if(l.setDate(l.getDate()+r),0===l.getDay()||6===l.getDay())continue;let o=z(l,t,n,a),d=s-i.length;i.push(...o.slice(0,d))}return i}(Y,K,W,b,3))):($(null),L([]))}else $(null),L([])},[Y,W,K,b,null==o?void 0:o.id]);let U=async e=>{m(!0);try{let t,n={appointmentType:e.appointmentType,appointmentDate:e.appointmentDate.toISOString(),patient:e.patientId,treatment:e.treatmentId||void 0,practitioner:e.practitionerId,price:e.price||0,durationInMinutes:e.durationInMinutes,status:e.status,consultationType:"consultation"===e.appointmentType?e.consultationType:void 0,interestedTreatments:"consultation"===e.appointmentType?e.interestedTreatments:void 0};G?(t=await h.RG.update(o.id,n),_.appointment.updated(t)):(t=await h.RG.create(n),_.appointment.created(t)),null==d||d(),l(!1),O.reset()}catch(e){console.error("Failed to save appointment:",e),_.error.saveFailed(G)}finally{m(!1)}};return(0,a.jsx)(C.lG,{open:i,onOpenChange:l,"data-sentry-element":"Dialog","data-sentry-component":"AppointmentFormDialog","data-sentry-source-file":"appointment-form-dialog.tsx",children:(0,a.jsxs)(C.Cf,{className:"sm:max-w-[600px]","data-sentry-element":"DialogContent","data-sentry-source-file":"appointment-form-dialog.tsx",children:[(0,a.jsxs)(C.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"appointment-form-dialog.tsx",children:[(0,a.jsx)(C.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"appointment-form-dialog.tsx",children:G?"Edit Appointment":"New Appointment"}),(0,a.jsx)(C.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"appointment-form-dialog.tsx",children:G?"Update the appointment details below.":"Fill in the details to schedule a new appointment."})]}),(0,a.jsx)(D.lV,{...O,"data-sentry-element":"Form","data-sentry-source-file":"appointment-form-dialog.tsx",children:(0,a.jsxs)("form",{onSubmit:O.handleSubmit(U),className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(D.zB,{control:O.control,name:"patientId",render:e=>{let{field:t}=e;return(0,a.jsxs)(D.eI,{children:[(0,a.jsx)(D.lR,{children:"Patient"}),(0,a.jsxs)(T.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,a.jsx)(D.MJ,{children:(0,a.jsx)(T.bq,{children:(0,a.jsx)(T.yv,{placeholder:"Select patient"})})}),(0,a.jsx)(T.gC,{children:u.map(e=>(0,a.jsx)(T.eb,{value:e.id,children:e.fullName},e.id))})]}),(0,a.jsx)(D.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"appointment-form-dialog.tsx"}),(0,a.jsx)(D.zB,{control:O.control,name:"treatmentId",render:e=>{let{field:t}=e;return(0,a.jsxs)(D.eI,{children:[(0,a.jsx)(D.lR,{children:"Treatment"}),(0,a.jsxs)(T.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,a.jsx)(D.MJ,{children:(0,a.jsx)(T.bq,{children:(0,a.jsx)(T.yv,{placeholder:"Select treatment"})})}),(0,a.jsx)(T.gC,{children:x.map(e=>(0,a.jsx)(T.eb,{value:e.id,children:e.name},e.id))})]}),(0,a.jsx)(D.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"appointment-form-dialog.tsx"})]}),(0,a.jsx)(D.zB,{control:O.control,name:"appointmentDate",render:e=>{let{field:t}=e;return(0,a.jsxs)(D.eI,{className:"flex flex-col",children:[(0,a.jsx)(D.lR,{children:"Appointment Date & Time"}),(0,a.jsxs)(k.AM,{children:[(0,a.jsx)(k.Wv,{asChild:!0,children:(0,a.jsx)(D.MJ,{children:(0,a.jsxs)(r.$,{variant:"outline",className:(0,I.cn)("w-full pl-3 text-left font-normal",!t.value&&"text-muted-foreground"),children:[t.value?(0,w.GP)(t.value,"PPP p"):(0,a.jsx)("span",{children:"Pick a date and time"}),(0,a.jsx)(N.A,{className:"ml-auto h-4 w-4 opacity-50"})]})})}),(0,a.jsx)(k.hl,{className:"w-auto p-0",align:"start",children:(0,a.jsx)(A.V,{mode:"single",selected:t.value,onSelect:t.onChange,disabled:e=>e<new Date||e<new Date("1900-01-01"),initialFocus:!0})})]}),(0,a.jsx)(D.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"appointment-form-dialog.tsx"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsx)(D.zB,{control:O.control,name:"price",render:e=>{let{field:t}=e;return(0,a.jsxs)(D.eI,{children:[(0,a.jsx)(D.lR,{children:"Price ($)"}),(0,a.jsx)(D.MJ,{children:(0,a.jsx)(S.p,{type:"number",step:"0.01",...t,onChange:e=>t.onChange(parseFloat(e.target.value)||0)})}),(0,a.jsx)(D.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"appointment-form-dialog.tsx"}),(0,a.jsx)(D.zB,{control:O.control,name:"durationInMinutes",render:e=>{let{field:t}=e;return(0,a.jsxs)(D.eI,{children:[(0,a.jsx)(D.lR,{children:"Duration (min)"}),(0,a.jsx)(D.MJ,{children:(0,a.jsx)(S.p,{type:"number",...t,onChange:e=>t.onChange(parseInt(e.target.value)||0)})}),(0,a.jsx)(D.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"appointment-form-dialog.tsx"}),(0,a.jsx)(D.zB,{control:O.control,name:"status",render:e=>{let{field:t}=e;return(0,a.jsxs)(D.eI,{children:[(0,a.jsx)(D.lR,{children:"Status"}),(0,a.jsxs)(T.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,a.jsx)(D.MJ,{children:(0,a.jsx)(T.bq,{children:(0,a.jsx)(T.yv,{})})}),(0,a.jsxs)(T.gC,{children:[(0,a.jsx)(T.eb,{value:"scheduled",children:"Scheduled"}),(0,a.jsx)(T.eb,{value:"completed",children:"Completed"}),(0,a.jsx)(T.eb,{value:"cancelled",children:"Cancelled"})]})]}),(0,a.jsx)(D.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"appointment-form-dialog.tsx"})]}),(0,a.jsx)(D.zB,{control:O.control,name:"practitionerId",render:e=>{let{field:t}=e;return(0,a.jsxs)(D.eI,{children:[(0,a.jsx)(D.lR,{children:"Practitioner"}),(0,a.jsxs)(T.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,a.jsx)(D.MJ,{children:(0,a.jsx)(T.bq,{children:(0,a.jsx)(T.yv,{placeholder:"Select practitioner"})})}),(0,a.jsx)(T.gC,{children:g.map(e=>(0,a.jsxs)(T.eb,{value:e.id,children:[e.firstName," ",e.lastName]},e.id))})]}),(0,a.jsx)(D.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"appointment-form-dialog.tsx"}),(0,a.jsxs)(C.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"appointment-form-dialog.tsx",children:[(0,a.jsx)(r.$,{type:"button",variant:"outline",onClick:()=>l(!1),disabled:c,"data-sentry-element":"Button","data-sentry-source-file":"appointment-form-dialog.tsx",children:"Cancel"}),(0,a.jsx)(r.$,{type:"submit",disabled:c,"data-sentry-element":"Button","data-sentry-source-file":"appointment-form-dialog.tsx",children:c?"Saving...":G?"Update":"Create"})]})]})})]})})}var O=n(14840),J=n(89944),q=n(23089),Y=n(93426);n(15686),n(70074);var W=n(86540),K=n(18183);let U={"zh-CN":$.g},Z=(0,O.Hz)({format:w.GP,parse:J.qg,startOfWeek:()=>(0,q.k)(new Date,{locale:$.g}),getDay:Y.P,locales:U});function X(e){let{onNewAppointment:t,onEditAppointment:n,onDeleteAppointment:i}=e,[l,o]=(0,s.useState)([]),[d,c]=(0,s.useState)(!0),[m,x]=(0,s.useState)("month"),[f,g]=(0,s.useState)(new Date),y=(0,s.useMemo)(()=>l.map(e=>{let t=(0,E.H)(e.appointmentDate),n=(0,V.z)(t,e.durationInMinutes),a=function(e){let t=e.patient.fullName,n="consultation"===e.appointmentType?"咨询":"治疗";if("treatment"===e.appointmentType&&e.treatment)return"".concat(t," - ").concat(e.treatment.name);if("consultation"===e.appointmentType){let n=e.consultationType?({initial:"初次咨询","follow-up":"复诊咨询","price-inquiry":"价格咨询"})[e.consultationType]:"咨询";return"".concat(t," - ").concat(n)}return"".concat(t," - ").concat(n)}(e);return{id:e.id,title:a,start:t,end:n,resource:e,status:e.status,appointmentType:e.appointmentType}}),[l]),j=(0,s.useCallback)(async()=>{try{c(!0);let e=await h.RG.getAll({limit:1e3});o(e.docs)}catch(e){console.error("Failed to fetch appointments:",e),_.error.fetchFailed()}finally{c(!1)}},[]);(0,s.useEffect)(()=>{j()},[j]);let v=(0,s.useCallback)(e=>{null==n||n(e.resource)},[n]),b=(0,s.useCallback)(e=>{let{start:n}=e;null==t||t()},[t]),N=(0,s.useCallback)(e=>({style:{backgroundColor:({scheduled:"#3b82f6",confirmed:"#10b981",completed:"#6b7280",cancelled:"#ef4444","no-show":"#f59e0b"})[e.status]||"#6b7280",borderColor:({consultation:"#8b5cf6",treatment:"#06b6d4"})[e.appointmentType]||"#6b7280",borderWidth:"2px",borderStyle:"solid",color:"white",fontSize:"12px",padding:"2px 4px"}}),[]);return(0,a.jsxs)(W.Zp,{className:"h-full","data-sentry-element":"Card","data-sentry-component":"AppointmentCalendar","data-sentry-source-file":"appointment-calendar.tsx",children:[(0,a.jsx)(W.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"appointment-calendar.tsx",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(W.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"appointment-calendar.tsx",children:[(0,a.jsx)(u.A,{className:"h-5 w-5","data-sentry-element":"IconCalendar","data-sentry-source-file":"appointment-calendar.tsx"}),"预约日历"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(r.$,{variant:"outline",size:"sm",onClick:j,disabled:d,"data-sentry-element":"Button","data-sentry-source-file":"appointment-calendar.tsx",children:[(0,a.jsx)(K.A,{className:"h-4 w-4 mr-2","data-sentry-element":"IconRefresh","data-sentry-source-file":"appointment-calendar.tsx"}),"刷新"]}),(0,a.jsxs)(r.$,{size:"sm",onClick:t,"data-sentry-element":"Button","data-sentry-source-file":"appointment-calendar.tsx",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2","data-sentry-element":"IconPlus","data-sentry-source-file":"appointment-calendar.tsx"}),"新建预约"]})]})]})}),(0,a.jsx)(W.Wu,{className:"p-4","data-sentry-element":"CardContent","data-sentry-source-file":"appointment-calendar.tsx",children:d?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(u.A,{className:"h-12 w-12 mx-auto mb-4 text-muted-foreground animate-pulse"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"加载预约数据中..."})]})}):(0,a.jsx)("div",{className:"h-[600px]",children:(0,a.jsx)(O.Vv,{localizer:Z,events:y,startAccessor:"start",endAccessor:"end",view:m,onView:x,date:f,onNavigate:g,onSelectEvent:v,onSelectSlot:b,selectable:!0,eventPropGetter:N,messages:L,culture:"zh-CN",formats:{monthHeaderFormat:"yyyy年MM月",dayHeaderFormat:"MM月dd日",dayRangeHeaderFormat:e=>{let{start:t,end:n}=e;return"".concat((0,w.GP)(t,"MM月dd日",{locale:$.g})," - ").concat((0,w.GP)(n,"MM月dd日",{locale:$.g}))},agendaDateFormat:"MM月dd日",agendaTimeFormat:"HH:mm",agendaTimeRangeFormat:e=>{let{start:t,end:n}=e;return"".concat((0,w.GP)(t,"HH:mm",{locale:$.g})," - ").concat((0,w.GP)(n,"HH:mm",{locale:$.g}))}},components:{month:{dateHeader:e=>{let{date:t,label:n}=e;return(0,a.jsx)("span",{className:"text-sm font-medium",children:n})}},week:{header:e=>{let{date:t,label:n}=e;return(0,a.jsx)("span",{className:"text-sm font-medium",children:n})}},event:e=>{let{event:t}=e;return(0,a.jsxs)("div",{className:"text-xs",children:[(0,a.jsx)("div",{className:"font-medium truncate",children:t.title}),(0,a.jsx)("div",{className:"opacity-75",children:"consultation"===t.appointmentType?"咨询":"治疗"})]})}}})})})]})}var Q=n(60171),ee=n(60541),et=n(37028),en=n(89280),ea=n(98721),es=n(3775);let ei={scheduled:{label:"已预约",color:"bg-blue-100 text-blue-800 hover:bg-blue-200",icon:et.A,nextStates:["confirmed","cancelled"]},confirmed:{label:"已确认",color:"bg-green-100 text-green-800 hover:bg-green-200",icon:en.A,nextStates:["completed","no-show","cancelled"]},completed:{label:"已完成",color:"bg-gray-100 text-gray-800 hover:bg-gray-200",icon:o.A,nextStates:[]},cancelled:{label:"已取消",color:"bg-red-100 text-red-800 hover:bg-red-200",icon:d.A,nextStates:["scheduled"]},"no-show":{label:"未到诊",color:"bg-amber-100 text-amber-800 hover:bg-amber-200",icon:ea.A,nextStates:["scheduled","cancelled"]}},er={scheduled:"预约",confirmed:"确认",completed:"完成",cancelled:"取消","no-show":"标记未到诊"};function el(e){let{appointment:t,onStatusChange:n,compact:o=!1}=e,{hasPermission:d}=(0,l.It)(),[c,m]=(0,s.useState)(!1),[u,p]=(0,s.useState)({open:!1,newStatus:"",title:"",description:""}),x=t.status,f=ei[x],g=(null==f?void 0:f.icon)||et.A,y=d("canEditAllAppointments"),j=async e=>{if(!y)return void _.error.permissionDenied("更改预约状态");if("cancelled"===e||"no-show"===e||"completed"===e){let n=er[e];p({open:!0,newStatus:e,title:"确认".concat(n,"预约"),description:"您确定要将 ".concat(t.patient.fullName,' 的预约状态更改为"').concat(ei[e].label,'"吗？')});return}await v(e)},v=async e=>{m(!0);try{let a=await h.RG.update(t.id,{status:e});_.appointment.statusChanged(a,x,e),null==n||n(a,e)}catch(e){console.error("Failed to update appointment status:",e),_.error.saveFailed(!0)}finally{m(!1),p({...u,open:!1})}},b=()=>{v(u.newStatus)};return o?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(i.E,{className:null==f?void 0:f.color,children:[(0,a.jsx)(g,{className:"h-3 w-3 mr-1"}),null==f?void 0:f.label]}),y&&(null==f?void 0:f.nextStates.length)>0&&(0,a.jsxs)(Q.rI,{children:[(0,a.jsx)(Q.ty,{asChild:!0,children:(0,a.jsx)(r.$,{variant:"ghost",size:"sm",disabled:c,className:"h-6 w-6 p-0",children:(0,a.jsx)(es.A,{className:"h-3 w-3"})})}),(0,a.jsx)(Q.SQ,{align:"end",children:f.nextStates.map(e=>{let t=ei[e].icon;return(0,a.jsxs)(Q._2,{onClick:()=>j(e),children:[(0,a.jsx)(t,{className:"h-4 w-4 mr-2"}),er[e]]},e)})})]}),(0,a.jsx)(ee.Lt,{open:u.open,onOpenChange:e=>p({...u,open:e}),children:(0,a.jsxs)(ee.EO,{children:[(0,a.jsxs)(ee.wd,{children:[(0,a.jsx)(ee.r7,{children:u.title}),(0,a.jsx)(ee.$v,{children:u.description})]}),(0,a.jsxs)(ee.ck,{children:[(0,a.jsx)(ee.Zr,{children:"取消"}),(0,a.jsx)(ee.Rx,{onClick:b,disabled:c,children:"确认"})]})]})})]}):(0,a.jsxs)("div",{className:"flex items-center gap-3","data-sentry-component":"AppointmentStatusManager","data-sentry-source-file":"appointment-status-manager.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(i.E,{className:null==f?void 0:f.color,"data-sentry-element":"Badge","data-sentry-source-file":"appointment-status-manager.tsx",children:[(0,a.jsx)(g,{className:"h-4 w-4 mr-2","data-sentry-element":"StatusIcon","data-sentry-source-file":"appointment-status-manager.tsx"}),null==f?void 0:f.label]}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:["预约时间: ",new Date(t.appointmentDate).toLocaleString("zh-CN")]})]}),y&&(null==f?void 0:f.nextStates.length)>0&&(0,a.jsxs)(Q.rI,{children:[(0,a.jsx)(Q.ty,{asChild:!0,children:(0,a.jsxs)(r.$,{variant:"outline",size:"sm",disabled:c,children:["更改状态",(0,a.jsx)(es.A,{className:"h-4 w-4 ml-2"})]})}),(0,a.jsxs)(Q.SQ,{align:"end",children:[(0,a.jsx)("div",{className:"px-2 py-1.5 text-sm font-medium text-muted-foreground",children:"可用操作"}),(0,a.jsx)(Q.mB,{}),f.nextStates.map(e=>{let t=ei[e].icon;return(0,a.jsxs)(Q._2,{onClick:()=>j(e),children:[(0,a.jsx)(t,{className:"h-4 w-4 mr-2"}),er[e]]},e)})]})]}),(0,a.jsx)(ee.Lt,{open:u.open,onOpenChange:e=>p({...u,open:e}),"data-sentry-element":"AlertDialog","data-sentry-source-file":"appointment-status-manager.tsx",children:(0,a.jsxs)(ee.EO,{"data-sentry-element":"AlertDialogContent","data-sentry-source-file":"appointment-status-manager.tsx",children:[(0,a.jsxs)(ee.wd,{"data-sentry-element":"AlertDialogHeader","data-sentry-source-file":"appointment-status-manager.tsx",children:[(0,a.jsx)(ee.r7,{"data-sentry-element":"AlertDialogTitle","data-sentry-source-file":"appointment-status-manager.tsx",children:u.title}),(0,a.jsx)(ee.$v,{"data-sentry-element":"AlertDialogDescription","data-sentry-source-file":"appointment-status-manager.tsx",children:u.description})]}),(0,a.jsxs)(ee.ck,{"data-sentry-element":"AlertDialogFooter","data-sentry-source-file":"appointment-status-manager.tsx",children:[(0,a.jsx)(ee.Zr,{"data-sentry-element":"AlertDialogCancel","data-sentry-source-file":"appointment-status-manager.tsx",children:"取消"}),(0,a.jsx)(ee.Rx,{onClick:b,disabled:c,"data-sentry-element":"AlertDialogAction","data-sentry-source-file":"appointment-status-manager.tsx",children:"确认"})]})]})})]})}var eo=n(48748);function ed(e){let{className:t,...n}=e;return(0,a.jsx)(eo.bL,{"data-slot":"switch",className:(0,I.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...n,"data-sentry-element":"SwitchPrimitive.Root","data-sentry-component":"Switch","data-sentry-source-file":"switch.tsx",children:(0,a.jsx)(eo.zi,{"data-slot":"switch-thumb",className:(0,I.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0"),"data-sentry-element":"SwitchPrimitive.Thumb","data-sentry-source-file":"switch.tsx"})})}var ec=n(84692),em=n(40153),eu=n(29776),ep=n(90323),ex=n(68065),eh=n(83063);class ef{scheduleReminders(e,t){let n={...this.defaultSettings,...t};if(!n.enabled)return;this.clearReminders(e.id);let a=new Date(e.appointmentDate),s=new Date;n.reminderTimes.forEach(t=>{let i=new Date(a.getTime()-60*t*1e3);i>s&&n.methods.forEach(n=>{let a={id:"".concat(e.id,"-").concat(t,"-").concat(n),appointmentId:e.id,reminderTime:i,method:n,status:"pending",createdAt:new Date};this.scheduleReminder(a,e)})})}scheduleReminder(e,t){let n=new Date,a=e.reminderTime.getTime()-n.getTime();if(a<=0)return void this.sendReminder(e,t);let s=setTimeout(()=>{this.sendReminder(e,t)},a);this.timers.set(e.id,s);let i=this.reminders.get(t.id)||[];i.push(e),this.reminders.set(t.id,i),console.log("Scheduled reminder for appointment ".concat(t.id," at ").concat(e.reminderTime.toISOString()))}async sendReminder(e,t){try{let n=Math.floor((new Date(t.appointmentDate).getTime()-new Date().getTime())/6e4);switch(e.method){case"notification":this.sendNotificationReminder(t,n);break;case"email":await this.sendEmailReminder(t,n);break;case"sms":await this.sendSMSReminder(t,n)}e.status="sent",_.reminder.reminderSet(t,B(e.reminderTime))}catch(t){console.error("Failed to send reminder:",t),e.status="failed"}finally{let t=this.timers.get(e.id);t&&(clearTimeout(t),this.timers.delete(e.id))}}sendNotificationReminder(e,t){if("Notification"in window)if("granted"===Notification.permission){let n=new Notification("预约提醒",{body:"".concat(e.patient.fullName," 的预约将在 ").concat(t," 分钟后开始"),icon:"/favicon.ico",tag:"appointment-".concat(e.id)});setTimeout(()=>n.close(),1e4)}else"denied"!==Notification.permission&&Notification.requestPermission().then(n=>{"granted"===n&&this.sendNotificationReminder(e,t)});_.reminder.upcoming(e,t)}async sendEmailReminder(e,t){var n;console.log("Email reminder would be sent for appointment ".concat(e.id)),console.log("Email data:",{to:e.patient.email,subject:"预约提醒",body:"您好 ".concat(e.patient.fullName,"，您的预约将在 ").concat(t," 分钟后开始。"),appointmentDetails:{date:B(new Date(e.appointmentDate)),treatment:(null==(n=e.treatment)?void 0:n.name)||"咨询",practitioner:"".concat(e.practitioner.firstName," ").concat(e.practitioner.lastName)}})}async sendSMSReminder(e,t){console.log("SMS reminder would be sent for appointment ".concat(e.id)),console.log("SMS data:",{to:e.patient.phone,message:"预约提醒：".concat(e.patient.fullName,"，您的预约将在 ").concat(t," 分钟后开始。时间：").concat(B(new Date(e.appointmentDate)))})}clearReminders(e){(this.reminders.get(e)||[]).forEach(e=>{let t=this.timers.get(e.id);t&&(clearTimeout(t),this.timers.delete(e.id))}),this.reminders.delete(e),console.log("Cleared reminders for appointment ".concat(e))}getReminders(e){return this.reminders.get(e)||[]}checkOverdueAppointments(e){let t=new Date;e.forEach(e=>{new Date(e.appointmentDate)<t&&("scheduled"===e.status||"confirmed"===e.status)&&_.reminder.overdue(e)})}async requestNotificationPermission(){return"Notification"in window&&"granted"===await Notification.requestPermission()}isNotificationSupported(){return"Notification"in window&&"granted"===Notification.permission}updateSettings(e){this.defaultSettings={...this.defaultSettings,...e}}getSettings(){return{...this.defaultSettings}}constructor(){this.reminders=new Map,this.timers=new Map,this.defaultSettings={enabled:!0,reminderTimes:[60,15],methods:["notification"]}}}let eg=new ef;function ey(e){let{onSettingsChange:t}=e,[n,l]=(0,s.useState)(!1),[o,d]=(0,s.useState)(eg.getSettings()),[c,u]=(0,s.useState)(""),[x,h]=(0,s.useState)(eg.isNotificationSupported());(0,s.useEffect)(()=>{h(eg.isNotificationSupported())},[]);let f=e=>{let n={...o,...e};d(n),eg.updateSettings(n),null==t||t(n)},g=e=>{f({reminderTimes:o.reminderTimes.filter(t=>t!==e)}),F.toast.success("提醒时间删除成功")},y=(e,t)=>{let n=[...o.methods];t?n.includes(e)||n.push(e):n=n.filter(t=>t!==e),f({methods:n})},j=async()=>{let e=await eg.requestNotificationPermission();h(e),e?F.toast.success("通知权限已授予"):F.toast.error("通知权限被拒绝")},v=e=>{if(e<60)return"".concat(e," 分钟前");{let t=Math.floor(e/60),n=e%60;return 0===n?"".concat(t," 小时前"):"".concat(t," 小时 ").concat(n," 分钟前")}};return(0,a.jsxs)(C.lG,{open:n,onOpenChange:l,"data-sentry-element":"Dialog","data-sentry-component":"AppointmentReminderSettings","data-sentry-source-file":"appointment-reminder-settings.tsx",children:[(0,a.jsx)(C.zM,{asChild:!0,"data-sentry-element":"DialogTrigger","data-sentry-source-file":"appointment-reminder-settings.tsx",children:(0,a.jsxs)(r.$,{variant:"outline",size:"sm","data-sentry-element":"Button","data-sentry-source-file":"appointment-reminder-settings.tsx",children:[(0,a.jsx)(eu.A,{className:"h-4 w-4 mr-2","data-sentry-element":"IconSettings","data-sentry-source-file":"appointment-reminder-settings.tsx"}),"提醒设置"]})}),(0,a.jsxs)(C.Cf,{className:"max-w-md","data-sentry-element":"DialogContent","data-sentry-source-file":"appointment-reminder-settings.tsx",children:[(0,a.jsxs)(C.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"appointment-reminder-settings.tsx",children:[(0,a.jsxs)(C.L3,{className:"flex items-center gap-2","data-sentry-element":"DialogTitle","data-sentry-source-file":"appointment-reminder-settings.tsx",children:[(0,a.jsx)(ep.A,{className:"h-5 w-5","data-sentry-element":"IconBell","data-sentry-source-file":"appointment-reminder-settings.tsx"}),"预约提醒设置"]}),(0,a.jsx)(C.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"appointment-reminder-settings.tsx",children:"配置预约提醒的时间和方式"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(ec.J,{htmlFor:"enable-reminders","data-sentry-element":"Label","data-sentry-source-file":"appointment-reminder-settings.tsx",children:"启用预约提醒"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"自动发送预约提醒通知"})]}),(0,a.jsx)(ed,{id:"enable-reminders",checked:o.enabled,onCheckedChange:e=>f({enabled:e}),"data-sentry-element":"Switch","data-sentry-source-file":"appointment-reminder-settings.tsx"})]}),o.enabled&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(ec.J,{children:"提醒时间"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:o.reminderTimes.map(e=>(0,a.jsxs)(i.E,{variant:"secondary",className:"flex items-center gap-1",children:[v(e),(0,a.jsx)(r.$,{variant:"ghost",size:"sm",className:"h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground",onClick:()=>g(e),children:(0,a.jsx)(m.A,{className:"h-3 w-3"})})]},e))}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(S.p,{type:"number",placeholder:"分钟",value:c,onChange:e=>u(e.target.value),className:"flex-1",min:"1",max:"1440"}),(0,a.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>{let e=parseInt(c);return isNaN(e)||e<=0?void F.toast.error("请输入有效的提醒时间（分钟）"):o.reminderTimes.includes(e)?void F.toast.error("该提醒时间已存在"):void(f({reminderTimes:[...o.reminderTimes,e].sort((e,t)=>t-e)}),u(""),F.toast.success("提醒时间添加成功"))},disabled:!c,children:(0,a.jsx)(p.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(ec.J,{children:"提醒方式"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(ep.A,{className:"h-4 w-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)(ec.J,{htmlFor:"method-notification",children:"浏览器通知"}),!x&&(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"需要授权通知权限"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[!x&&(0,a.jsx)(r.$,{variant:"outline",size:"sm",onClick:j,children:"授权"}),(0,a.jsx)(em.S,{id:"method-notification",checked:o.methods.includes("notification"),onCheckedChange:e=>y("notification",e),disabled:!x})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(ex.A,{className:"h-4 w-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)(ec.J,{htmlFor:"method-email",children:"邮件提醒"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"即将推出"})]})]}),(0,a.jsx)(em.S,{id:"method-email",checked:o.methods.includes("email"),onCheckedChange:e=>y("email",e),disabled:!0})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(eh.A,{className:"h-4 w-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)(ec.J,{htmlFor:"method-sms",children:"短信提醒"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"即将推出"})]})]}),(0,a.jsx)(em.S,{id:"method-sms",checked:o.methods.includes("sms"),onCheckedChange:e=>y("sms",e),disabled:!0})]})]})]})]})]}),(0,a.jsx)(C.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"appointment-reminder-settings.tsx",children:(0,a.jsx)(r.$,{onClick:()=>l(!1),"data-sentry-element":"Button","data-sentry-source-file":"appointment-reminder-settings.tsx",children:"完成"})})]})]})}var ej=n(65674);function ev(e){let{filters:t,onFiltersChange:n}=e,[i,l]=(0,s.useState)(!1),[o,d]=(0,s.useState)(!1),c=(e,a)=>{n({...t,[e]:a})},m=t.search||t.status&&"all"!==t.status||t.dateFrom||t.dateTo;return(0,a.jsxs)("div",{className:"space-y-4 p-4 border rounded-lg bg-muted/50","data-sentry-component":"AppointmentFiltersComponent","data-sentry-source-file":"appointment-filters.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-sm font-medium",children:"筛选"}),m&&(0,a.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>{n({search:"",status:"all",dateFrom:void 0,dateTo:void 0})},children:"清除全部"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(ec.J,{htmlFor:"search","data-sentry-element":"Label","data-sentry-source-file":"appointment-filters.tsx",children:"搜索"}),(0,a.jsx)(S.p,{id:"search",placeholder:"患者姓名、治疗项目...",value:t.search,onChange:e=>c("search",e.target.value),"data-sentry-element":"Input","data-sentry-source-file":"appointment-filters.tsx"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(ec.J,{"data-sentry-element":"Label","data-sentry-source-file":"appointment-filters.tsx",children:"状态"}),(0,a.jsxs)(T.l6,{value:t.status,onValueChange:e=>c("status",e),"data-sentry-element":"Select","data-sentry-source-file":"appointment-filters.tsx",children:[(0,a.jsx)(T.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"appointment-filters.tsx",children:(0,a.jsx)(T.yv,{placeholder:"所有状态","data-sentry-element":"SelectValue","data-sentry-source-file":"appointment-filters.tsx"})}),(0,a.jsxs)(T.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"appointment-filters.tsx",children:[(0,a.jsx)(T.eb,{value:"all","data-sentry-element":"SelectItem","data-sentry-source-file":"appointment-filters.tsx",children:"所有状态"}),(0,a.jsx)(T.eb,{value:"scheduled","data-sentry-element":"SelectItem","data-sentry-source-file":"appointment-filters.tsx",children:"已安排"}),(0,a.jsx)(T.eb,{value:"completed","data-sentry-element":"SelectItem","data-sentry-source-file":"appointment-filters.tsx",children:"已完成"}),(0,a.jsx)(T.eb,{value:"cancelled","data-sentry-element":"SelectItem","data-sentry-source-file":"appointment-filters.tsx",children:"已取消"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(ec.J,{"data-sentry-element":"Label","data-sentry-source-file":"appointment-filters.tsx",children:"开始日期"}),(0,a.jsxs)(k.AM,{open:i,onOpenChange:l,"data-sentry-element":"Popover","data-sentry-source-file":"appointment-filters.tsx",children:[(0,a.jsx)(k.Wv,{asChild:!0,"data-sentry-element":"PopoverTrigger","data-sentry-source-file":"appointment-filters.tsx",children:(0,a.jsxs)(r.$,{variant:"outline",className:(0,I.cn)("w-full justify-start text-left font-normal",!t.dateFrom&&"text-muted-foreground"),"data-sentry-element":"Button","data-sentry-source-file":"appointment-filters.tsx",children:[(0,a.jsx)(N.A,{className:"mr-2 h-4 w-4","data-sentry-element":"CalendarIcon","data-sentry-source-file":"appointment-filters.tsx"}),t.dateFrom?(0,w.GP)(t.dateFrom,"PPP"):(0,a.jsx)("span",{children:"选择日期"})]})}),(0,a.jsx)(k.hl,{className:"w-auto p-0",align:"start","data-sentry-element":"PopoverContent","data-sentry-source-file":"appointment-filters.tsx",children:(0,a.jsx)(A.V,{mode:"single",selected:t.dateFrom,onSelect:e=>{c("dateFrom",e),l(!1)},initialFocus:!0,"data-sentry-element":"Calendar","data-sentry-source-file":"appointment-filters.tsx"})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(ec.J,{"data-sentry-element":"Label","data-sentry-source-file":"appointment-filters.tsx",children:"结束日期"}),(0,a.jsxs)(k.AM,{open:o,onOpenChange:d,"data-sentry-element":"Popover","data-sentry-source-file":"appointment-filters.tsx",children:[(0,a.jsx)(k.Wv,{asChild:!0,"data-sentry-element":"PopoverTrigger","data-sentry-source-file":"appointment-filters.tsx",children:(0,a.jsxs)(r.$,{variant:"outline",className:(0,I.cn)("w-full justify-start text-left font-normal",!t.dateTo&&"text-muted-foreground"),"data-sentry-element":"Button","data-sentry-source-file":"appointment-filters.tsx",children:[(0,a.jsx)(N.A,{className:"mr-2 h-4 w-4","data-sentry-element":"CalendarIcon","data-sentry-source-file":"appointment-filters.tsx"}),t.dateTo?(0,w.GP)(t.dateTo,"PPP"):(0,a.jsx)("span",{children:"选择日期"})]})}),(0,a.jsx)(k.hl,{className:"w-auto p-0",align:"start","data-sentry-element":"PopoverContent","data-sentry-source-file":"appointment-filters.tsx",children:(0,a.jsx)(A.V,{mode:"single",selected:t.dateTo,onSelect:e=>{c("dateTo",e),d(!1)},disabled:e=>!!t.dateFrom&&e<t.dateFrom,initialFocus:!0,"data-sentry-element":"Calendar","data-sentry-source-file":"appointment-filters.tsx"})})]})]})]})]})}var eb=n(42278),ew=n(26230);let eN=(e,t,n)=>[{accessorKey:"patient",header:"患者",cell:e=>{let{row:t}=e,n=t.getValue("patient");return(null==n?void 0:n.fullName)||"未知患者"}},{accessorKey:"treatment",header:"治疗项目",cell:e=>{let{row:t}=e,n=t.getValue("treatment");return(null==n?void 0:n.name)||"未知治疗"}},{accessorKey:"appointmentDate",header:"日期时间",cell:e=>{let{row:t}=e,n=new Date(t.getValue("appointmentDate"));return(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{children:n.toLocaleDateString()}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:n.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]})}},{accessorKey:"price",header:"价格",cell:e=>{let{row:t}=e,n=t.getValue("price");return"\xa5".concat(n)}},{accessorKey:"status",header:"状态",cell:e=>{let{row:t}=e,n=t.original;return(0,a.jsx)(el,{appointment:n,onStatusChange:e=>{setAppointments(t=>t.map(t=>t.id===e.id?e:t))},compact:!0})}},{id:"actions",header:"操作",cell:s=>{let{row:i}=s,l=i.original;return(0,a.jsxs)("div",{className:"flex items-center gap-2",children:["scheduled"===l.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>n(l,"completed"),className:"text-green-600 hover:text-green-700",children:(0,a.jsx)(o.A,{className:"h-4 w-4"})}),(0,a.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>n(l,"cancelled"),className:"text-red-600 hover:text-red-700",children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)(Q.rI,{children:[(0,a.jsx)(Q.ty,{asChild:!0,children:(0,a.jsx)(r.$,{variant:"outline",size:"sm",children:(0,a.jsx)(c.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(Q.SQ,{children:[(0,a.jsxs)(Q._2,{onClick:()=>e(l),children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2"}),(0,ew.t)("common.actions.edit")]}),(0,a.jsxs)(Q._2,{onClick:()=>t(l),className:"text-red-600",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),(0,ew.t)("common.actions.delete")]})]})]})]})}}];function eC(){let{hasPermission:e,user:t}=(0,l.It)(),[n,i]=(0,s.useState)([]),[o,d]=(0,s.useState)([]),[c,m]=(0,s.useState)(!0),[j,v]=(0,s.useState)(null),[b,w]=(0,s.useState)(!1),[N,C]=(0,s.useState)(),[D,S]=(0,s.useState)(!1),[T,k]=(0,s.useState)(),[A,I]=(0,s.useState)(!1),[M,P]=(0,s.useState)({search:"",status:"all",dateFrom:void 0,dateTo:void 0}),[R,z]=(0,s.useState)("table"),E=async()=>{try{m(!0),v(null);let e=await h.RG.getAll({limit:100});i(e.docs)}catch(e){console.error("Failed to fetch appointments:",e),v("Failed to load appointments. Please try again later.")}finally{m(!1)}};(0,s.useEffect)(()=>{let e=[...n];if(M.search){let t=M.search.toLowerCase();e=e.filter(e=>e.patient.fullName.toLowerCase().includes(t)||e.treatment.name.toLowerCase().includes(t))}if(M.status&&"all"!==M.status&&(e=e.filter(e=>e.status===M.status)),M.dateFrom&&(e=e.filter(e=>new Date(e.appointmentDate)>=M.dateFrom)),M.dateTo){let t=new Date(M.dateTo);t.setHours(23,59,59,999),e=e.filter(e=>new Date(e.appointmentDate)<=t)}d(e)},[n,M]),(0,s.useEffect)(()=>{E()},[]);let V=async()=>{if(T){I(!0);try{await h.RG.delete(T.id),F.toast.success("Appointment deleted successfully"),S(!1),k(void 0),E()}catch(e){console.error("Failed to delete appointment:",e),F.toast.error("Failed to delete appointment")}finally{I(!1)}}},$=()=>{w(!1),C(void 0),E()},B=()=>{C(void 0),w(!0)},L=eN(e=>{C(e),w(!0)},e=>{k(e),S(!0)},async(e,t)=>{I(!0);try{await h.RG.update(e.id,{status:t}),F.toast.success("Appointment ".concat(t," successfully")),E()}catch(e){console.error("Failed to update appointment status:",e),F.toast.error("Failed to update appointment status")}finally{I(!1)}}),{table:_}=(0,y.u)({data:o,columns:L,pageCount:Math.ceil(o.length/10),shallow:!0,debounceMs:500});return c?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:(0,ew.t)("appointments.loadingAppointments")})]})}):j?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-red-600 mb-4",children:j}),(0,a.jsx)(r.$,{onClick:E,variant:"outline",children:"重试"})]})}):0===n.length?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(u.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-2",children:(0,ew.t)("appointments.noAppointments")}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:"开始安排您的第一个预约。"}),(0,a.jsx)(l.Bk,{permission:"canCreateAppointments",children:(0,a.jsxs)(r.$,{onClick:B,children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),(0,ew.t)("appointments.newAppointment")]})})]})}),(0,a.jsx)(G,{open:b,onOpenChange:w,appointment:N,onSuccess:$})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium",children:[o.length," / ",n.length," ",(0,ew.t)("appointments.appointmentsCount")]}),(0,a.jsx)(eb.tU,{value:R,onValueChange:e=>z(e),"data-sentry-element":"Tabs","data-sentry-source-file":"appointments-list.tsx",children:(0,a.jsxs)(eb.j7,{className:"grid w-full grid-cols-2","data-sentry-element":"TabsList","data-sentry-source-file":"appointments-list.tsx",children:[(0,a.jsxs)(eb.Xi,{value:"table",className:"flex items-center gap-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"appointments-list.tsx",children:[(0,a.jsx)(x.A,{className:"h-4 w-4","data-sentry-element":"IconTable","data-sentry-source-file":"appointments-list.tsx"}),"表格视图"]}),(0,a.jsxs)(eb.Xi,{value:"calendar",className:"flex items-center gap-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"appointments-list.tsx",children:[(0,a.jsx)(u.A,{className:"h-4 w-4","data-sentry-element":"IconCalendar","data-sentry-source-file":"appointments-list.tsx"}),"日历视图"]})]})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(ey,{"data-sentry-element":"AppointmentReminderSettings","data-sentry-source-file":"appointments-list.tsx"}),(0,a.jsx)(l.Bk,{permission:"canCreateAppointments","data-sentry-element":"PermissionGate","data-sentry-source-file":"appointments-list.tsx",children:(0,a.jsxs)(r.$,{onClick:B,"data-sentry-element":"Button","data-sentry-source-file":"appointments-list.tsx",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2","data-sentry-element":"IconPlus","data-sentry-source-file":"appointments-list.tsx"}),(0,ew.t)("appointments.newAppointment")]})})]})]}),"table"===R&&(0,a.jsx)(ev,{filters:M,onFiltersChange:P}),"table"===R?(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(f.b,{table:_,children:(0,a.jsx)(g.w,{table:_})})}):(0,a.jsx)(X,{onNewAppointment:B,onEditAppointment:handleEditAppointment,onDeleteAppointment:handleDeleteAppointment}),0===o.length&&n.length>0&&(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"没有预约符合当前筛选条件。"})})]}),(0,a.jsx)(G,{open:b,onOpenChange:w,appointment:N,onSuccess:$,"data-sentry-element":"AppointmentFormDialog","data-sentry-source-file":"appointments-list.tsx"}),(0,a.jsx)(ej.K,{open:D,onOpenChange:S,title:"删除预约",description:"您确定要删除 ".concat(null==T?void 0:T.patient.fullName," 的预约吗？此操作无法撤销。"),confirmText:"删除",variant:"destructive",onConfirm:V,loading:A,"data-sentry-element":"ConfirmationDialog","data-sentry-source-file":"appointments-list.tsx"})]})}},42278:(e,t,n)=>{"use strict";n.d(t,{Xi:()=>o,av:()=>d,j7:()=>l,tU:()=>r});var a=n(52880);n(99004);var s=n(21747),i=n(54651);function r(e){let{className:t,...n}=e;return(0,a.jsx)(s.bL,{"data-slot":"tabs",className:(0,i.cn)("flex flex-col gap-2",t),...n,"data-sentry-element":"TabsPrimitive.Root","data-sentry-component":"Tabs","data-sentry-source-file":"tabs.tsx"})}function l(e){let{className:t,...n}=e;return(0,a.jsx)(s.B8,{"data-slot":"tabs-list",className:(0,i.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",t),...n,"data-sentry-element":"TabsPrimitive.List","data-sentry-component":"TabsList","data-sentry-source-file":"tabs.tsx"})}function o(e){let{className:t,...n}=e;return(0,a.jsx)(s.l9,{"data-slot":"tabs-trigger",className:(0,i.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n,"data-sentry-element":"TabsPrimitive.Trigger","data-sentry-component":"TabsTrigger","data-sentry-source-file":"tabs.tsx"})}function d(e){let{className:t,...n}=e;return(0,a.jsx)(s.UC,{"data-slot":"tabs-content",className:(0,i.cn)("flex-1 outline-none",t),...n,"data-sentry-element":"TabsPrimitive.Content","data-sentry-component":"TabsContent","data-sentry-source-file":"tabs.tsx"})}},70074:()=>{},86540:(e,t,n)=>{"use strict";n.d(t,{BT:()=>o,Wu:()=>c,X9:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>r,wL:()=>m});var a=n(52880);n(99004);var s=n(54651);function i(e){let{className:t,...n}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...n,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function r(e){let{className:t,...n}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...n,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...n}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...n,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...n}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...n,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...n}=e;return(0,a.jsx)("div",{"data-slot":"card-action",className:(0,s.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",t),...n,"data-sentry-component":"CardAction","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...n}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...n,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function m(e){let{className:t,...n}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...n,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}}},e=>{var t=t=>e(e.s=t);e.O(0,[7758,6550,1769,6677,7905,1359,4089,4629,7131,2090,3530,2350,290,3028,2826,8284,3979,1385,229,6821,3571,4873,9442,4579,9253,7358],()=>t(13946)),_N_E=e.O()}]);